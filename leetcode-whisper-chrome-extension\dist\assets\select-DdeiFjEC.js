function F0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function vh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var yh={exports:{}},$s={},wh={exports:{}},W={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var si=Symbol.for("react.element"),z0=Symbol.for("react.portal"),B0=Symbol.for("react.fragment"),$0=Symbol.for("react.strict_mode"),U0=Symbol.for("react.profiler"),W0=Symbol.for("react.provider"),H0=Symbol.for("react.context"),K0=Symbol.for("react.forward_ref"),G0=Symbol.for("react.suspense"),Y0=Symbol.for("react.memo"),X0=Symbol.for("react.lazy"),wf=Symbol.iterator;function Q0(e){return e===null||typeof e!="object"?null:(e=wf&&e[wf]||e["@@iterator"],typeof e=="function"?e:null)}var xh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Sh=Object.assign,Ch={};function $r(e,t,n){this.props=e,this.context=t,this.refs=Ch,this.updater=n||xh}$r.prototype.isReactComponent={};$r.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};$r.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ph(){}Ph.prototype=$r.prototype;function Lu(e,t,n){this.props=e,this.context=t,this.refs=Ch,this.updater=n||xh}var Nu=Lu.prototype=new Ph;Nu.constructor=Lu;Sh(Nu,$r.prototype);Nu.isPureReactComponent=!0;var xf=Array.isArray,Eh=Object.prototype.hasOwnProperty,Du={current:null},Th={key:!0,ref:!0,__self:!0,__source:!0};function kh(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Eh.call(t,r)&&!Th.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:si,type:e,key:i,ref:s,props:o,_owner:Du.current}}function Z0(e,t){return{$$typeof:si,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function _u(e){return typeof e=="object"&&e!==null&&e.$$typeof===si}function q0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Sf=/\/+/g;function vl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?q0(""+e.key):t.toString(36)}function $i(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case si:case z0:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+vl(s,0):r,xf(o)?(n="",e!=null&&(n=e.replace(Sf,"$&/")+"/"),$i(o,t,n,"",function(u){return u})):o!=null&&(_u(o)&&(o=Z0(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Sf,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",xf(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+vl(i,l);s+=$i(i,t,n,a,o)}else if(a=Q0(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+vl(i,l++),s+=$i(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function vi(e,t,n){if(e==null)return e;var r=[],o=0;return $i(e,r,"","",function(i){return t.call(n,i,o++)}),r}function J0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ie={current:null},Ui={transition:null},ew={ReactCurrentDispatcher:Ie,ReactCurrentBatchConfig:Ui,ReactCurrentOwner:Du};function Rh(){throw Error("act(...) is not supported in production builds of React.")}W.Children={map:vi,forEach:function(e,t,n){vi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return vi(e,function(){t++}),t},toArray:function(e){return vi(e,function(t){return t})||[]},only:function(e){if(!_u(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};W.Component=$r;W.Fragment=B0;W.Profiler=U0;W.PureComponent=Lu;W.StrictMode=$0;W.Suspense=G0;W.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ew;W.act=Rh;W.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Sh({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Du.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Eh.call(t,a)&&!Th.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:si,type:e.type,key:o,ref:i,props:r,_owner:s}};W.createContext=function(e){return e={$$typeof:H0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:W0,_context:e},e.Consumer=e};W.createElement=kh;W.createFactory=function(e){var t=kh.bind(null,e);return t.type=e,t};W.createRef=function(){return{current:null}};W.forwardRef=function(e){return{$$typeof:K0,render:e}};W.isValidElement=_u;W.lazy=function(e){return{$$typeof:X0,_payload:{_status:-1,_result:e},_init:J0}};W.memo=function(e,t){return{$$typeof:Y0,type:e,compare:t===void 0?null:t}};W.startTransition=function(e){var t=Ui.transition;Ui.transition={};try{e()}finally{Ui.transition=t}};W.unstable_act=Rh;W.useCallback=function(e,t){return Ie.current.useCallback(e,t)};W.useContext=function(e){return Ie.current.useContext(e)};W.useDebugValue=function(){};W.useDeferredValue=function(e){return Ie.current.useDeferredValue(e)};W.useEffect=function(e,t){return Ie.current.useEffect(e,t)};W.useId=function(){return Ie.current.useId()};W.useImperativeHandle=function(e,t,n){return Ie.current.useImperativeHandle(e,t,n)};W.useInsertionEffect=function(e,t){return Ie.current.useInsertionEffect(e,t)};W.useLayoutEffect=function(e,t){return Ie.current.useLayoutEffect(e,t)};W.useMemo=function(e,t){return Ie.current.useMemo(e,t)};W.useReducer=function(e,t,n){return Ie.current.useReducer(e,t,n)};W.useRef=function(e){return Ie.current.useRef(e)};W.useState=function(e){return Ie.current.useState(e)};W.useSyncExternalStore=function(e,t,n){return Ie.current.useSyncExternalStore(e,t,n)};W.useTransition=function(){return Ie.current.useTransition()};W.version="18.3.1";wh.exports=W;var y=wh.exports;const kt=vh(y),Ah=F0({__proto__:null,default:kt},[y]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tw=y,nw=Symbol.for("react.element"),rw=Symbol.for("react.fragment"),ow=Object.prototype.hasOwnProperty,iw=tw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,sw={key:!0,ref:!0,__self:!0,__source:!0};function Mh(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)ow.call(t,r)&&!sw.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:nw,type:e,key:i,ref:s,props:o,_owner:iw.current}}$s.Fragment=rw;$s.jsx=Mh;$s.jsxs=Mh;yh.exports=$s;var k=yh.exports,bh={exports:{}},et={},Lh={exports:{}},Nh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,b){var D=R.length;R.push(b);e:for(;0<D;){var B=D-1>>>1,re=R[B];if(0<o(re,b))R[B]=b,R[D]=re,D=B;else break e}}function n(R){return R.length===0?null:R[0]}function r(R){if(R.length===0)return null;var b=R[0],D=R.pop();if(D!==b){R[0]=D;e:for(var B=0,re=R.length,St=re>>>1;B<St;){var ke=2*(B+1)-1,Ct=R[ke],_e=ke+1,I=R[_e];if(0>o(Ct,D))_e<re&&0>o(I,Ct)?(R[B]=I,R[_e]=D,B=_e):(R[B]=Ct,R[ke]=D,B=ke);else if(_e<re&&0>o(I,D))R[B]=I,R[_e]=D,B=_e;else break e}}return b}function o(R,b){var D=R.sortIndex-b.sortIndex;return D!==0?D:R.id-b.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,d=3,v=!1,w=!1,m=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(R){for(var b=n(u);b!==null;){if(b.callback===null)r(u);else if(b.startTime<=R)r(u),b.sortIndex=b.expirationTime,t(a,b);else break;b=n(u)}}function S(R){if(m=!1,g(R),!w)if(n(a)!==null)w=!0,F(C);else{var b=n(u);b!==null&&O(S,b.startTime-R)}}function C(R,b){w=!1,m&&(m=!1,h(T),T=-1),v=!0;var D=d;try{for(g(b),f=n(a);f!==null&&(!(f.expirationTime>b)||R&&!j());){var B=f.callback;if(typeof B=="function"){f.callback=null,d=f.priorityLevel;var re=B(f.expirationTime<=b);b=e.unstable_now(),typeof re=="function"?f.callback=re:f===n(a)&&r(a),g(b)}else r(a);f=n(a)}if(f!==null)var St=!0;else{var ke=n(u);ke!==null&&O(S,ke.startTime-b),St=!1}return St}finally{f=null,d=D,v=!1}}var E=!1,P=null,T=-1,L=5,M=-1;function j(){return!(e.unstable_now()-M<L)}function _(){if(P!==null){var R=e.unstable_now();M=R;var b=!0;try{b=P(!0,R)}finally{b?U():(E=!1,P=null)}}else E=!1}var U;if(typeof p=="function")U=function(){p(_)};else if(typeof MessageChannel<"u"){var z=new MessageChannel,K=z.port2;z.port1.onmessage=_,U=function(){K.postMessage(null)}}else U=function(){x(_,0)};function F(R){P=R,E||(E=!0,U())}function O(R,b){T=x(function(){R(e.unstable_now())},b)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_continueExecution=function(){w||v||(w=!0,F(C))},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(R){switch(d){case 1:case 2:case 3:var b=3;break;default:b=d}var D=d;d=b;try{return R()}finally{d=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(R,b){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var D=d;d=R;try{return b()}finally{d=D}},e.unstable_scheduleCallback=function(R,b,D){var B=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?B+D:B):D=B,R){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=D+re,R={id:c++,callback:b,priorityLevel:R,startTime:D,expirationTime:re,sortIndex:-1},D>B?(R.sortIndex=D,t(u,R),n(a)===null&&R===n(u)&&(m?(h(T),T=-1):m=!0,O(S,D-B))):(R.sortIndex=re,t(a,R),w||v||(w=!0,F(C))),R},e.unstable_shouldYield=j,e.unstable_wrapCallback=function(R){var b=d;return function(){var D=d;d=b;try{return R.apply(this,arguments)}finally{d=D}}}})(Nh);Lh.exports=Nh;var lw=Lh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var aw=y,qe=lw;function A(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Dh=new Set,Oo={};function qn(e,t){Lr(e,t),Lr(e+"Capture",t)}function Lr(e,t){for(Oo[e]=t,e=0;e<t.length;e++)Dh.add(t[e])}var Ht=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),da=Object.prototype.hasOwnProperty,uw=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Cf={},Pf={};function cw(e){return da.call(Pf,e)?!0:da.call(Cf,e)?!1:uw.test(e)?Pf[e]=!0:(Cf[e]=!0,!1)}function fw(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function dw(e,t,n,r){if(t===null||typeof t>"u"||fw(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function je(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Ee={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ee[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ee[t]=new je(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ee[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ee[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ee[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ee[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ee[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ee[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ee[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ou=/[\-:]([a-z])/g;function Vu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ou,Vu);Ee[t]=new je(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ou,Vu);Ee[t]=new je(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ou,Vu);Ee[t]=new je(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ee[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});Ee.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ee[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function Iu(e,t,n,r){var o=Ee.hasOwnProperty(t)?Ee[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(dw(t,n,o,r)&&(n=null),r||o===null?cw(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var qt=aw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,yi=Symbol.for("react.element"),ir=Symbol.for("react.portal"),sr=Symbol.for("react.fragment"),ju=Symbol.for("react.strict_mode"),pa=Symbol.for("react.profiler"),_h=Symbol.for("react.provider"),Oh=Symbol.for("react.context"),Fu=Symbol.for("react.forward_ref"),ha=Symbol.for("react.suspense"),ma=Symbol.for("react.suspense_list"),zu=Symbol.for("react.memo"),on=Symbol.for("react.lazy"),Vh=Symbol.for("react.offscreen"),Ef=Symbol.iterator;function no(e){return e===null||typeof e!="object"?null:(e=Ef&&e[Ef]||e["@@iterator"],typeof e=="function"?e:null)}var ae=Object.assign,yl;function ho(e){if(yl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);yl=t&&t[1]||""}return`
`+yl+e}var wl=!1;function xl(e,t){if(!e||wl)return"";wl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{wl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ho(e):""}function pw(e){switch(e.tag){case 5:return ho(e.type);case 16:return ho("Lazy");case 13:return ho("Suspense");case 19:return ho("SuspenseList");case 0:case 2:case 15:return e=xl(e.type,!1),e;case 11:return e=xl(e.type.render,!1),e;case 1:return e=xl(e.type,!0),e;default:return""}}function ga(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case sr:return"Fragment";case ir:return"Portal";case pa:return"Profiler";case ju:return"StrictMode";case ha:return"Suspense";case ma:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Oh:return(e.displayName||"Context")+".Consumer";case _h:return(e._context.displayName||"Context")+".Provider";case Fu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case zu:return t=e.displayName||null,t!==null?t:ga(e.type)||"Memo";case on:t=e._payload,e=e._init;try{return ga(e(t))}catch{}}return null}function hw(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ga(t);case 8:return t===ju?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function wn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ih(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function mw(e){var t=Ih(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function wi(e){e._valueTracker||(e._valueTracker=mw(e))}function jh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ih(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ss(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function va(e,t){var n=t.checked;return ae({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Tf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=wn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Fh(e,t){t=t.checked,t!=null&&Iu(e,"checked",t,!1)}function ya(e,t){Fh(e,t);var n=wn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?wa(e,t.type,n):t.hasOwnProperty("defaultValue")&&wa(e,t.type,wn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function kf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function wa(e,t,n){(t!=="number"||ss(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var mo=Array.isArray;function Cr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+wn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function xa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(A(91));return ae({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Rf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(A(92));if(mo(n)){if(1<n.length)throw Error(A(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:wn(n)}}function zh(e,t){var n=wn(t.value),r=wn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Af(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Bh(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Sa(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Bh(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var xi,$h=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(xi=xi||document.createElement("div"),xi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=xi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Co={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},gw=["Webkit","ms","Moz","O"];Object.keys(Co).forEach(function(e){gw.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Co[t]=Co[e]})});function Uh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Co.hasOwnProperty(e)&&Co[e]?(""+t).trim():t+"px"}function Wh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Uh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var vw=ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ca(e,t){if(t){if(vw[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(A(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(A(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(A(61))}if(t.style!=null&&typeof t.style!="object")throw Error(A(62))}}function Pa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ea=null;function Bu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ta=null,Pr=null,Er=null;function Mf(e){if(e=ui(e)){if(typeof Ta!="function")throw Error(A(280));var t=e.stateNode;t&&(t=Gs(t),Ta(e.stateNode,e.type,t))}}function Hh(e){Pr?Er?Er.push(e):Er=[e]:Pr=e}function Kh(){if(Pr){var e=Pr,t=Er;if(Er=Pr=null,Mf(e),t)for(e=0;e<t.length;e++)Mf(t[e])}}function Gh(e,t){return e(t)}function Yh(){}var Sl=!1;function Xh(e,t,n){if(Sl)return e(t,n);Sl=!0;try{return Gh(e,t,n)}finally{Sl=!1,(Pr!==null||Er!==null)&&(Yh(),Kh())}}function Io(e,t){var n=e.stateNode;if(n===null)return null;var r=Gs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(A(231,t,typeof n));return n}var ka=!1;if(Ht)try{var ro={};Object.defineProperty(ro,"passive",{get:function(){ka=!0}}),window.addEventListener("test",ro,ro),window.removeEventListener("test",ro,ro)}catch{ka=!1}function yw(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Po=!1,ls=null,as=!1,Ra=null,ww={onError:function(e){Po=!0,ls=e}};function xw(e,t,n,r,o,i,s,l,a){Po=!1,ls=null,yw.apply(ww,arguments)}function Sw(e,t,n,r,o,i,s,l,a){if(xw.apply(this,arguments),Po){if(Po){var u=ls;Po=!1,ls=null}else throw Error(A(198));as||(as=!0,Ra=u)}}function Jn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Qh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function bf(e){if(Jn(e)!==e)throw Error(A(188))}function Cw(e){var t=e.alternate;if(!t){if(t=Jn(e),t===null)throw Error(A(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return bf(o),e;if(i===r)return bf(o),t;i=i.sibling}throw Error(A(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(A(189))}}if(n.alternate!==r)throw Error(A(190))}if(n.tag!==3)throw Error(A(188));return n.stateNode.current===n?e:t}function Zh(e){return e=Cw(e),e!==null?qh(e):null}function qh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=qh(e);if(t!==null)return t;e=e.sibling}return null}var Jh=qe.unstable_scheduleCallback,Lf=qe.unstable_cancelCallback,Pw=qe.unstable_shouldYield,Ew=qe.unstable_requestPaint,de=qe.unstable_now,Tw=qe.unstable_getCurrentPriorityLevel,$u=qe.unstable_ImmediatePriority,em=qe.unstable_UserBlockingPriority,us=qe.unstable_NormalPriority,kw=qe.unstable_LowPriority,tm=qe.unstable_IdlePriority,Us=null,bt=null;function Rw(e){if(bt&&typeof bt.onCommitFiberRoot=="function")try{bt.onCommitFiberRoot(Us,e,void 0,(e.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:bw,Aw=Math.log,Mw=Math.LN2;function bw(e){return e>>>=0,e===0?32:31-(Aw(e)/Mw|0)|0}var Si=64,Ci=4194304;function go(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function cs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=go(l):(i&=s,i!==0&&(r=go(i)))}else s=n&~o,s!==0?r=go(s):i!==0&&(r=go(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-gt(t),o=1<<n,r|=e[n],t&=~o;return r}function Lw(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nw(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-gt(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=Lw(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function Aa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function nm(){var e=Si;return Si<<=1,!(Si&4194240)&&(Si=64),e}function Cl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function li(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-gt(t),e[t]=n}function Dw(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-gt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Uu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-gt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var G=0;function rm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var om,Wu,im,sm,lm,Ma=!1,Pi=[],fn=null,dn=null,pn=null,jo=new Map,Fo=new Map,ln=[],_w="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Nf(e,t){switch(e){case"focusin":case"focusout":fn=null;break;case"dragenter":case"dragleave":dn=null;break;case"mouseover":case"mouseout":pn=null;break;case"pointerover":case"pointerout":jo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Fo.delete(t.pointerId)}}function oo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=ui(t),t!==null&&Wu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Ow(e,t,n,r,o){switch(t){case"focusin":return fn=oo(fn,e,t,n,r,o),!0;case"dragenter":return dn=oo(dn,e,t,n,r,o),!0;case"mouseover":return pn=oo(pn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return jo.set(i,oo(jo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Fo.set(i,oo(Fo.get(i)||null,e,t,n,r,o)),!0}return!1}function am(e){var t=Vn(e.target);if(t!==null){var n=Jn(t);if(n!==null){if(t=n.tag,t===13){if(t=Qh(n),t!==null){e.blockedOn=t,lm(e.priority,function(){im(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Wi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ba(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ea=r,n.target.dispatchEvent(r),Ea=null}else return t=ui(n),t!==null&&Wu(t),e.blockedOn=n,!1;t.shift()}return!0}function Df(e,t,n){Wi(e)&&n.delete(t)}function Vw(){Ma=!1,fn!==null&&Wi(fn)&&(fn=null),dn!==null&&Wi(dn)&&(dn=null),pn!==null&&Wi(pn)&&(pn=null),jo.forEach(Df),Fo.forEach(Df)}function io(e,t){e.blockedOn===t&&(e.blockedOn=null,Ma||(Ma=!0,qe.unstable_scheduleCallback(qe.unstable_NormalPriority,Vw)))}function zo(e){function t(o){return io(o,e)}if(0<Pi.length){io(Pi[0],e);for(var n=1;n<Pi.length;n++){var r=Pi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(fn!==null&&io(fn,e),dn!==null&&io(dn,e),pn!==null&&io(pn,e),jo.forEach(t),Fo.forEach(t),n=0;n<ln.length;n++)r=ln[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ln.length&&(n=ln[0],n.blockedOn===null);)am(n),n.blockedOn===null&&ln.shift()}var Tr=qt.ReactCurrentBatchConfig,fs=!0;function Iw(e,t,n,r){var o=G,i=Tr.transition;Tr.transition=null;try{G=1,Hu(e,t,n,r)}finally{G=o,Tr.transition=i}}function jw(e,t,n,r){var o=G,i=Tr.transition;Tr.transition=null;try{G=4,Hu(e,t,n,r)}finally{G=o,Tr.transition=i}}function Hu(e,t,n,r){if(fs){var o=ba(e,t,n,r);if(o===null)Nl(e,t,r,ds,n),Nf(e,r);else if(Ow(o,e,t,n,r))r.stopPropagation();else if(Nf(e,r),t&4&&-1<_w.indexOf(e)){for(;o!==null;){var i=ui(o);if(i!==null&&om(i),i=ba(e,t,n,r),i===null&&Nl(e,t,r,ds,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Nl(e,t,r,null,n)}}var ds=null;function ba(e,t,n,r){if(ds=null,e=Bu(r),e=Vn(e),e!==null)if(t=Jn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Qh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ds=e,null}function um(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Tw()){case $u:return 1;case em:return 4;case us:case kw:return 16;case tm:return 536870912;default:return 16}default:return 16}}var un=null,Ku=null,Hi=null;function cm(){if(Hi)return Hi;var e,t=Ku,n=t.length,r,o="value"in un?un.value:un.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Hi=o.slice(e,1<r?1-r:void 0)}function Ki(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ei(){return!0}function _f(){return!1}function tt(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ei:_f,this.isPropagationStopped=_f,this}return ae(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ei)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ei)},persist:function(){},isPersistent:Ei}),t}var Ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Gu=tt(Ur),ai=ae({},Ur,{view:0,detail:0}),Fw=tt(ai),Pl,El,so,Ws=ae({},ai,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Yu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==so&&(so&&e.type==="mousemove"?(Pl=e.screenX-so.screenX,El=e.screenY-so.screenY):El=Pl=0,so=e),Pl)},movementY:function(e){return"movementY"in e?e.movementY:El}}),Of=tt(Ws),zw=ae({},Ws,{dataTransfer:0}),Bw=tt(zw),$w=ae({},ai,{relatedTarget:0}),Tl=tt($w),Uw=ae({},Ur,{animationName:0,elapsedTime:0,pseudoElement:0}),Ww=tt(Uw),Hw=ae({},Ur,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Kw=tt(Hw),Gw=ae({},Ur,{data:0}),Vf=tt(Gw),Yw={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Xw={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Qw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Zw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Qw[e])?!!t[e]:!1}function Yu(){return Zw}var qw=ae({},ai,{key:function(e){if(e.key){var t=Yw[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ki(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Xw[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Yu,charCode:function(e){return e.type==="keypress"?Ki(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ki(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Jw=tt(qw),e1=ae({},Ws,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),If=tt(e1),t1=ae({},ai,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Yu}),n1=tt(t1),r1=ae({},Ur,{propertyName:0,elapsedTime:0,pseudoElement:0}),o1=tt(r1),i1=ae({},Ws,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),s1=tt(i1),l1=[9,13,27,32],Xu=Ht&&"CompositionEvent"in window,Eo=null;Ht&&"documentMode"in document&&(Eo=document.documentMode);var a1=Ht&&"TextEvent"in window&&!Eo,fm=Ht&&(!Xu||Eo&&8<Eo&&11>=Eo),jf=" ",Ff=!1;function dm(e,t){switch(e){case"keyup":return l1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function pm(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var lr=!1;function u1(e,t){switch(e){case"compositionend":return pm(t);case"keypress":return t.which!==32?null:(Ff=!0,jf);case"textInput":return e=t.data,e===jf&&Ff?null:e;default:return null}}function c1(e,t){if(lr)return e==="compositionend"||!Xu&&dm(e,t)?(e=cm(),Hi=Ku=un=null,lr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return fm&&t.locale!=="ko"?null:t.data;default:return null}}var f1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!f1[e.type]:t==="textarea"}function hm(e,t,n,r){Hh(r),t=ps(t,"onChange"),0<t.length&&(n=new Gu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var To=null,Bo=null;function d1(e){Tm(e,0)}function Hs(e){var t=cr(e);if(jh(t))return e}function p1(e,t){if(e==="change")return t}var mm=!1;if(Ht){var kl;if(Ht){var Rl="oninput"in document;if(!Rl){var Bf=document.createElement("div");Bf.setAttribute("oninput","return;"),Rl=typeof Bf.oninput=="function"}kl=Rl}else kl=!1;mm=kl&&(!document.documentMode||9<document.documentMode)}function $f(){To&&(To.detachEvent("onpropertychange",gm),Bo=To=null)}function gm(e){if(e.propertyName==="value"&&Hs(Bo)){var t=[];hm(t,Bo,e,Bu(e)),Xh(d1,t)}}function h1(e,t,n){e==="focusin"?($f(),To=t,Bo=n,To.attachEvent("onpropertychange",gm)):e==="focusout"&&$f()}function m1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Hs(Bo)}function g1(e,t){if(e==="click")return Hs(t)}function v1(e,t){if(e==="input"||e==="change")return Hs(t)}function y1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yt=typeof Object.is=="function"?Object.is:y1;function $o(e,t){if(yt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!da.call(t,o)||!yt(e[o],t[o]))return!1}return!0}function Uf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Wf(e,t){var n=Uf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Uf(n)}}function vm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ym(){for(var e=window,t=ss();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ss(e.document)}return t}function Qu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function w1(e){var t=ym(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&vm(n.ownerDocument.documentElement,n)){if(r!==null&&Qu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Wf(n,i);var s=Wf(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var x1=Ht&&"documentMode"in document&&11>=document.documentMode,ar=null,La=null,ko=null,Na=!1;function Hf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Na||ar==null||ar!==ss(r)||(r=ar,"selectionStart"in r&&Qu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ko&&$o(ko,r)||(ko=r,r=ps(La,"onSelect"),0<r.length&&(t=new Gu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=ar)))}function Ti(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:Ti("Animation","AnimationEnd"),animationiteration:Ti("Animation","AnimationIteration"),animationstart:Ti("Animation","AnimationStart"),transitionend:Ti("Transition","TransitionEnd")},Al={},wm={};Ht&&(wm=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);function Ks(e){if(Al[e])return Al[e];if(!ur[e])return e;var t=ur[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wm)return Al[e]=t[n];return e}var xm=Ks("animationend"),Sm=Ks("animationiteration"),Cm=Ks("animationstart"),Pm=Ks("transitionend"),Em=new Map,Kf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tn(e,t){Em.set(e,t),qn(t,[e])}for(var Ml=0;Ml<Kf.length;Ml++){var bl=Kf[Ml],S1=bl.toLowerCase(),C1=bl[0].toUpperCase()+bl.slice(1);Tn(S1,"on"+C1)}Tn(xm,"onAnimationEnd");Tn(Sm,"onAnimationIteration");Tn(Cm,"onAnimationStart");Tn("dblclick","onDoubleClick");Tn("focusin","onFocus");Tn("focusout","onBlur");Tn(Pm,"onTransitionEnd");Lr("onMouseEnter",["mouseout","mouseover"]);Lr("onMouseLeave",["mouseout","mouseover"]);Lr("onPointerEnter",["pointerout","pointerover"]);Lr("onPointerLeave",["pointerout","pointerover"]);qn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));qn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));qn("onBeforeInput",["compositionend","keypress","textInput","paste"]);qn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var vo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),P1=new Set("cancel close invalid load scroll toggle".split(" ").concat(vo));function Gf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Sw(r,t,void 0,e),e.currentTarget=null}function Tm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;Gf(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;Gf(o,l,u),i=a}}}if(as)throw e=Ra,as=!1,Ra=null,e}function ee(e,t){var n=t[Ia];n===void 0&&(n=t[Ia]=new Set);var r=e+"__bubble";n.has(r)||(km(t,e,2,!1),n.add(r))}function Ll(e,t,n){var r=0;t&&(r|=4),km(n,e,r,t)}var ki="_reactListening"+Math.random().toString(36).slice(2);function Uo(e){if(!e[ki]){e[ki]=!0,Dh.forEach(function(n){n!=="selectionchange"&&(P1.has(n)||Ll(n,!1,e),Ll(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ki]||(t[ki]=!0,Ll("selectionchange",!1,t))}}function km(e,t,n,r){switch(um(t)){case 1:var o=Iw;break;case 4:o=jw;break;default:o=Hu}n=o.bind(null,t,n,e),o=void 0,!ka||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Nl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=Vn(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}Xh(function(){var u=i,c=Bu(n),f=[];e:{var d=Em.get(e);if(d!==void 0){var v=Gu,w=e;switch(e){case"keypress":if(Ki(n)===0)break e;case"keydown":case"keyup":v=Jw;break;case"focusin":w="focus",v=Tl;break;case"focusout":w="blur",v=Tl;break;case"beforeblur":case"afterblur":v=Tl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Of;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=Bw;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=n1;break;case xm:case Sm:case Cm:v=Ww;break;case Pm:v=o1;break;case"scroll":v=Fw;break;case"wheel":v=s1;break;case"copy":case"cut":case"paste":v=Kw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=If}var m=(t&4)!==0,x=!m&&e==="scroll",h=m?d!==null?d+"Capture":null:d;m=[];for(var p=u,g;p!==null;){g=p;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,h!==null&&(S=Io(p,h),S!=null&&m.push(Wo(p,S,g)))),x)break;p=p.return}0<m.length&&(d=new v(d,w,null,n,c),f.push({event:d,listeners:m}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",d&&n!==Ea&&(w=n.relatedTarget||n.fromElement)&&(Vn(w)||w[Kt]))break e;if((v||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,v?(w=n.relatedTarget||n.toElement,v=u,w=w?Vn(w):null,w!==null&&(x=Jn(w),w!==x||w.tag!==5&&w.tag!==6)&&(w=null)):(v=null,w=u),v!==w)){if(m=Of,S="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(m=If,S="onPointerLeave",h="onPointerEnter",p="pointer"),x=v==null?d:cr(v),g=w==null?d:cr(w),d=new m(S,p+"leave",v,n,c),d.target=x,d.relatedTarget=g,S=null,Vn(c)===u&&(m=new m(h,p+"enter",w,n,c),m.target=g,m.relatedTarget=x,S=m),x=S,v&&w)t:{for(m=v,h=w,p=0,g=m;g;g=tr(g))p++;for(g=0,S=h;S;S=tr(S))g++;for(;0<p-g;)m=tr(m),p--;for(;0<g-p;)h=tr(h),g--;for(;p--;){if(m===h||h!==null&&m===h.alternate)break t;m=tr(m),h=tr(h)}m=null}else m=null;v!==null&&Yf(f,d,v,m,!1),w!==null&&x!==null&&Yf(f,x,w,m,!0)}}e:{if(d=u?cr(u):window,v=d.nodeName&&d.nodeName.toLowerCase(),v==="select"||v==="input"&&d.type==="file")var C=p1;else if(zf(d))if(mm)C=v1;else{C=m1;var E=h1}else(v=d.nodeName)&&v.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=g1);if(C&&(C=C(e,u))){hm(f,C,n,c);break e}E&&E(e,d,u),e==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&wa(d,"number",d.value)}switch(E=u?cr(u):window,e){case"focusin":(zf(E)||E.contentEditable==="true")&&(ar=E,La=u,ko=null);break;case"focusout":ko=La=ar=null;break;case"mousedown":Na=!0;break;case"contextmenu":case"mouseup":case"dragend":Na=!1,Hf(f,n,c);break;case"selectionchange":if(x1)break;case"keydown":case"keyup":Hf(f,n,c)}var P;if(Xu)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else lr?dm(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(fm&&n.locale!=="ko"&&(lr||T!=="onCompositionStart"?T==="onCompositionEnd"&&lr&&(P=cm()):(un=c,Ku="value"in un?un.value:un.textContent,lr=!0)),E=ps(u,T),0<E.length&&(T=new Vf(T,e,null,n,c),f.push({event:T,listeners:E}),P?T.data=P:(P=pm(n),P!==null&&(T.data=P)))),(P=a1?u1(e,n):c1(e,n))&&(u=ps(u,"onBeforeInput"),0<u.length&&(c=new Vf("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=P))}Tm(f,t)})}function Wo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ps(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Io(e,n),i!=null&&r.unshift(Wo(e,i,o)),i=Io(e,t),i!=null&&r.push(Wo(e,i,o))),e=e.return}return r}function tr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Yf(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=Io(n,i),a!=null&&s.unshift(Wo(n,a,l))):o||(a=Io(n,i),a!=null&&s.push(Wo(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var E1=/\r\n?/g,T1=/\u0000|\uFFFD/g;function Xf(e){return(typeof e=="string"?e:""+e).replace(E1,`
`).replace(T1,"")}function Ri(e,t,n){if(t=Xf(t),Xf(e)!==t&&n)throw Error(A(425))}function hs(){}var Da=null,_a=null;function Oa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Va=typeof setTimeout=="function"?setTimeout:void 0,k1=typeof clearTimeout=="function"?clearTimeout:void 0,Qf=typeof Promise=="function"?Promise:void 0,R1=typeof queueMicrotask=="function"?queueMicrotask:typeof Qf<"u"?function(e){return Qf.resolve(null).then(e).catch(A1)}:Va;function A1(e){setTimeout(function(){throw e})}function Dl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),zo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);zo(t)}function hn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Zf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Wr=Math.random().toString(36).slice(2),Rt="__reactFiber$"+Wr,Ho="__reactProps$"+Wr,Kt="__reactContainer$"+Wr,Ia="__reactEvents$"+Wr,M1="__reactListeners$"+Wr,b1="__reactHandles$"+Wr;function Vn(e){var t=e[Rt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Kt]||n[Rt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Zf(e);e!==null;){if(n=e[Rt])return n;e=Zf(e)}return t}e=n,n=e.parentNode}return null}function ui(e){return e=e[Rt]||e[Kt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function cr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(A(33))}function Gs(e){return e[Ho]||null}var ja=[],fr=-1;function kn(e){return{current:e}}function te(e){0>fr||(e.current=ja[fr],ja[fr]=null,fr--)}function Z(e,t){fr++,ja[fr]=e.current,e.current=t}var xn={},De=kn(xn),Be=kn(!1),Un=xn;function Nr(e,t){var n=e.type.contextTypes;if(!n)return xn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function $e(e){return e=e.childContextTypes,e!=null}function ms(){te(Be),te(De)}function qf(e,t,n){if(De.current!==xn)throw Error(A(168));Z(De,t),Z(Be,n)}function Rm(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(A(108,hw(e)||"Unknown",o));return ae({},n,r)}function gs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||xn,Un=De.current,Z(De,e),Z(Be,Be.current),!0}function Jf(e,t,n){var r=e.stateNode;if(!r)throw Error(A(169));n?(e=Rm(e,t,Un),r.__reactInternalMemoizedMergedChildContext=e,te(Be),te(De),Z(De,e)):te(Be),Z(Be,n)}var Ft=null,Ys=!1,_l=!1;function Am(e){Ft===null?Ft=[e]:Ft.push(e)}function L1(e){Ys=!0,Am(e)}function Rn(){if(!_l&&Ft!==null){_l=!0;var e=0,t=G;try{var n=Ft;for(G=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ft=null,Ys=!1}catch(o){throw Ft!==null&&(Ft=Ft.slice(e+1)),Jh($u,Rn),o}finally{G=t,_l=!1}}return null}var dr=[],pr=0,vs=null,ys=0,ot=[],it=0,Wn=null,zt=1,Bt="";function Nn(e,t){dr[pr++]=ys,dr[pr++]=vs,vs=e,ys=t}function Mm(e,t,n){ot[it++]=zt,ot[it++]=Bt,ot[it++]=Wn,Wn=e;var r=zt;e=Bt;var o=32-gt(r)-1;r&=~(1<<o),n+=1;var i=32-gt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,zt=1<<32-gt(t)+o|n<<o|r,Bt=i+e}else zt=1<<i|n<<o|r,Bt=e}function Zu(e){e.return!==null&&(Nn(e,1),Mm(e,1,0))}function qu(e){for(;e===vs;)vs=dr[--pr],dr[pr]=null,ys=dr[--pr],dr[pr]=null;for(;e===Wn;)Wn=ot[--it],ot[it]=null,Bt=ot[--it],ot[it]=null,zt=ot[--it],ot[it]=null}var Xe=null,Ye=null,oe=!1,mt=null;function bm(e,t){var n=st(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ed(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Xe=e,Ye=hn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Xe=e,Ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Wn!==null?{id:zt,overflow:Bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=st(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Xe=e,Ye=null,!0):!1;default:return!1}}function Fa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function za(e){if(oe){var t=Ye;if(t){var n=t;if(!ed(e,t)){if(Fa(e))throw Error(A(418));t=hn(n.nextSibling);var r=Xe;t&&ed(e,t)?bm(r,n):(e.flags=e.flags&-4097|2,oe=!1,Xe=e)}}else{if(Fa(e))throw Error(A(418));e.flags=e.flags&-4097|2,oe=!1,Xe=e}}}function td(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Xe=e}function Ai(e){if(e!==Xe)return!1;if(!oe)return td(e),oe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Oa(e.type,e.memoizedProps)),t&&(t=Ye)){if(Fa(e))throw Lm(),Error(A(418));for(;t;)bm(e,t),t=hn(t.nextSibling)}if(td(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(A(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ye=hn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ye=null}}else Ye=Xe?hn(e.stateNode.nextSibling):null;return!0}function Lm(){for(var e=Ye;e;)e=hn(e.nextSibling)}function Dr(){Ye=Xe=null,oe=!1}function Ju(e){mt===null?mt=[e]:mt.push(e)}var N1=qt.ReactCurrentBatchConfig;function lo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(A(309));var r=n.stateNode}if(!r)throw Error(A(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(A(284));if(!n._owner)throw Error(A(290,e))}return e}function Mi(e,t){throw e=Object.prototype.toString.call(t),Error(A(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nd(e){var t=e._init;return t(e._payload)}function Nm(e){function t(h,p){if(e){var g=h.deletions;g===null?(h.deletions=[p],h.flags|=16):g.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=yn(h,p),h.index=0,h.sibling=null,h}function i(h,p,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<p?(h.flags|=2,p):g):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,p,g,S){return p===null||p.tag!==6?(p=Bl(g,h.mode,S),p.return=h,p):(p=o(p,g),p.return=h,p)}function a(h,p,g,S){var C=g.type;return C===sr?c(h,p,g.props.children,S,g.key):p!==null&&(p.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===on&&nd(C)===p.type)?(S=o(p,g.props),S.ref=lo(h,p,g),S.return=h,S):(S=Ji(g.type,g.key,g.props,null,h.mode,S),S.ref=lo(h,p,g),S.return=h,S)}function u(h,p,g,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=$l(g,h.mode,S),p.return=h,p):(p=o(p,g.children||[]),p.return=h,p)}function c(h,p,g,S,C){return p===null||p.tag!==7?(p=Bn(g,h.mode,S,C),p.return=h,p):(p=o(p,g),p.return=h,p)}function f(h,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Bl(""+p,h.mode,g),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case yi:return g=Ji(p.type,p.key,p.props,null,h.mode,g),g.ref=lo(h,null,p),g.return=h,g;case ir:return p=$l(p,h.mode,g),p.return=h,p;case on:var S=p._init;return f(h,S(p._payload),g)}if(mo(p)||no(p))return p=Bn(p,h.mode,g,null),p.return=h,p;Mi(h,p)}return null}function d(h,p,g,S){var C=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return C!==null?null:l(h,p,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case yi:return g.key===C?a(h,p,g,S):null;case ir:return g.key===C?u(h,p,g,S):null;case on:return C=g._init,d(h,p,C(g._payload),S)}if(mo(g)||no(g))return C!==null?null:c(h,p,g,S,null);Mi(h,g)}return null}function v(h,p,g,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(g)||null,l(p,h,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case yi:return h=h.get(S.key===null?g:S.key)||null,a(p,h,S,C);case ir:return h=h.get(S.key===null?g:S.key)||null,u(p,h,S,C);case on:var E=S._init;return v(h,p,g,E(S._payload),C)}if(mo(S)||no(S))return h=h.get(g)||null,c(p,h,S,C,null);Mi(p,S)}return null}function w(h,p,g,S){for(var C=null,E=null,P=p,T=p=0,L=null;P!==null&&T<g.length;T++){P.index>T?(L=P,P=null):L=P.sibling;var M=d(h,P,g[T],S);if(M===null){P===null&&(P=L);break}e&&P&&M.alternate===null&&t(h,P),p=i(M,p,T),E===null?C=M:E.sibling=M,E=M,P=L}if(T===g.length)return n(h,P),oe&&Nn(h,T),C;if(P===null){for(;T<g.length;T++)P=f(h,g[T],S),P!==null&&(p=i(P,p,T),E===null?C=P:E.sibling=P,E=P);return oe&&Nn(h,T),C}for(P=r(h,P);T<g.length;T++)L=v(P,h,T,g[T],S),L!==null&&(e&&L.alternate!==null&&P.delete(L.key===null?T:L.key),p=i(L,p,T),E===null?C=L:E.sibling=L,E=L);return e&&P.forEach(function(j){return t(h,j)}),oe&&Nn(h,T),C}function m(h,p,g,S){var C=no(g);if(typeof C!="function")throw Error(A(150));if(g=C.call(g),g==null)throw Error(A(151));for(var E=C=null,P=p,T=p=0,L=null,M=g.next();P!==null&&!M.done;T++,M=g.next()){P.index>T?(L=P,P=null):L=P.sibling;var j=d(h,P,M.value,S);if(j===null){P===null&&(P=L);break}e&&P&&j.alternate===null&&t(h,P),p=i(j,p,T),E===null?C=j:E.sibling=j,E=j,P=L}if(M.done)return n(h,P),oe&&Nn(h,T),C;if(P===null){for(;!M.done;T++,M=g.next())M=f(h,M.value,S),M!==null&&(p=i(M,p,T),E===null?C=M:E.sibling=M,E=M);return oe&&Nn(h,T),C}for(P=r(h,P);!M.done;T++,M=g.next())M=v(P,h,T,M.value,S),M!==null&&(e&&M.alternate!==null&&P.delete(M.key===null?T:M.key),p=i(M,p,T),E===null?C=M:E.sibling=M,E=M);return e&&P.forEach(function(_){return t(h,_)}),oe&&Nn(h,T),C}function x(h,p,g,S){if(typeof g=="object"&&g!==null&&g.type===sr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case yi:e:{for(var C=g.key,E=p;E!==null;){if(E.key===C){if(C=g.type,C===sr){if(E.tag===7){n(h,E.sibling),p=o(E,g.props.children),p.return=h,h=p;break e}}else if(E.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===on&&nd(C)===E.type){n(h,E.sibling),p=o(E,g.props),p.ref=lo(h,E,g),p.return=h,h=p;break e}n(h,E);break}else t(h,E);E=E.sibling}g.type===sr?(p=Bn(g.props.children,h.mode,S,g.key),p.return=h,h=p):(S=Ji(g.type,g.key,g.props,null,h.mode,S),S.ref=lo(h,p,g),S.return=h,h=S)}return s(h);case ir:e:{for(E=g.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(h,p.sibling),p=o(p,g.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=$l(g,h.mode,S),p.return=h,h=p}return s(h);case on:return E=g._init,x(h,p,E(g._payload),S)}if(mo(g))return w(h,p,g,S);if(no(g))return m(h,p,g,S);Mi(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,g),p.return=h,h=p):(n(h,p),p=Bl(g,h.mode,S),p.return=h,h=p),s(h)):n(h,p)}return x}var _r=Nm(!0),Dm=Nm(!1),ws=kn(null),xs=null,hr=null,ec=null;function tc(){ec=hr=xs=null}function nc(e){var t=ws.current;te(ws),e._currentValue=t}function Ba(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function kr(e,t){xs=e,ec=hr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ze=!0),e.firstContext=null)}function at(e){var t=e._currentValue;if(ec!==e)if(e={context:e,memoizedValue:t,next:null},hr===null){if(xs===null)throw Error(A(308));hr=e,xs.dependencies={lanes:0,firstContext:e}}else hr=hr.next=e;return t}var In=null;function rc(e){In===null?In=[e]:In.push(e)}function _m(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,rc(t)):(n.next=o.next,o.next=n),t.interleaved=n,Gt(e,r)}function Gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var sn=!1;function oc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Om(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function $t(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function mn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,H&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Gt(e,n)}return o=r.interleaved,o===null?(t.next=t,rc(r)):(t.next=o.next,o.next=t),r.interleaved=t,Gt(e,n)}function Gi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Uu(e,n)}}function rd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ss(e,t,n,r){var o=e.updateQueue;sn=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(i!==null){var f=o.baseState;s=0,c=u=a=null,l=i;do{var d=l.lane,v=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:v,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,m=l;switch(d=t,v=n,m.tag){case 1:if(w=m.payload,typeof w=="function"){f=w.call(v,f,d);break e}f=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=m.payload,d=typeof w=="function"?w.call(v,f,d):w,d==null)break e;f=ae({},f,d);break e;case 2:sn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[l]:d.push(l))}else v={eventTime:v,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=v,a=f):c=c.next=v,s|=d;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;d=l,l=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(c===null&&(a=f),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Kn|=s,e.lanes=s,e.memoizedState=f}}function od(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(A(191,o));o.call(r)}}}var ci={},Lt=kn(ci),Ko=kn(ci),Go=kn(ci);function jn(e){if(e===ci)throw Error(A(174));return e}function ic(e,t){switch(Z(Go,t),Z(Ko,e),Z(Lt,ci),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Sa(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Sa(t,e)}te(Lt),Z(Lt,t)}function Or(){te(Lt),te(Ko),te(Go)}function Vm(e){jn(Go.current);var t=jn(Lt.current),n=Sa(t,e.type);t!==n&&(Z(Ko,e),Z(Lt,n))}function sc(e){Ko.current===e&&(te(Lt),te(Ko))}var ie=kn(0);function Cs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ol=[];function lc(){for(var e=0;e<Ol.length;e++)Ol[e]._workInProgressVersionPrimary=null;Ol.length=0}var Yi=qt.ReactCurrentDispatcher,Vl=qt.ReactCurrentBatchConfig,Hn=0,le=null,ge=null,ye=null,Ps=!1,Ro=!1,Yo=0,D1=0;function Ae(){throw Error(A(321))}function ac(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yt(e[n],t[n]))return!1;return!0}function uc(e,t,n,r,o,i){if(Hn=i,le=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Yi.current=e===null||e.memoizedState===null?I1:j1,e=n(r,o),Ro){i=0;do{if(Ro=!1,Yo=0,25<=i)throw Error(A(301));i+=1,ye=ge=null,t.updateQueue=null,Yi.current=F1,e=n(r,o)}while(Ro)}if(Yi.current=Es,t=ge!==null&&ge.next!==null,Hn=0,ye=ge=le=null,Ps=!1,t)throw Error(A(300));return e}function cc(){var e=Yo!==0;return Yo=0,e}function Tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?le.memoizedState=ye=e:ye=ye.next=e,ye}function ut(){if(ge===null){var e=le.alternate;e=e!==null?e.memoizedState:null}else e=ge.next;var t=ye===null?le.memoizedState:ye.next;if(t!==null)ye=t,ge=e;else{if(e===null)throw Error(A(310));ge=e,e={memoizedState:ge.memoizedState,baseState:ge.baseState,baseQueue:ge.baseQueue,queue:ge.queue,next:null},ye===null?le.memoizedState=ye=e:ye=ye.next=e}return ye}function Xo(e,t){return typeof t=="function"?t(e):t}function Il(e){var t=ut(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=ge,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var c=u.lane;if((Hn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,le.lanes|=c,Kn|=c}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,yt(r,t.memoizedState)||(ze=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,le.lanes|=i,Kn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function jl(e){var t=ut(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);yt(i,t.memoizedState)||(ze=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Im(){}function jm(e,t){var n=le,r=ut(),o=t(),i=!yt(r.memoizedState,o);if(i&&(r.memoizedState=o,ze=!0),r=r.queue,fc(Bm.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ye!==null&&ye.memoizedState.tag&1){if(n.flags|=2048,Qo(9,zm.bind(null,n,r,o,t),void 0,null),xe===null)throw Error(A(349));Hn&30||Fm(n,t,o)}return o}function Fm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=le.updateQueue,t===null?(t={lastEffect:null,stores:null},le.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function zm(e,t,n,r){t.value=n,t.getSnapshot=r,$m(t)&&Um(e)}function Bm(e,t,n){return n(function(){$m(t)&&Um(e)})}function $m(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yt(e,n)}catch{return!0}}function Um(e){var t=Gt(e,1);t!==null&&vt(t,e,1,-1)}function id(e){var t=Tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Xo,lastRenderedState:e},t.queue=e,e=e.dispatch=V1.bind(null,le,e),[t.memoizedState,e]}function Qo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=le.updateQueue,t===null?(t={lastEffect:null,stores:null},le.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wm(){return ut().memoizedState}function Xi(e,t,n,r){var o=Tt();le.flags|=e,o.memoizedState=Qo(1|t,n,void 0,r===void 0?null:r)}function Xs(e,t,n,r){var o=ut();r=r===void 0?null:r;var i=void 0;if(ge!==null){var s=ge.memoizedState;if(i=s.destroy,r!==null&&ac(r,s.deps)){o.memoizedState=Qo(t,n,i,r);return}}le.flags|=e,o.memoizedState=Qo(1|t,n,i,r)}function sd(e,t){return Xi(8390656,8,e,t)}function fc(e,t){return Xs(2048,8,e,t)}function Hm(e,t){return Xs(4,2,e,t)}function Km(e,t){return Xs(4,4,e,t)}function Gm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ym(e,t,n){return n=n!=null?n.concat([e]):null,Xs(4,4,Gm.bind(null,t,e),n)}function dc(){}function Xm(e,t){var n=ut();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ac(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Qm(e,t){var n=ut();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ac(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zm(e,t,n){return Hn&21?(yt(n,t)||(n=nm(),le.lanes|=n,Kn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ze=!0),e.memoizedState=n)}function _1(e,t){var n=G;G=n!==0&&4>n?n:4,e(!0);var r=Vl.transition;Vl.transition={};try{e(!1),t()}finally{G=n,Vl.transition=r}}function qm(){return ut().memoizedState}function O1(e,t,n){var r=vn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jm(e))eg(t,n);else if(n=_m(e,t,n,r),n!==null){var o=Ve();vt(n,e,r,o),tg(n,t,r)}}function V1(e,t,n){var r=vn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jm(e))eg(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,yt(l,s)){var a=t.interleaved;a===null?(o.next=o,rc(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=_m(e,t,o,r),n!==null&&(o=Ve(),vt(n,e,r,o),tg(n,t,r))}}function Jm(e){var t=e.alternate;return e===le||t!==null&&t===le}function eg(e,t){Ro=Ps=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function tg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Uu(e,n)}}var Es={readContext:at,useCallback:Ae,useContext:Ae,useEffect:Ae,useImperativeHandle:Ae,useInsertionEffect:Ae,useLayoutEffect:Ae,useMemo:Ae,useReducer:Ae,useRef:Ae,useState:Ae,useDebugValue:Ae,useDeferredValue:Ae,useTransition:Ae,useMutableSource:Ae,useSyncExternalStore:Ae,useId:Ae,unstable_isNewReconciler:!1},I1={readContext:at,useCallback:function(e,t){return Tt().memoizedState=[e,t===void 0?null:t],e},useContext:at,useEffect:sd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Xi(4194308,4,Gm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Xi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Xi(4,2,e,t)},useMemo:function(e,t){var n=Tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=O1.bind(null,le,e),[r.memoizedState,e]},useRef:function(e){var t=Tt();return e={current:e},t.memoizedState=e},useState:id,useDebugValue:dc,useDeferredValue:function(e){return Tt().memoizedState=e},useTransition:function(){var e=id(!1),t=e[0];return e=_1.bind(null,e[1]),Tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=le,o=Tt();if(oe){if(n===void 0)throw Error(A(407));n=n()}else{if(n=t(),xe===null)throw Error(A(349));Hn&30||Fm(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,sd(Bm.bind(null,r,i,e),[e]),r.flags|=2048,Qo(9,zm.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Tt(),t=xe.identifierPrefix;if(oe){var n=Bt,r=zt;n=(r&~(1<<32-gt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Yo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=D1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},j1={readContext:at,useCallback:Xm,useContext:at,useEffect:fc,useImperativeHandle:Ym,useInsertionEffect:Hm,useLayoutEffect:Km,useMemo:Qm,useReducer:Il,useRef:Wm,useState:function(){return Il(Xo)},useDebugValue:dc,useDeferredValue:function(e){var t=ut();return Zm(t,ge.memoizedState,e)},useTransition:function(){var e=Il(Xo)[0],t=ut().memoizedState;return[e,t]},useMutableSource:Im,useSyncExternalStore:jm,useId:qm,unstable_isNewReconciler:!1},F1={readContext:at,useCallback:Xm,useContext:at,useEffect:fc,useImperativeHandle:Ym,useInsertionEffect:Hm,useLayoutEffect:Km,useMemo:Qm,useReducer:jl,useRef:Wm,useState:function(){return jl(Xo)},useDebugValue:dc,useDeferredValue:function(e){var t=ut();return ge===null?t.memoizedState=e:Zm(t,ge.memoizedState,e)},useTransition:function(){var e=jl(Xo)[0],t=ut().memoizedState;return[e,t]},useMutableSource:Im,useSyncExternalStore:jm,useId:qm,unstable_isNewReconciler:!1};function pt(e,t){if(e&&e.defaultProps){t=ae({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function $a(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ae({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Qs={isMounted:function(e){return(e=e._reactInternals)?Jn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=vn(e),i=$t(r,o);i.payload=t,n!=null&&(i.callback=n),t=mn(e,i,o),t!==null&&(vt(t,e,o,r),Gi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=vn(e),i=$t(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=mn(e,i,o),t!==null&&(vt(t,e,o,r),Gi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ve(),r=vn(e),o=$t(n,r);o.tag=2,t!=null&&(o.callback=t),t=mn(e,o,r),t!==null&&(vt(t,e,r,n),Gi(t,e,r))}};function ld(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!$o(n,r)||!$o(o,i):!0}function ng(e,t,n){var r=!1,o=xn,i=t.contextType;return typeof i=="object"&&i!==null?i=at(i):(o=$e(t)?Un:De.current,r=t.contextTypes,i=(r=r!=null)?Nr(e,o):xn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Qs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function ad(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Qs.enqueueReplaceState(t,t.state,null)}function Ua(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},oc(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=at(i):(i=$e(t)?Un:De.current,o.context=Nr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&($a(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Qs.enqueueReplaceState(o,o.state,null),Ss(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Vr(e,t){try{var n="",r=t;do n+=pw(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Fl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Wa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var z1=typeof WeakMap=="function"?WeakMap:Map;function rg(e,t,n){n=$t(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ks||(ks=!0,eu=r),Wa(e,t)},n}function og(e,t,n){n=$t(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Wa(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Wa(e,t),typeof r!="function"&&(gn===null?gn=new Set([this]):gn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function ud(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new z1;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=ex.bind(null,e,t,n),t.then(e,e))}function cd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function fd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=$t(-1,1),t.tag=2,mn(n,t,1))),n.lanes|=1),e)}var B1=qt.ReactCurrentOwner,ze=!1;function Oe(e,t,n,r){t.child=e===null?Dm(t,null,n,r):_r(t,e.child,n,r)}function dd(e,t,n,r,o){n=n.render;var i=t.ref;return kr(t,o),r=uc(e,t,n,r,i,o),n=cc(),e!==null&&!ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Yt(e,t,o)):(oe&&n&&Zu(t),t.flags|=1,Oe(e,t,r,o),t.child)}function pd(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!xc(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,ig(e,t,i,r,o)):(e=Ji(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:$o,n(s,r)&&e.ref===t.ref)return Yt(e,t,o)}return t.flags|=1,e=yn(i,r),e.ref=t.ref,e.return=t,t.child=e}function ig(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if($o(i,r)&&e.ref===t.ref)if(ze=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(ze=!0);else return t.lanes=e.lanes,Yt(e,t,o)}return Ha(e,t,n,r,o)}function sg(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Z(gr,Ke),Ke|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Z(gr,Ke),Ke|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Z(gr,Ke),Ke|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Z(gr,Ke),Ke|=r;return Oe(e,t,o,n),t.child}function lg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ha(e,t,n,r,o){var i=$e(n)?Un:De.current;return i=Nr(t,i),kr(t,o),n=uc(e,t,n,r,i,o),r=cc(),e!==null&&!ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Yt(e,t,o)):(oe&&r&&Zu(t),t.flags|=1,Oe(e,t,n,o),t.child)}function hd(e,t,n,r,o){if($e(n)){var i=!0;gs(t)}else i=!1;if(kr(t,o),t.stateNode===null)Qi(e,t),ng(t,n,r),Ua(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=at(u):(u=$e(n)?Un:De.current,u=Nr(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&ad(t,s,r,u),sn=!1;var d=t.memoizedState;s.state=d,Ss(t,r,s,o),a=t.memoizedState,l!==r||d!==a||Be.current||sn?(typeof c=="function"&&($a(t,n,c,r),a=t.memoizedState),(l=sn||ld(t,n,l,r,d,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Om(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:pt(t.type,l),s.props=u,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=at(a):(a=$e(n)?Un:De.current,a=Nr(t,a));var v=n.getDerivedStateFromProps;(c=typeof v=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||d!==a)&&ad(t,s,r,a),sn=!1,d=t.memoizedState,s.state=d,Ss(t,r,s,o);var w=t.memoizedState;l!==f||d!==w||Be.current||sn?(typeof v=="function"&&($a(t,n,v,r),w=t.memoizedState),(u=sn||ld(t,n,u,r,d,w,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,w,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,w,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),s.props=r,s.state=w,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Ka(e,t,n,r,i,o)}function Ka(e,t,n,r,o,i){lg(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Jf(t,n,!1),Yt(e,t,i);r=t.stateNode,B1.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=_r(t,e.child,null,i),t.child=_r(t,null,l,i)):Oe(e,t,l,i),t.memoizedState=r.state,o&&Jf(t,n,!0),t.child}function ag(e){var t=e.stateNode;t.pendingContext?qf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&qf(e,t.context,!1),ic(e,t.containerInfo)}function md(e,t,n,r,o){return Dr(),Ju(o),t.flags|=256,Oe(e,t,n,r),t.child}var Ga={dehydrated:null,treeContext:null,retryLane:0};function Ya(e){return{baseLanes:e,cachePool:null,transitions:null}}function ug(e,t,n){var r=t.pendingProps,o=ie.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Z(ie,o&1),e===null)return za(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Js(s,r,0,null),e=Bn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ya(n),t.memoizedState=Ga,e):pc(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return $1(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=yn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=yn(l,i):(i=Bn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Ya(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Ga,r}return i=e.child,e=i.sibling,r=yn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function pc(e,t){return t=Js({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function bi(e,t,n,r){return r!==null&&Ju(r),_r(t,e.child,null,n),e=pc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $1(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Fl(Error(A(422))),bi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Js({mode:"visible",children:r.children},o,0,null),i=Bn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&_r(t,e.child,null,s),t.child.memoizedState=Ya(s),t.memoizedState=Ga,i);if(!(t.mode&1))return bi(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(A(419)),r=Fl(i,r,void 0),bi(e,t,s,r)}if(l=(s&e.childLanes)!==0,ze||l){if(r=xe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Gt(e,o),vt(r,e,o,-1))}return wc(),r=Fl(Error(A(421))),bi(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=tx.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ye=hn(o.nextSibling),Xe=t,oe=!0,mt=null,e!==null&&(ot[it++]=zt,ot[it++]=Bt,ot[it++]=Wn,zt=e.id,Bt=e.overflow,Wn=t),t=pc(t,r.children),t.flags|=4096,t)}function gd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ba(e.return,t,n)}function zl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function cg(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Oe(e,t,r.children,n),r=ie.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gd(e,n,t);else if(e.tag===19)gd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Z(ie,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Cs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),zl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Cs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}zl(t,!0,n,null,i);break;case"together":zl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Qi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Yt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Kn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(A(153));if(t.child!==null){for(e=t.child,n=yn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=yn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function U1(e,t,n){switch(t.tag){case 3:ag(t),Dr();break;case 5:Vm(t);break;case 1:$e(t.type)&&gs(t);break;case 4:ic(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Z(ws,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Z(ie,ie.current&1),t.flags|=128,null):n&t.child.childLanes?ug(e,t,n):(Z(ie,ie.current&1),e=Yt(e,t,n),e!==null?e.sibling:null);Z(ie,ie.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return cg(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Z(ie,ie.current),r)break;return null;case 22:case 23:return t.lanes=0,sg(e,t,n)}return Yt(e,t,n)}var fg,Xa,dg,pg;fg=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Xa=function(){};dg=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,jn(Lt.current);var i=null;switch(n){case"input":o=va(e,o),r=va(e,r),i=[];break;case"select":o=ae({},o,{value:void 0}),r=ae({},r,{value:void 0}),i=[];break;case"textarea":o=xa(e,o),r=xa(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=hs)}Ca(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Oo.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Oo.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&ee("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};pg=function(e,t,n,r){n!==r&&(t.flags|=4)};function ao(e,t){if(!oe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function W1(e,t,n){var r=t.pendingProps;switch(qu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return $e(t.type)&&ms(),Me(t),null;case 3:return r=t.stateNode,Or(),te(Be),te(De),lc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ai(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,mt!==null&&(ru(mt),mt=null))),Xa(e,t),Me(t),null;case 5:sc(t);var o=jn(Go.current);if(n=t.type,e!==null&&t.stateNode!=null)dg(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(A(166));return Me(t),null}if(e=jn(Lt.current),Ai(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Rt]=t,r[Ho]=i,e=(t.mode&1)!==0,n){case"dialog":ee("cancel",r),ee("close",r);break;case"iframe":case"object":case"embed":ee("load",r);break;case"video":case"audio":for(o=0;o<vo.length;o++)ee(vo[o],r);break;case"source":ee("error",r);break;case"img":case"image":case"link":ee("error",r),ee("load",r);break;case"details":ee("toggle",r);break;case"input":Tf(r,i),ee("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ee("invalid",r);break;case"textarea":Rf(r,i),ee("invalid",r)}Ca(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Ri(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Ri(r.textContent,l,e),o=["children",""+l]):Oo.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&ee("scroll",r)}switch(n){case"input":wi(r),kf(r,i,!0);break;case"textarea":wi(r),Af(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=hs)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Bh(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Rt]=t,e[Ho]=r,fg(e,t,!1,!1),t.stateNode=e;e:{switch(s=Pa(n,r),n){case"dialog":ee("cancel",e),ee("close",e),o=r;break;case"iframe":case"object":case"embed":ee("load",e),o=r;break;case"video":case"audio":for(o=0;o<vo.length;o++)ee(vo[o],e);o=r;break;case"source":ee("error",e),o=r;break;case"img":case"image":case"link":ee("error",e),ee("load",e),o=r;break;case"details":ee("toggle",e),o=r;break;case"input":Tf(e,r),o=va(e,r),ee("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ae({},r,{value:void 0}),ee("invalid",e);break;case"textarea":Rf(e,r),o=xa(e,r),ee("invalid",e);break;default:o=r}Ca(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?Wh(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&$h(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Vo(e,a):typeof a=="number"&&Vo(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Oo.hasOwnProperty(i)?a!=null&&i==="onScroll"&&ee("scroll",e):a!=null&&Iu(e,i,a,s))}switch(n){case"input":wi(e),kf(e,r,!1);break;case"textarea":wi(e),Af(e);break;case"option":r.value!=null&&e.setAttribute("value",""+wn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Cr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Cr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=hs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Me(t),null;case 6:if(e&&t.stateNode!=null)pg(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(A(166));if(n=jn(Go.current),jn(Lt.current),Ai(t)){if(r=t.stateNode,n=t.memoizedProps,r[Rt]=t,(i=r.nodeValue!==n)&&(e=Xe,e!==null))switch(e.tag){case 3:Ri(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ri(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Rt]=t,t.stateNode=r}return Me(t),null;case 13:if(te(ie),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(oe&&Ye!==null&&t.mode&1&&!(t.flags&128))Lm(),Dr(),t.flags|=98560,i=!1;else if(i=Ai(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(A(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(A(317));i[Rt]=t}else Dr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Me(t),i=!1}else mt!==null&&(ru(mt),mt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ie.current&1?ve===0&&(ve=3):wc())),t.updateQueue!==null&&(t.flags|=4),Me(t),null);case 4:return Or(),Xa(e,t),e===null&&Uo(t.stateNode.containerInfo),Me(t),null;case 10:return nc(t.type._context),Me(t),null;case 17:return $e(t.type)&&ms(),Me(t),null;case 19:if(te(ie),i=t.memoizedState,i===null)return Me(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)ao(i,!1);else{if(ve!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Cs(e),s!==null){for(t.flags|=128,ao(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Z(ie,ie.current&1|2),t.child}e=e.sibling}i.tail!==null&&de()>Ir&&(t.flags|=128,r=!0,ao(i,!1),t.lanes=4194304)}else{if(!r)if(e=Cs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ao(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!oe)return Me(t),null}else 2*de()-i.renderingStartTime>Ir&&n!==1073741824&&(t.flags|=128,r=!0,ao(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=de(),t.sibling=null,n=ie.current,Z(ie,r?n&1|2:n&1),t):(Me(t),null);case 22:case 23:return yc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ke&1073741824&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),null;case 24:return null;case 25:return null}throw Error(A(156,t.tag))}function H1(e,t){switch(qu(t),t.tag){case 1:return $e(t.type)&&ms(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Or(),te(Be),te(De),lc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return sc(t),null;case 13:if(te(ie),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(A(340));Dr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return te(ie),null;case 4:return Or(),null;case 10:return nc(t.type._context),null;case 22:case 23:return yc(),null;case 24:return null;default:return null}}var Li=!1,Le=!1,K1=typeof WeakSet=="function"?WeakSet:Set,N=null;function mr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ce(e,t,r)}else n.current=null}function Qa(e,t,n){try{n()}catch(r){ce(e,t,r)}}var vd=!1;function G1(e,t){if(Da=fs,e=ym(),Qu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var v;f!==n||o!==0&&f.nodeType!==3||(l=s+o),f!==i||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(v=f.firstChild)!==null;)d=f,f=v;for(;;){if(f===e)break t;if(d===n&&++u===o&&(l=s),d===i&&++c===r&&(a=s),(v=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=v}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(_a={focusedElem:e,selectionRange:n},fs=!1,N=t;N!==null;)if(t=N,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,N=e;else for(;N!==null;){t=N;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var m=w.memoizedProps,x=w.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?m:pt(t.type,m),x);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(A(163))}}catch(S){ce(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,N=e;break}N=t.return}return w=vd,vd=!1,w}function Ao(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Qa(t,n,i)}o=o.next}while(o!==r)}}function Zs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Za(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function hg(e){var t=e.alternate;t!==null&&(e.alternate=null,hg(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Rt],delete t[Ho],delete t[Ia],delete t[M1],delete t[b1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mg(e){return e.tag===5||e.tag===3||e.tag===4}function yd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mg(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function qa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=hs));else if(r!==4&&(e=e.child,e!==null))for(qa(e,t,n),e=e.sibling;e!==null;)qa(e,t,n),e=e.sibling}function Ja(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ja(e,t,n),e=e.sibling;e!==null;)Ja(e,t,n),e=e.sibling}var Se=null,ht=!1;function Jt(e,t,n){for(n=n.child;n!==null;)gg(e,t,n),n=n.sibling}function gg(e,t,n){if(bt&&typeof bt.onCommitFiberUnmount=="function")try{bt.onCommitFiberUnmount(Us,n)}catch{}switch(n.tag){case 5:Le||mr(n,t);case 6:var r=Se,o=ht;Se=null,Jt(e,t,n),Se=r,ht=o,Se!==null&&(ht?(e=Se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Se.removeChild(n.stateNode));break;case 18:Se!==null&&(ht?(e=Se,n=n.stateNode,e.nodeType===8?Dl(e.parentNode,n):e.nodeType===1&&Dl(e,n),zo(e)):Dl(Se,n.stateNode));break;case 4:r=Se,o=ht,Se=n.stateNode.containerInfo,ht=!0,Jt(e,t,n),Se=r,ht=o;break;case 0:case 11:case 14:case 15:if(!Le&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Qa(n,t,s),o=o.next}while(o!==r)}Jt(e,t,n);break;case 1:if(!Le&&(mr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){ce(n,t,l)}Jt(e,t,n);break;case 21:Jt(e,t,n);break;case 22:n.mode&1?(Le=(r=Le)||n.memoizedState!==null,Jt(e,t,n),Le=r):Jt(e,t,n);break;default:Jt(e,t,n)}}function wd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new K1),t.forEach(function(r){var o=nx.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ct(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:Se=l.stateNode,ht=!1;break e;case 3:Se=l.stateNode.containerInfo,ht=!0;break e;case 4:Se=l.stateNode.containerInfo,ht=!0;break e}l=l.return}if(Se===null)throw Error(A(160));gg(i,s,o),Se=null,ht=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){ce(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)vg(t,e),t=t.sibling}function vg(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ct(t,e),Et(e),r&4){try{Ao(3,e,e.return),Zs(3,e)}catch(m){ce(e,e.return,m)}try{Ao(5,e,e.return)}catch(m){ce(e,e.return,m)}}break;case 1:ct(t,e),Et(e),r&512&&n!==null&&mr(n,n.return);break;case 5:if(ct(t,e),Et(e),r&512&&n!==null&&mr(n,n.return),e.flags&32){var o=e.stateNode;try{Vo(o,"")}catch(m){ce(e,e.return,m)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&Fh(o,i),Pa(l,s);var u=Pa(l,i);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?Wh(o,f):c==="dangerouslySetInnerHTML"?$h(o,f):c==="children"?Vo(o,f):Iu(o,c,f,u)}switch(l){case"input":ya(o,i);break;case"textarea":zh(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?Cr(o,!!i.multiple,v,!1):d!==!!i.multiple&&(i.defaultValue!=null?Cr(o,!!i.multiple,i.defaultValue,!0):Cr(o,!!i.multiple,i.multiple?[]:"",!1))}o[Ho]=i}catch(m){ce(e,e.return,m)}}break;case 6:if(ct(t,e),Et(e),r&4){if(e.stateNode===null)throw Error(A(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(m){ce(e,e.return,m)}}break;case 3:if(ct(t,e),Et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{zo(t.containerInfo)}catch(m){ce(e,e.return,m)}break;case 4:ct(t,e),Et(e);break;case 13:ct(t,e),Et(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(gc=de())),r&4&&wd(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Le=(u=Le)||c,ct(t,e),Le=u):ct(t,e),Et(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(N=e,c=e.child;c!==null;){for(f=N=c;N!==null;){switch(d=N,v=d.child,d.tag){case 0:case 11:case 14:case 15:Ao(4,d,d.return);break;case 1:mr(d,d.return);var w=d.stateNode;if(typeof w.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(m){ce(r,n,m)}}break;case 5:mr(d,d.return);break;case 22:if(d.memoizedState!==null){Sd(f);continue}}v!==null?(v.return=d,N=v):Sd(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Uh("display",s))}catch(m){ce(e,e.return,m)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(m){ce(e,e.return,m)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ct(t,e),Et(e),r&4&&wd(e);break;case 21:break;default:ct(t,e),Et(e)}}function Et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mg(n)){var r=n;break e}n=n.return}throw Error(A(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Vo(o,""),r.flags&=-33);var i=yd(e);Ja(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=yd(e);qa(e,l,s);break;default:throw Error(A(161))}}catch(a){ce(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Y1(e,t,n){N=e,yg(e)}function yg(e,t,n){for(var r=(e.mode&1)!==0;N!==null;){var o=N,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Li;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||Le;l=Li;var u=Le;if(Li=s,(Le=a)&&!u)for(N=o;N!==null;)s=N,a=s.child,s.tag===22&&s.memoizedState!==null?Cd(o):a!==null?(a.return=s,N=a):Cd(o);for(;i!==null;)N=i,yg(i),i=i.sibling;N=o,Li=l,Le=u}xd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,N=i):xd(e)}}function xd(e){for(;N!==null;){var t=N;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Le||Zs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Le)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:pt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&od(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}od(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&zo(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(A(163))}Le||t.flags&512&&Za(t)}catch(d){ce(t,t.return,d)}}if(t===e){N=null;break}if(n=t.sibling,n!==null){n.return=t.return,N=n;break}N=t.return}}function Sd(e){for(;N!==null;){var t=N;if(t===e){N=null;break}var n=t.sibling;if(n!==null){n.return=t.return,N=n;break}N=t.return}}function Cd(e){for(;N!==null;){var t=N;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Zs(4,t)}catch(a){ce(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){ce(t,o,a)}}var i=t.return;try{Za(t)}catch(a){ce(t,i,a)}break;case 5:var s=t.return;try{Za(t)}catch(a){ce(t,s,a)}}}catch(a){ce(t,t.return,a)}if(t===e){N=null;break}var l=t.sibling;if(l!==null){l.return=t.return,N=l;break}N=t.return}}var X1=Math.ceil,Ts=qt.ReactCurrentDispatcher,hc=qt.ReactCurrentOwner,lt=qt.ReactCurrentBatchConfig,H=0,xe=null,me=null,Pe=0,Ke=0,gr=kn(0),ve=0,Zo=null,Kn=0,qs=0,mc=0,Mo=null,Fe=null,gc=0,Ir=1/0,jt=null,ks=!1,eu=null,gn=null,Ni=!1,cn=null,Rs=0,bo=0,tu=null,Zi=-1,qi=0;function Ve(){return H&6?de():Zi!==-1?Zi:Zi=de()}function vn(e){return e.mode&1?H&2&&Pe!==0?Pe&-Pe:N1.transition!==null?(qi===0&&(qi=nm()),qi):(e=G,e!==0||(e=window.event,e=e===void 0?16:um(e.type)),e):1}function vt(e,t,n,r){if(50<bo)throw bo=0,tu=null,Error(A(185));li(e,n,r),(!(H&2)||e!==xe)&&(e===xe&&(!(H&2)&&(qs|=n),ve===4&&an(e,Pe)),Ue(e,r),n===1&&H===0&&!(t.mode&1)&&(Ir=de()+500,Ys&&Rn()))}function Ue(e,t){var n=e.callbackNode;Nw(e,t);var r=cs(e,e===xe?Pe:0);if(r===0)n!==null&&Lf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Lf(n),t===1)e.tag===0?L1(Pd.bind(null,e)):Am(Pd.bind(null,e)),R1(function(){!(H&6)&&Rn()}),n=null;else{switch(rm(r)){case 1:n=$u;break;case 4:n=em;break;case 16:n=us;break;case 536870912:n=tm;break;default:n=us}n=kg(n,wg.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function wg(e,t){if(Zi=-1,qi=0,H&6)throw Error(A(327));var n=e.callbackNode;if(Rr()&&e.callbackNode!==n)return null;var r=cs(e,e===xe?Pe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=As(e,r);else{t=r;var o=H;H|=2;var i=Sg();(xe!==e||Pe!==t)&&(jt=null,Ir=de()+500,zn(e,t));do try{q1();break}catch(l){xg(e,l)}while(!0);tc(),Ts.current=i,H=o,me!==null?t=0:(xe=null,Pe=0,t=ve)}if(t!==0){if(t===2&&(o=Aa(e),o!==0&&(r=o,t=nu(e,o))),t===1)throw n=Zo,zn(e,0),an(e,r),Ue(e,de()),n;if(t===6)an(e,r);else{if(o=e.current.alternate,!(r&30)&&!Q1(o)&&(t=As(e,r),t===2&&(i=Aa(e),i!==0&&(r=i,t=nu(e,i))),t===1))throw n=Zo,zn(e,0),an(e,r),Ue(e,de()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(A(345));case 2:Dn(e,Fe,jt);break;case 3:if(an(e,r),(r&130023424)===r&&(t=gc+500-de(),10<t)){if(cs(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ve(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Va(Dn.bind(null,e,Fe,jt),t);break}Dn(e,Fe,jt);break;case 4:if(an(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-gt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=de()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*X1(r/1960))-r,10<r){e.timeoutHandle=Va(Dn.bind(null,e,Fe,jt),r);break}Dn(e,Fe,jt);break;case 5:Dn(e,Fe,jt);break;default:throw Error(A(329))}}}return Ue(e,de()),e.callbackNode===n?wg.bind(null,e):null}function nu(e,t){var n=Mo;return e.current.memoizedState.isDehydrated&&(zn(e,t).flags|=256),e=As(e,t),e!==2&&(t=Fe,Fe=n,t!==null&&ru(t)),e}function ru(e){Fe===null?Fe=e:Fe.push.apply(Fe,e)}function Q1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!yt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function an(e,t){for(t&=~mc,t&=~qs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-gt(t),r=1<<n;e[n]=-1,t&=~r}}function Pd(e){if(H&6)throw Error(A(327));Rr();var t=cs(e,0);if(!(t&1))return Ue(e,de()),null;var n=As(e,t);if(e.tag!==0&&n===2){var r=Aa(e);r!==0&&(t=r,n=nu(e,r))}if(n===1)throw n=Zo,zn(e,0),an(e,t),Ue(e,de()),n;if(n===6)throw Error(A(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Dn(e,Fe,jt),Ue(e,de()),null}function vc(e,t){var n=H;H|=1;try{return e(t)}finally{H=n,H===0&&(Ir=de()+500,Ys&&Rn())}}function Gn(e){cn!==null&&cn.tag===0&&!(H&6)&&Rr();var t=H;H|=1;var n=lt.transition,r=G;try{if(lt.transition=null,G=1,e)return e()}finally{G=r,lt.transition=n,H=t,!(H&6)&&Rn()}}function yc(){Ke=gr.current,te(gr)}function zn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,k1(n)),me!==null)for(n=me.return;n!==null;){var r=n;switch(qu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ms();break;case 3:Or(),te(Be),te(De),lc();break;case 5:sc(r);break;case 4:Or();break;case 13:te(ie);break;case 19:te(ie);break;case 10:nc(r.type._context);break;case 22:case 23:yc()}n=n.return}if(xe=e,me=e=yn(e.current,null),Pe=Ke=t,ve=0,Zo=null,mc=qs=Kn=0,Fe=Mo=null,In!==null){for(t=0;t<In.length;t++)if(n=In[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}In=null}return e}function xg(e,t){do{var n=me;try{if(tc(),Yi.current=Es,Ps){for(var r=le.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ps=!1}if(Hn=0,ye=ge=le=null,Ro=!1,Yo=0,hc.current=null,n===null||n.return===null){ve=1,Zo=t,me=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=Pe,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var v=cd(s);if(v!==null){v.flags&=-257,fd(v,s,l,i,t),v.mode&1&&ud(i,u,t),t=v,a=u;var w=t.updateQueue;if(w===null){var m=new Set;m.add(a),t.updateQueue=m}else w.add(a);break e}else{if(!(t&1)){ud(i,u,t),wc();break e}a=Error(A(426))}}else if(oe&&l.mode&1){var x=cd(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),fd(x,s,l,i,t),Ju(Vr(a,l));break e}}i=a=Vr(a,l),ve!==4&&(ve=2),Mo===null?Mo=[i]:Mo.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=rg(i,a,t);rd(i,h);break e;case 1:l=a;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(gn===null||!gn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=og(i,l,t);rd(i,S);break e}}i=i.return}while(i!==null)}Pg(n)}catch(C){t=C,me===n&&n!==null&&(me=n=n.return);continue}break}while(!0)}function Sg(){var e=Ts.current;return Ts.current=Es,e===null?Es:e}function wc(){(ve===0||ve===3||ve===2)&&(ve=4),xe===null||!(Kn&268435455)&&!(qs&268435455)||an(xe,Pe)}function As(e,t){var n=H;H|=2;var r=Sg();(xe!==e||Pe!==t)&&(jt=null,zn(e,t));do try{Z1();break}catch(o){xg(e,o)}while(!0);if(tc(),H=n,Ts.current=r,me!==null)throw Error(A(261));return xe=null,Pe=0,ve}function Z1(){for(;me!==null;)Cg(me)}function q1(){for(;me!==null&&!Pw();)Cg(me)}function Cg(e){var t=Tg(e.alternate,e,Ke);e.memoizedProps=e.pendingProps,t===null?Pg(e):me=t,hc.current=null}function Pg(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=H1(n,t),n!==null){n.flags&=32767,me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ve=6,me=null;return}}else if(n=W1(n,t,Ke),n!==null){me=n;return}if(t=t.sibling,t!==null){me=t;return}me=t=e}while(t!==null);ve===0&&(ve=5)}function Dn(e,t,n){var r=G,o=lt.transition;try{lt.transition=null,G=1,J1(e,t,n,r)}finally{lt.transition=o,G=r}return null}function J1(e,t,n,r){do Rr();while(cn!==null);if(H&6)throw Error(A(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(A(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Dw(e,i),e===xe&&(me=xe=null,Pe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ni||(Ni=!0,kg(us,function(){return Rr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=lt.transition,lt.transition=null;var s=G;G=1;var l=H;H|=4,hc.current=null,G1(e,n),vg(n,e),w1(_a),fs=!!Da,_a=Da=null,e.current=n,Y1(n),Ew(),H=l,G=s,lt.transition=i}else e.current=n;if(Ni&&(Ni=!1,cn=e,Rs=o),i=e.pendingLanes,i===0&&(gn=null),Rw(n.stateNode),Ue(e,de()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ks)throw ks=!1,e=eu,eu=null,e;return Rs&1&&e.tag!==0&&Rr(),i=e.pendingLanes,i&1?e===tu?bo++:(bo=0,tu=e):bo=0,Rn(),null}function Rr(){if(cn!==null){var e=rm(Rs),t=lt.transition,n=G;try{if(lt.transition=null,G=16>e?16:e,cn===null)var r=!1;else{if(e=cn,cn=null,Rs=0,H&6)throw Error(A(331));var o=H;for(H|=4,N=e.current;N!==null;){var i=N,s=i.child;if(N.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(N=u;N!==null;){var c=N;switch(c.tag){case 0:case 11:case 15:Ao(8,c,i)}var f=c.child;if(f!==null)f.return=c,N=f;else for(;N!==null;){c=N;var d=c.sibling,v=c.return;if(hg(c),c===u){N=null;break}if(d!==null){d.return=v,N=d;break}N=v}}}var w=i.alternate;if(w!==null){var m=w.child;if(m!==null){w.child=null;do{var x=m.sibling;m.sibling=null,m=x}while(m!==null)}}N=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,N=s;else e:for(;N!==null;){if(i=N,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Ao(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,N=h;break e}N=i.return}}var p=e.current;for(N=p;N!==null;){s=N;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,N=g;else e:for(s=p;N!==null;){if(l=N,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Zs(9,l)}}catch(C){ce(l,l.return,C)}if(l===s){N=null;break e}var S=l.sibling;if(S!==null){S.return=l.return,N=S;break e}N=l.return}}if(H=o,Rn(),bt&&typeof bt.onPostCommitFiberRoot=="function")try{bt.onPostCommitFiberRoot(Us,e)}catch{}r=!0}return r}finally{G=n,lt.transition=t}}return!1}function Ed(e,t,n){t=Vr(n,t),t=rg(e,t,1),e=mn(e,t,1),t=Ve(),e!==null&&(li(e,1,t),Ue(e,t))}function ce(e,t,n){if(e.tag===3)Ed(e,e,n);else for(;t!==null;){if(t.tag===3){Ed(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(gn===null||!gn.has(r))){e=Vr(n,e),e=og(t,e,1),t=mn(t,e,1),e=Ve(),t!==null&&(li(t,1,e),Ue(t,e));break}}t=t.return}}function ex(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ve(),e.pingedLanes|=e.suspendedLanes&n,xe===e&&(Pe&n)===n&&(ve===4||ve===3&&(Pe&130023424)===Pe&&500>de()-gc?zn(e,0):mc|=n),Ue(e,t)}function Eg(e,t){t===0&&(e.mode&1?(t=Ci,Ci<<=1,!(Ci&130023424)&&(Ci=4194304)):t=1);var n=Ve();e=Gt(e,t),e!==null&&(li(e,t,n),Ue(e,n))}function tx(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Eg(e,n)}function nx(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(A(314))}r!==null&&r.delete(t),Eg(e,n)}var Tg;Tg=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Be.current)ze=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ze=!1,U1(e,t,n);ze=!!(e.flags&131072)}else ze=!1,oe&&t.flags&1048576&&Mm(t,ys,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Qi(e,t),e=t.pendingProps;var o=Nr(t,De.current);kr(t,n),o=uc(null,t,r,e,o,n);var i=cc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,$e(r)?(i=!0,gs(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,oc(t),o.updater=Qs,t.stateNode=o,o._reactInternals=t,Ua(t,r,e,n),t=Ka(null,t,r,!0,i,n)):(t.tag=0,oe&&i&&Zu(t),Oe(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Qi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=ox(r),e=pt(r,e),o){case 0:t=Ha(null,t,r,e,n);break e;case 1:t=hd(null,t,r,e,n);break e;case 11:t=dd(null,t,r,e,n);break e;case 14:t=pd(null,t,r,pt(r.type,e),n);break e}throw Error(A(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),Ha(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),hd(e,t,r,o,n);case 3:e:{if(ag(t),e===null)throw Error(A(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Om(e,t),Ss(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Vr(Error(A(423)),t),t=md(e,t,r,n,o);break e}else if(r!==o){o=Vr(Error(A(424)),t),t=md(e,t,r,n,o);break e}else for(Ye=hn(t.stateNode.containerInfo.firstChild),Xe=t,oe=!0,mt=null,n=Dm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Dr(),r===o){t=Yt(e,t,n);break e}Oe(e,t,r,n)}t=t.child}return t;case 5:return Vm(t),e===null&&za(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Oa(r,o)?s=null:i!==null&&Oa(r,i)&&(t.flags|=32),lg(e,t),Oe(e,t,s,n),t.child;case 6:return e===null&&za(t),null;case 13:return ug(e,t,n);case 4:return ic(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=_r(t,null,r,n):Oe(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),dd(e,t,r,o,n);case 7:return Oe(e,t,t.pendingProps,n),t.child;case 8:return Oe(e,t,t.pendingProps.children,n),t.child;case 12:return Oe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,Z(ws,r._currentValue),r._currentValue=s,i!==null)if(yt(i.value,s)){if(i.children===o.children&&!Be.current){t=Yt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=$t(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ba(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(A(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ba(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Oe(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,kr(t,n),o=at(o),r=r(o),t.flags|=1,Oe(e,t,r,n),t.child;case 14:return r=t.type,o=pt(r,t.pendingProps),o=pt(r.type,o),pd(e,t,r,o,n);case 15:return ig(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:pt(r,o),Qi(e,t),t.tag=1,$e(r)?(e=!0,gs(t)):e=!1,kr(t,n),ng(t,r,o),Ua(t,r,o,n),Ka(null,t,r,!0,e,n);case 19:return cg(e,t,n);case 22:return sg(e,t,n)}throw Error(A(156,t.tag))};function kg(e,t){return Jh(e,t)}function rx(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function st(e,t,n,r){return new rx(e,t,n,r)}function xc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ox(e){if(typeof e=="function")return xc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Fu)return 11;if(e===zu)return 14}return 2}function yn(e,t){var n=e.alternate;return n===null?(n=st(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ji(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")xc(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case sr:return Bn(n.children,o,i,t);case ju:s=8,o|=8;break;case pa:return e=st(12,n,t,o|2),e.elementType=pa,e.lanes=i,e;case ha:return e=st(13,n,t,o),e.elementType=ha,e.lanes=i,e;case ma:return e=st(19,n,t,o),e.elementType=ma,e.lanes=i,e;case Vh:return Js(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case _h:s=10;break e;case Oh:s=9;break e;case Fu:s=11;break e;case zu:s=14;break e;case on:s=16,r=null;break e}throw Error(A(130,e==null?e:typeof e,""))}return t=st(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Bn(e,t,n,r){return e=st(7,e,r,t),e.lanes=n,e}function Js(e,t,n,r){return e=st(22,e,r,t),e.elementType=Vh,e.lanes=n,e.stateNode={isHidden:!1},e}function Bl(e,t,n){return e=st(6,e,null,t),e.lanes=n,e}function $l(e,t,n){return t=st(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ix(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Cl(0),this.expirationTimes=Cl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Cl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Sc(e,t,n,r,o,i,s,l,a){return e=new ix(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=st(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},oc(i),e}function sx(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ir,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Rg(e){if(!e)return xn;e=e._reactInternals;e:{if(Jn(e)!==e||e.tag!==1)throw Error(A(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if($e(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(A(171))}if(e.tag===1){var n=e.type;if($e(n))return Rm(e,n,t)}return t}function Ag(e,t,n,r,o,i,s,l,a){return e=Sc(n,r,!0,e,o,i,s,l,a),e.context=Rg(null),n=e.current,r=Ve(),o=vn(n),i=$t(r,o),i.callback=t??null,mn(n,i,o),e.current.lanes=o,li(e,o,r),Ue(e,r),e}function el(e,t,n,r){var o=t.current,i=Ve(),s=vn(o);return n=Rg(n),t.context===null?t.context=n:t.pendingContext=n,t=$t(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=mn(o,t,s),e!==null&&(vt(e,o,s,i),Gi(e,o,s)),s}function Ms(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Td(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Cc(e,t){Td(e,t),(e=e.alternate)&&Td(e,t)}function lx(){return null}var Mg=typeof reportError=="function"?reportError:function(e){console.error(e)};function Pc(e){this._internalRoot=e}tl.prototype.render=Pc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(A(409));el(e,t,null,null)};tl.prototype.unmount=Pc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Gn(function(){el(null,e,null,null)}),t[Kt]=null}};function tl(e){this._internalRoot=e}tl.prototype.unstable_scheduleHydration=function(e){if(e){var t=sm();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ln.length&&t!==0&&t<ln[n].priority;n++);ln.splice(n,0,e),n===0&&am(e)}};function Ec(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function nl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function kd(){}function ax(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Ms(s);i.call(u)}}var s=Ag(t,r,e,0,null,!1,!1,"",kd);return e._reactRootContainer=s,e[Kt]=s.current,Uo(e.nodeType===8?e.parentNode:e),Gn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=Ms(a);l.call(u)}}var a=Sc(e,0,!1,null,null,!1,!1,"",kd);return e._reactRootContainer=a,e[Kt]=a.current,Uo(e.nodeType===8?e.parentNode:e),Gn(function(){el(t,a,n,r)}),a}function rl(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=Ms(s);l.call(a)}}el(t,s,e,o)}else s=ax(n,t,e,o,r);return Ms(s)}om=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=go(t.pendingLanes);n!==0&&(Uu(t,n|1),Ue(t,de()),!(H&6)&&(Ir=de()+500,Rn()))}break;case 13:Gn(function(){var r=Gt(e,1);if(r!==null){var o=Ve();vt(r,e,1,o)}}),Cc(e,1)}};Wu=function(e){if(e.tag===13){var t=Gt(e,134217728);if(t!==null){var n=Ve();vt(t,e,134217728,n)}Cc(e,134217728)}};im=function(e){if(e.tag===13){var t=vn(e),n=Gt(e,t);if(n!==null){var r=Ve();vt(n,e,t,r)}Cc(e,t)}};sm=function(){return G};lm=function(e,t){var n=G;try{return G=e,t()}finally{G=n}};Ta=function(e,t,n){switch(t){case"input":if(ya(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Gs(r);if(!o)throw Error(A(90));jh(r),ya(r,o)}}}break;case"textarea":zh(e,n);break;case"select":t=n.value,t!=null&&Cr(e,!!n.multiple,t,!1)}};Gh=vc;Yh=Gn;var ux={usingClientEntryPoint:!1,Events:[ui,cr,Gs,Hh,Kh,vc]},uo={findFiberByHostInstance:Vn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},cx={bundleType:uo.bundleType,version:uo.version,rendererPackageName:uo.rendererPackageName,rendererConfig:uo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:qt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Zh(e),e===null?null:e.stateNode},findFiberByHostInstance:uo.findFiberByHostInstance||lx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Di=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Di.isDisabled&&Di.supportsFiber)try{Us=Di.inject(cx),bt=Di}catch{}}et.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ux;et.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ec(t))throw Error(A(200));return sx(e,t,null,n)};et.createRoot=function(e,t){if(!Ec(e))throw Error(A(299));var n=!1,r="",o=Mg;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Sc(e,1,!1,null,null,n,!1,r,o),e[Kt]=t.current,Uo(e.nodeType===8?e.parentNode:e),new Pc(t)};et.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(A(188)):(e=Object.keys(e).join(","),Error(A(268,e)));return e=Zh(t),e=e===null?null:e.stateNode,e};et.flushSync=function(e){return Gn(e)};et.hydrate=function(e,t,n){if(!nl(t))throw Error(A(200));return rl(null,e,t,!0,n)};et.hydrateRoot=function(e,t,n){if(!Ec(e))throw Error(A(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Mg;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Ag(t,null,e,1,n??null,o,!1,i,s),e[Kt]=t.current,Uo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new tl(t)};et.render=function(e,t,n){if(!nl(t))throw Error(A(200));return rl(null,e,t,!1,n)};et.unmountComponentAtNode=function(e){if(!nl(e))throw Error(A(40));return e._reactRootContainer?(Gn(function(){rl(null,null,e,!1,function(){e._reactRootContainer=null,e[Kt]=null})}),!0):!1};et.unstable_batchedUpdates=vc;et.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!nl(n))throw Error(A(200));if(e==null||e._reactInternals===void 0)throw Error(A(38));return rl(e,t,n,!1,r)};et.version="18.3.1-next-f1338f8080-20240426";function bg(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(bg)}catch(e){console.error(e)}}bg(),bh.exports=et;var Hr=bh.exports;const fx=vh(Hr);var dx,Rd=Hr;dx=Rd.createRoot,Rd.hydrateRoot;function Ad(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Lg(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Ad(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Ad(e[o],null)}}}}function Te(...e){return y.useCallback(Lg(...e),e)}function qo(e){const t=hx(e),n=y.forwardRef((r,o)=>{const{children:i,...s}=r,l=y.Children.toArray(i),a=l.find(gx);if(a){const u=a.props.children,c=l.map(f=>f===a?y.Children.count(u)>1?y.Children.only(null):y.isValidElement(u)?u.props.children:null:f);return k.jsx(t,{...s,ref:o,children:y.isValidElement(u)?y.cloneElement(u,void 0,c):null})}return k.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var px=qo("Slot");function hx(e){const t=y.forwardRef((n,r)=>{const{children:o,...i}=n;if(y.isValidElement(o)){const s=yx(o),l=vx(i,o.props);return o.type!==y.Fragment&&(l.ref=r?Lg(r,s):s),y.cloneElement(o,l)}return y.Children.count(o)>1?y.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var mx=Symbol("radix.slottable");function gx(e){return y.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===mx}function vx(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{const a=i(...l);return o(...l),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function yx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Ng(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Ng(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function wx(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Ng(e))&&(r&&(r+=" "),r+=t);return r}const Md=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,bd=wx,xx=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return bd(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const c=n==null?void 0:n[u],f=i==null?void 0:i[u];if(c===null)return null;const d=Md(c)||Md(f);return o[u][d]}),l=n&&Object.entries(n).reduce((u,c)=>{let[f,d]=c;return d===void 0||(u[f]=d),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:f,className:d,...v}=c;return Object.entries(v).every(w=>{let[m,x]=w;return Array.isArray(x)?x.includes({...i,...l}[m]):{...i,...l}[m]===x})?[...u,f,d]:u},[]);return bd(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)},Tc="-",Sx=e=>{const t=Px(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const l=s.split(Tc);return l[0]===""&&l.length!==1&&l.shift(),Dg(l,t)||Cx(s)},getConflictingClassGroupIds:(s,l)=>{const a=n[s]||[];return l&&r[s]?[...a,...r[s]]:a}}},Dg=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Dg(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Tc);return(s=t.validators.find(({validator:l})=>l(i)))==null?void 0:s.classGroupId},Ld=/^\[(.+)\]$/,Cx=e=>{if(Ld.test(e)){const t=Ld.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Px=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Tx(Object.entries(e.classGroups),n).forEach(([i,s])=>{ou(s,r,i,t)}),r},ou=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Nd(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(Ex(o)){ou(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{ou(s,Nd(t,i),n,r)})})},Nd=(e,t)=>{let n=e;return t.split(Tc).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Ex=e=>e.isThemeGetter,Tx=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,l])=>[t+s,l])):i);return[n,o]}):e,kx=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},_g="!",Rx=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=l=>{const a=[];let u=0,c=0,f;for(let x=0;x<l.length;x++){let h=l[x];if(u===0){if(h===o&&(r||l.slice(x,x+i)===t)){a.push(l.slice(c,x)),c=x+i;continue}if(h==="/"){f=x;continue}}h==="["?u++:h==="]"&&u--}const d=a.length===0?l:l.substring(c),v=d.startsWith(_g),w=v?d.substring(1):d,m=f&&f>c?f-c:void 0;return{modifiers:a,hasImportantModifier:v,baseClassName:w,maybePostfixModifierPosition:m}};return n?l=>n({className:l,parseClassName:s}):s},Ax=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Mx=e=>({cache:kx(e.cacheSize),parseClassName:Rx(e),...Sx(e)}),bx=/\s+/,Lx=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(bx);let l="";for(let a=s.length-1;a>=0;a-=1){const u=s[a],{modifiers:c,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:v}=n(u);let w=!!v,m=r(w?d.substring(0,v):d);if(!m){if(!w){l=u+(l.length>0?" "+l:l);continue}if(m=r(d),!m){l=u+(l.length>0?" "+l:l);continue}w=!1}const x=Ax(c).join(":"),h=f?x+_g:x,p=h+m;if(i.includes(p))continue;i.push(p);const g=o(m,w);for(let S=0;S<g.length;++S){const C=g[S];i.push(h+C)}l=u+(l.length>0?" "+l:l)}return l};function Nx(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Og(t))&&(r&&(r+=" "),r+=n);return r}const Og=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Og(e[r]))&&(n&&(n+=" "),n+=t);return n};function Dx(e,...t){let n,r,o,i=s;function s(a){const u=t.reduce((c,f)=>f(c),e());return n=Mx(u),r=n.cache.get,o=n.cache.set,i=l,l(a)}function l(a){const u=r(a);if(u)return u;const c=Lx(a,n);return o(a,c),c}return function(){return i(Nx.apply(null,arguments))}}const J=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Vg=/^\[(?:([a-z-]+):)?(.+)\]$/i,_x=/^\d+\/\d+$/,Ox=new Set(["px","full","screen"]),Vx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ix=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,jx=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Fx=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,zx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,It=e=>Ar(e)||Ox.has(e)||_x.test(e),en=e=>Kr(e,"length",Yx),Ar=e=>!!e&&!Number.isNaN(Number(e)),Ul=e=>Kr(e,"number",Ar),co=e=>!!e&&Number.isInteger(Number(e)),Bx=e=>e.endsWith("%")&&Ar(e.slice(0,-1)),$=e=>Vg.test(e),tn=e=>Vx.test(e),$x=new Set(["length","size","percentage"]),Ux=e=>Kr(e,$x,Ig),Wx=e=>Kr(e,"position",Ig),Hx=new Set(["image","url"]),Kx=e=>Kr(e,Hx,Qx),Gx=e=>Kr(e,"",Xx),fo=()=>!0,Kr=(e,t,n)=>{const r=Vg.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Yx=e=>Ix.test(e)&&!jx.test(e),Ig=()=>!1,Xx=e=>Fx.test(e),Qx=e=>zx.test(e),Zx=()=>{const e=J("colors"),t=J("spacing"),n=J("blur"),r=J("brightness"),o=J("borderColor"),i=J("borderRadius"),s=J("borderSpacing"),l=J("borderWidth"),a=J("contrast"),u=J("grayscale"),c=J("hueRotate"),f=J("invert"),d=J("gap"),v=J("gradientColorStops"),w=J("gradientColorStopPositions"),m=J("inset"),x=J("margin"),h=J("opacity"),p=J("padding"),g=J("saturate"),S=J("scale"),C=J("sepia"),E=J("skew"),P=J("space"),T=J("translate"),L=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto",$,t],_=()=>[$,t],U=()=>["",It,en],z=()=>["auto",Ar,$],K=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],F=()=>["solid","dashed","dotted","double","none"],O=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],R=()=>["start","end","center","between","around","evenly","stretch"],b=()=>["","0",$],D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[Ar,$];return{cacheSize:500,separator:":",theme:{colors:[fo],spacing:[It,en],blur:["none","",tn,$],brightness:B(),borderColor:[e],borderRadius:["none","","full",tn,$],borderSpacing:_(),borderWidth:U(),contrast:B(),grayscale:b(),hueRotate:B(),invert:b(),gap:_(),gradientColorStops:[e],gradientColorStopPositions:[Bx,en],inset:j(),margin:j(),opacity:B(),padding:_(),saturate:B(),scale:B(),sepia:b(),skew:B(),space:_(),translate:_()},classGroups:{aspect:[{aspect:["auto","square","video",$]}],container:["container"],columns:[{columns:[tn]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...K(),$]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",co,$]}],basis:[{basis:j()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",$]}],grow:[{grow:b()}],shrink:[{shrink:b()}],order:[{order:["first","last","none",co,$]}],"grid-cols":[{"grid-cols":[fo]}],"col-start-end":[{col:["auto",{span:["full",co,$]},$]}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":[fo]}],"row-start-end":[{row:["auto",{span:[co,$]},$]}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",$]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",$]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...R()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...R(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...R(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",$,t]}],"min-w":[{"min-w":[$,t,"min","max","fit"]}],"max-w":[{"max-w":[$,t,"none","full","min","max","fit","prose",{screen:[tn]},tn]}],h:[{h:[$,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[$,t,"auto","min","max","fit"]}],"font-size":[{text:["base",tn,en]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ul]}],"font-family":[{font:[fo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",$]}],"line-clamp":[{"line-clamp":["none",Ar,Ul]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",It,$]}],"list-image":[{"list-image":["none",$]}],"list-style-type":[{list:["none","disc","decimal",$]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...F(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",It,en]}],"underline-offset":[{"underline-offset":["auto",It,$]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...K(),Wx]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Ux]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Kx]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...F(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:F()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...F()]}],"outline-offset":[{"outline-offset":[It,$]}],"outline-w":[{outline:[It,en]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[It,en]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",tn,Gx]}],"shadow-color":[{shadow:[fo]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...O(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":O()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",tn,$]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[f]}],saturate:[{saturate:[g]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",$]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",$]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",$]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[co,$]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",$]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",$]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",$]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[It,en,Ul]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},qx=Dx(Zx),we=qx,jg=kt.forwardRef(({className:e,size:t,...n},r)=>{const o=s=>`${-1.2+s*.1}s`,i=s=>`${s*30}deg`;return k.jsx("div",{className:we("",e),role:"status","aria-label":"Loading",ref:r,style:{width:`${t||"20"}px`,height:`${t||"20"}px`},...n,children:k.jsx("div",{className:"relative left-1/2 top-1/2 size-full",children:[...Array(12)].map((s,l)=>k.jsx("div",{className:"absolute left-[-10%] top-[-3.9%] h-[8%] w-[24%] animate-spinner rounded-md bg-foreground",style:{animationDelay:o(l),transform:`rotate(${i(l)}) translate(146%)`}},l))})})});jg.displayName="Spinner";const Fg=y.createContext({});function Jx(e){const t=y.useRef(null);return t.current===null&&(t.current=e()),t.current}const kc=y.createContext(null),zg=y.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});function eS(e=!0){const t=y.useContext(kc);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:o}=t,i=y.useId();y.useEffect(()=>{e&&o(i)},[e]);const s=y.useCallback(()=>e&&r&&r(i),[i,r,e]);return!n&&r?[!1,s]:[!0]}const Rc=typeof window<"u",tS=Rc?y.useLayoutEffect:y.useEffect,Qe=e=>e;let Bg=Qe;function Ac(e){let t;return()=>(t===void 0&&(t=e()),t)}const jr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Ut=e=>e*1e3,Wt=e=>e/1e3,nS={useManualTiming:!1};function rS(e){let t=new Set,n=new Set,r=!1,o=!1;const i=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function l(u){i.has(u)&&(a.schedule(u),e()),u(s)}const a={schedule:(u,c=!1,f=!1)=>{const v=f&&r?t:n;return c&&i.add(u),v.has(u)||v.add(u),u},cancel:u=>{n.delete(u),i.delete(u)},process:u=>{if(s=u,r){o=!0;return}r=!0,[t,n]=[n,t],t.forEach(l),t.clear(),r=!1,o&&(o=!1,a.process(u))}};return a}const _i=["read","resolveKeyframes","update","preRender","render","postRender"],oS=40;function $g(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,s=_i.reduce((h,p)=>(h[p]=rS(i),h),{}),{read:l,resolveKeyframes:a,update:u,preRender:c,render:f,postRender:d}=s,v=()=>{const h=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(h-o.timestamp,oS),1),o.timestamp=h,o.isProcessing=!0,l.process(o),a.process(o),u.process(o),c.process(o),f.process(o),d.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(v))},w=()=>{n=!0,r=!0,o.isProcessing||e(v)};return{schedule:_i.reduce((h,p)=>{const g=s[p];return h[p]=(S,C=!1,E=!1)=>(n||w(),g.schedule(S,C,E)),h},{}),cancel:h=>{for(let p=0;p<_i.length;p++)s[_i[p]].cancel(h)},state:o,steps:s}}const{schedule:ne,cancel:Sn,state:Ce,steps:Wl}=$g(typeof requestAnimationFrame<"u"?requestAnimationFrame:Qe,!0),Ug=y.createContext({strict:!1}),Dd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Fr={};for(const e in Dd)Fr[e]={isEnabled:t=>Dd[e].some(n=>!!t[n])};function iS(e){for(const t in e)Fr[t]={...Fr[t],...e[t]}}const sS=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function bs(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||sS.has(e)}let Wg=e=>!bs(e);function lS(e){e&&(Wg=t=>t.startsWith("on")?!bs(t):e(t))}try{lS(require("@emotion/is-prop-valid").default)}catch{}function aS(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Wg(o)||n===!0&&bs(o)||!t&&!bs(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function uS(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}const ol=y.createContext({});function Jo(e){return typeof e=="string"||Array.isArray(e)}function il(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Mc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],bc=["initial",...Mc];function sl(e){return il(e.animate)||bc.some(t=>Jo(e[t]))}function Hg(e){return!!(sl(e)||e.variants)}function cS(e,t){if(sl(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Jo(n)?n:void 0,animate:Jo(r)?r:void 0}}return e.inherit!==!1?t:{}}function fS(e){const{initial:t,animate:n}=cS(e,y.useContext(ol));return y.useMemo(()=>({initial:t,animate:n}),[_d(t),_d(n)])}function _d(e){return Array.isArray(e)?e.join(" "):e}const dS=Symbol.for("motionComponentSymbol");function vr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function pS(e,t,n){return y.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):vr(n)&&(n.current=r))},[t])}const Lc=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),hS="framerAppearId",Kg="data-"+Lc(hS),{schedule:Nc}=$g(queueMicrotask,!1),Gg=y.createContext({});function mS(e,t,n,r,o){var i,s;const{visualElement:l}=y.useContext(ol),a=y.useContext(Ug),u=y.useContext(kc),c=y.useContext(zg).reducedMotion,f=y.useRef(null);r=r||a.renderer,!f.current&&r&&(f.current=r(e,{visualState:t,parent:l,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const d=f.current,v=y.useContext(Gg);d&&!d.projection&&o&&(d.type==="html"||d.type==="svg")&&gS(f.current,n,o,v);const w=y.useRef(!1);y.useInsertionEffect(()=>{d&&w.current&&d.update(n,u)});const m=n[Kg],x=y.useRef(!!m&&!(!((i=window.MotionHandoffIsComplete)===null||i===void 0)&&i.call(window,m))&&((s=window.MotionHasOptimisedAnimation)===null||s===void 0?void 0:s.call(window,m)));return tS(()=>{d&&(w.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),Nc.render(d.render),x.current&&d.animationState&&d.animationState.animateChanges())}),y.useEffect(()=>{d&&(!x.current&&d.animationState&&d.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var h;(h=window.MotionHandoffMarkAsComplete)===null||h===void 0||h.call(window,m)}),x.current=!1))}),d}function gS(e,t,n,r){const{layoutId:o,layout:i,drag:s,dragConstraints:l,layoutScroll:a,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Yg(e.parent)),e.projection.setOptions({layoutId:o,layout:i,alwaysMeasureLayout:!!s||l&&vr(l),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:u})}function Yg(e){if(e)return e.options.allowProjection!==!1?e.projection:Yg(e.parent)}function vS({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){var i,s;e&&iS(e);function l(u,c){let f;const d={...y.useContext(zg),...u,layoutId:yS(u)},{isStatic:v}=d,w=fS(u),m=r(u,v);if(!v&&Rc){wS();const x=xS(d);f=x.MeasureLayout,w.visualElement=mS(o,m,d,t,x.ProjectionNode)}return k.jsxs(ol.Provider,{value:w,children:[f&&w.visualElement?k.jsx(f,{visualElement:w.visualElement,...d}):null,n(o,u,pS(m,w.visualElement,c),m,v,w.visualElement)]})}l.displayName=`motion.${typeof o=="string"?o:`create(${(s=(i=o.displayName)!==null&&i!==void 0?i:o.name)!==null&&s!==void 0?s:""})`}`;const a=y.forwardRef(l);return a[dS]=o,a}function yS({layoutId:e}){const t=y.useContext(Fg).id;return t&&e!==void 0?t+"-"+e:e}function wS(e,t){y.useContext(Ug).strict}function xS(e){const{drag:t,layout:n}=Fr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const SS=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Dc(e){return typeof e!="string"||e.includes("-")?!1:!!(SS.indexOf(e)>-1||/[A-Z]/u.test(e))}function Od(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function _c(e,t,n,r){if(typeof t=="function"){const[o,i]=Od(r);t=t(n!==void 0?n:e.custom,o,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,i]=Od(r);t=t(n!==void 0?n:e.custom,o,i)}return t}const iu=e=>Array.isArray(e),CS=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),PS=e=>iu(e)?e[e.length-1]||0:e,Ne=e=>!!(e&&e.getVelocity);function es(e){const t=Ne(e)?e.get():e;return CS(t)?t.toValue():t}function ES({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,o,i){const s={latestValues:TS(r,o,i,e),renderState:t()};return n&&(s.onMount=l=>n({props:r,current:l,...s}),s.onUpdate=l=>n(l)),s}const Xg=e=>(t,n)=>{const r=y.useContext(ol),o=y.useContext(kc),i=()=>ES(e,t,r,o);return n?i():Jx(i)};function TS(e,t,n,r){const o={},i=r(e,{});for(const d in i)o[d]=es(i[d]);let{initial:s,animate:l}=e;const a=sl(e),u=Hg(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?l:s;if(f&&typeof f!="boolean"&&!il(f)){const d=Array.isArray(f)?f:[f];for(let v=0;v<d.length;v++){const w=_c(e,d[v]);if(w){const{transitionEnd:m,transition:x,...h}=w;for(const p in h){let g=h[p];if(Array.isArray(g)){const S=c?g.length-1:0;g=g[S]}g!==null&&(o[p]=g)}for(const p in m)o[p]=m[p]}}}return o}const Gr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],er=new Set(Gr),Qg=e=>t=>typeof t=="string"&&t.startsWith(e),Zg=Qg("--"),kS=Qg("var(--"),Oc=e=>kS(e)?RS.test(e.split("/*")[0].trim()):!1,RS=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,qg=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Xt=(e,t,n)=>n>t?t:n<e?e:n,Yr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},ei={...Yr,transform:e=>Xt(0,1,e)},Oi={...Yr,default:1},fi=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),nn=fi("deg"),Nt=fi("%"),V=fi("px"),AS=fi("vh"),MS=fi("vw"),Vd={...Nt,parse:e=>Nt.parse(e)/100,transform:e=>Nt.transform(e*100)},bS={borderWidth:V,borderTopWidth:V,borderRightWidth:V,borderBottomWidth:V,borderLeftWidth:V,borderRadius:V,radius:V,borderTopLeftRadius:V,borderTopRightRadius:V,borderBottomRightRadius:V,borderBottomLeftRadius:V,width:V,maxWidth:V,height:V,maxHeight:V,top:V,right:V,bottom:V,left:V,padding:V,paddingTop:V,paddingRight:V,paddingBottom:V,paddingLeft:V,margin:V,marginTop:V,marginRight:V,marginBottom:V,marginLeft:V,backgroundPositionX:V,backgroundPositionY:V},LS={rotate:nn,rotateX:nn,rotateY:nn,rotateZ:nn,scale:Oi,scaleX:Oi,scaleY:Oi,scaleZ:Oi,skew:nn,skewX:nn,skewY:nn,distance:V,translateX:V,translateY:V,translateZ:V,x:V,y:V,z:V,perspective:V,transformPerspective:V,opacity:ei,originX:Vd,originY:Vd,originZ:V},Id={...Yr,transform:Math.round},Vc={...bS,...LS,zIndex:Id,size:V,fillOpacity:ei,strokeOpacity:ei,numOctaves:Id},NS={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},DS=Gr.length;function _S(e,t,n){let r="",o=!0;for(let i=0;i<DS;i++){const s=Gr[i],l=e[s];if(l===void 0)continue;let a=!0;if(typeof l=="number"?a=l===(s.startsWith("scale")?1:0):a=parseFloat(l)===0,!a||n){const u=qg(l,Vc[s]);if(!a){o=!1;const c=NS[s]||s;r+=`${c}(${u}) `}n&&(t[s]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function Ic(e,t,n){const{style:r,vars:o,transformOrigin:i}=e;let s=!1,l=!1;for(const a in t){const u=t[a];if(er.has(a)){s=!0;continue}else if(Zg(a)){o[a]=u;continue}else{const c=qg(u,Vc[a]);a.startsWith("origin")?(l=!0,i[a]=c):r[a]=c}}if(t.transform||(s||n?r.transform=_S(t,e.transform,n):r.transform&&(r.transform="none")),l){const{originX:a="50%",originY:u="50%",originZ:c=0}=i;r.transformOrigin=`${a} ${u} ${c}`}}const OS={offset:"stroke-dashoffset",array:"stroke-dasharray"},VS={offset:"strokeDashoffset",array:"strokeDasharray"};function IS(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?OS:VS;e[i.offset]=V.transform(-r);const s=V.transform(t),l=V.transform(n);e[i.array]=`${s} ${l}`}function jd(e,t,n){return typeof e=="string"?e:V.transform(t+n*e)}function jS(e,t,n){const r=jd(t,e.x,e.width),o=jd(n,e.y,e.height);return`${r} ${o}`}function jc(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:i,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},c,f){if(Ic(e,u,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:d,style:v,dimensions:w}=e;d.transform&&(w&&(v.transform=d.transform),delete d.transform),w&&(o!==void 0||i!==void 0||v.transform)&&(v.transformOrigin=jS(w,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),r!==void 0&&(d.scale=r),s!==void 0&&IS(d,s,l,a,!1)}const Fc=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Jg=()=>({...Fc(),attrs:{}}),zc=e=>typeof e=="string"&&e.toLowerCase()==="svg";function ev(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const tv=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function nv(e,t,n,r){ev(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(tv.has(o)?o:Lc(o),t.attrs[o])}const Ls={};function FS(e){Object.assign(Ls,e)}function rv(e,{layout:t,layoutId:n}){return er.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ls[e]||e==="opacity")}function Bc(e,t,n){var r;const{style:o}=e,i={};for(const s in o)(Ne(o[s])||t.style&&Ne(t.style[s])||rv(s,e)||((r=n==null?void 0:n.getValue(s))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[s]=o[s]);return i}function ov(e,t,n){const r=Bc(e,t,n);for(const o in e)if(Ne(e[o])||Ne(t[o])){const i=Gr.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[i]=e[o]}return r}function zS(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const Fd=["x","y","width","height","cx","cy","r"],BS={useVisualState:Xg({scrapeMotionValuesFromProps:ov,createRenderState:Jg,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:o})=>{if(!n)return;let i=!!e.drag;if(!i){for(const l in o)if(er.has(l)){i=!0;break}}if(!i)return;let s=!t;if(t)for(let l=0;l<Fd.length;l++){const a=Fd[l];e[a]!==t[a]&&(s=!0)}s&&ne.read(()=>{zS(n,r),ne.render(()=>{jc(r,o,zc(n.tagName),e.transformTemplate),nv(n,r)})})}})},$S={useVisualState:Xg({scrapeMotionValuesFromProps:Bc,createRenderState:Fc})};function iv(e,t,n){for(const r in t)!Ne(t[r])&&!rv(r,n)&&(e[r]=t[r])}function US({transformTemplate:e},t){return y.useMemo(()=>{const n=Fc();return Ic(n,t,e),Object.assign({},n.vars,n.style)},[t])}function WS(e,t){const n=e.style||{},r={};return iv(r,n,e),Object.assign(r,US(e,t)),r}function HS(e,t){const n={},r=WS(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function KS(e,t,n,r){const o=y.useMemo(()=>{const i=Jg();return jc(i,t,zc(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};iv(i,e.style,e),o.style={...i,...o.style}}return o}function GS(e=!1){return(n,r,o,{latestValues:i},s)=>{const a=(Dc(n)?KS:HS)(r,i,s,n),u=aS(r,typeof n=="string",e),c=n!==y.Fragment?{...u,...a,ref:o}:{},{children:f}=r,d=y.useMemo(()=>Ne(f)?f.get():f,[f]);return y.createElement(n,{...c,children:d})}}function YS(e,t){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const s={...Dc(r)?BS:$S,preloadedFeatures:e,useRender:GS(o),createVisualElement:t,Component:r};return vS(s)}}function sv(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function ll(e,t,n){const r=e.getProps();return _c(r,t,n!==void 0?n:r.custom,e)}const XS=Ac(()=>window.ScrollTimeline!==void 0);class QS{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(o=>{if(XS()&&o.attachTimeline)return o.attachTimeline(t);if(typeof n=="function")return n(o)});return()=>{r.forEach((o,i)=>{o&&o(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class ZS extends QS{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function $c(e,t){return e?e[t]||e.default||e:void 0}const su=2e4;function lv(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<su;)t+=n,r=e.next(t);return t>=su?1/0:t}function Uc(e){return typeof e=="function"}function zd(e,t){e.timeline=t,e.onfinish=null}const Wc=e=>Array.isArray(e)&&typeof e[0]=="number",qS={linearEasing:void 0};function JS(e,t){const n=Ac(e);return()=>{var r;return(r=qS[t])!==null&&r!==void 0?r:n()}}const Ns=JS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),av=(e,t,n=10)=>{let r="";const o=Math.max(Math.round(t/n),2);for(let i=0;i<o;i++)r+=e(jr(0,o-1,i))+", ";return`linear(${r.substring(0,r.length-2)})`};function uv(e){return!!(typeof e=="function"&&Ns()||!e||typeof e=="string"&&(e in lu||Ns())||Wc(e)||Array.isArray(e)&&e.every(uv))}const yo=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,lu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:yo([0,.65,.55,1]),circOut:yo([.55,0,1,.45]),backIn:yo([.31,.01,.66,-.59]),backOut:yo([.33,1.53,.69,.99])};function cv(e,t){if(e)return typeof e=="function"&&Ns()?av(e,t):Wc(e)?yo(e):Array.isArray(e)?e.map(n=>cv(n,t)||lu.easeOut):lu[e]}const ft={x:!1,y:!1};function fv(){return ft.x||ft.y}function eC(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let o=document;const i=(r=void 0)!==null&&r!==void 0?r:o.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}function dv(e,t){const n=eC(e),r=new AbortController,o={passive:!0,...t,signal:r.signal};return[n,o,()=>r.abort()]}function Bd(e){return t=>{t.pointerType==="touch"||fv()||e(t)}}function tC(e,t,n={}){const[r,o,i]=dv(e,n),s=Bd(l=>{const{target:a}=l,u=t(l);if(typeof u!="function"||!a)return;const c=Bd(f=>{u(f),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,o)});return r.forEach(l=>{l.addEventListener("pointerenter",s,o)}),i}const pv=(e,t)=>t?e===t?!0:pv(e,t.parentElement):!1,Hc=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,nC=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function rC(e){return nC.has(e.tagName)||e.tabIndex!==-1}const wo=new WeakSet;function $d(e){return t=>{t.key==="Enter"&&e(t)}}function Hl(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const oC=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=$d(()=>{if(wo.has(n))return;Hl(n,"down");const o=$d(()=>{Hl(n,"up")}),i=()=>Hl(n,"cancel");n.addEventListener("keyup",o,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Ud(e){return Hc(e)&&!fv()}function iC(e,t,n={}){const[r,o,i]=dv(e,n),s=l=>{const a=l.currentTarget;if(!Ud(l)||wo.has(a))return;wo.add(a);const u=t(l),c=(v,w)=>{window.removeEventListener("pointerup",f),window.removeEventListener("pointercancel",d),!(!Ud(v)||!wo.has(a))&&(wo.delete(a),typeof u=="function"&&u(v,{success:w}))},f=v=>{c(v,n.useGlobalTarget||pv(a,v.target))},d=v=>{c(v,!1)};window.addEventListener("pointerup",f,o),window.addEventListener("pointercancel",d,o)};return r.forEach(l=>{!rC(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(n.useGlobalTarget?window:l).addEventListener("pointerdown",s,o),l.addEventListener("focus",u=>oC(u,o),o)}),i}function sC(e){return e==="x"||e==="y"?ft[e]?null:(ft[e]=!0,()=>{ft[e]=!1}):ft.x||ft.y?null:(ft.x=ft.y=!0,()=>{ft.x=ft.y=!1})}const hv=new Set(["width","height","top","left","right","bottom",...Gr]);let ts;function lC(){ts=void 0}const Dt={now:()=>(ts===void 0&&Dt.set(Ce.isProcessing||nS.useManualTiming?Ce.timestamp:performance.now()),ts),set:e=>{ts=e,queueMicrotask(lC)}};function Kc(e,t){e.indexOf(t)===-1&&e.push(t)}function Gc(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Yc{constructor(){this.subscriptions=[]}add(t){return Kc(this.subscriptions,t),()=>Gc(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function mv(e,t){return t?e*(1e3/t):0}const Wd=30,aC=e=>!isNaN(parseFloat(e));class uC{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{const i=Dt.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Dt.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=aC(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Yc);const r=this.events[t].add(n);return t==="change"?()=>{r(),ne.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Dt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Wd)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Wd);return mv(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ti(e,t){return new uC(e,t)}function cC(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ti(n))}function fC(e,t){const n=ll(e,t);let{transitionEnd:r={},transition:o={},...i}=n||{};i={...i,...r};for(const s in i){const l=PS(i[s]);cC(e,s,l)}}function dC(e){return!!(Ne(e)&&e.add)}function au(e,t){const n=e.getValue("willChange");if(dC(n))return n.add(t)}function gv(e){return e.props[Kg]}const vv=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,pC=1e-7,hC=12;function mC(e,t,n,r,o){let i,s,l=0;do s=t+(n-t)/2,i=vv(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>pC&&++l<hC);return s}function di(e,t,n,r){if(e===t&&n===r)return Qe;const o=i=>mC(i,0,1,e,n);return i=>i===0||i===1?i:vv(o(i),t,r)}const yv=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,wv=e=>t=>1-e(1-t),xv=di(.33,1.53,.69,.99),Xc=wv(xv),Sv=yv(Xc),Cv=e=>(e*=2)<1?.5*Xc(e):.5*(2-Math.pow(2,-10*(e-1))),Qc=e=>1-Math.sin(Math.acos(e)),Pv=wv(Qc),Ev=yv(Qc),Tv=e=>/^0[^.\s]+$/u.test(e);function gC(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Tv(e):!0}const Lo=e=>Math.round(e*1e5)/1e5,Zc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function vC(e){return e==null}const yC=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,qc=(e,t)=>n=>!!(typeof n=="string"&&yC.test(n)&&n.startsWith(e)||t&&!vC(n)&&Object.prototype.hasOwnProperty.call(n,t)),kv=(e,t,n)=>r=>{if(typeof r!="string")return r;const[o,i,s,l]=r.match(Zc);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},wC=e=>Xt(0,255,e),Kl={...Yr,transform:e=>Math.round(wC(e))},Fn={test:qc("rgb","red"),parse:kv("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Kl.transform(e)+", "+Kl.transform(t)+", "+Kl.transform(n)+", "+Lo(ei.transform(r))+")"};function xC(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const uu={test:qc("#"),parse:xC,transform:Fn.transform},yr={test:qc("hsl","hue"),parse:kv("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Nt.transform(Lo(t))+", "+Nt.transform(Lo(n))+", "+Lo(ei.transform(r))+")"},be={test:e=>Fn.test(e)||uu.test(e)||yr.test(e),parse:e=>Fn.test(e)?Fn.parse(e):yr.test(e)?yr.parse(e):uu.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Fn.transform(e):yr.transform(e)},SC=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function CC(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(Zc))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(SC))===null||n===void 0?void 0:n.length)||0)>0}const Rv="number",Av="color",PC="var",EC="var(",Hd="${}",TC=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ni(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let i=0;const l=t.replace(TC,a=>(be.test(a)?(r.color.push(i),o.push(Av),n.push(be.parse(a))):a.startsWith(EC)?(r.var.push(i),o.push(PC),n.push(a)):(r.number.push(i),o.push(Rv),n.push(parseFloat(a))),++i,Hd)).split(Hd);return{values:n,split:l,indexes:r,types:o}}function Mv(e){return ni(e).values}function bv(e){const{split:t,types:n}=ni(e),r=t.length;return o=>{let i="";for(let s=0;s<r;s++)if(i+=t[s],o[s]!==void 0){const l=n[s];l===Rv?i+=Lo(o[s]):l===Av?i+=be.transform(o[s]):i+=o[s]}return i}}const kC=e=>typeof e=="number"?0:e;function RC(e){const t=Mv(e);return bv(e)(t.map(kC))}const Cn={test:CC,parse:Mv,createTransformer:bv,getAnimatableNone:RC},AC=new Set(["brightness","contrast","saturate","opacity"]);function MC(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Zc)||[];if(!r)return e;const o=n.replace(r,"");let i=AC.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const bC=/\b([a-z-]*)\(.*?\)/gu,cu={...Cn,getAnimatableNone:e=>{const t=e.match(bC);return t?t.map(MC).join(" "):e}},LC={...Vc,color:be,backgroundColor:be,outlineColor:be,fill:be,stroke:be,borderColor:be,borderTopColor:be,borderRightColor:be,borderBottomColor:be,borderLeftColor:be,filter:cu,WebkitFilter:cu},Jc=e=>LC[e];function Lv(e,t){let n=Jc(e);return n!==cu&&(n=Cn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const NC=new Set(["auto","none","0"]);function DC(e,t,n){let r=0,o;for(;r<e.length&&!o;){const i=e[r];typeof i=="string"&&!NC.has(i)&&ni(i).values.length&&(o=e[r]),r++}if(o&&n)for(const i of t)e[i]=Lv(n,o)}const Kd=e=>e===Yr||e===V,Gd=(e,t)=>parseFloat(e.split(", ")[t]),Yd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/u);if(o)return Gd(o[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?Gd(i[1],e):0}},_C=new Set(["x","y","z"]),OC=Gr.filter(e=>!_C.has(e));function VC(e){const t=[];return OC.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const zr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Yd(4,13),y:Yd(5,14)};zr.translateX=zr.x;zr.translateY=zr.y;const $n=new Set;let fu=!1,du=!1;function Nv(){if(du){const e=Array.from($n).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=VC(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([i,s])=>{var l;(l=r.getValue(i))===null||l===void 0||l.set(s)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}du=!1,fu=!1,$n.forEach(e=>e.complete()),$n.clear()}function Dv(){$n.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(du=!0)})}function IC(){Dv(),Nv()}class ef{constructor(t,n,r,o,i,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=i,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?($n.add(this),fu||(fu=!0,ne.read(Dv),ne.resolveKeyframes(Nv))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const s=o==null?void 0:o.get(),l=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const a=r.readValue(n,l);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=l),o&&s===void 0&&o.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),$n.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,$n.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const _v=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),jC=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function FC(e){const t=jC.exec(e);if(!t)return[,];const[,n,r,o]=t;return[`--${n??r}`,o]}function Ov(e,t,n=1){const[r,o]=FC(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const s=i.trim();return _v(s)?parseFloat(s):s}return Oc(o)?Ov(o,t,n+1):o}const Vv=e=>t=>t.test(e),zC={test:e=>e==="auto",parse:e=>e},Iv=[Yr,V,Nt,nn,MS,AS,zC],Xd=e=>Iv.find(Vv(e));class jv extends ef{constructor(t,n,r,o,i){super(t,n,r,o,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="string"&&(u=u.trim(),Oc(u))){const c=Ov(u,n.current);c!==void 0&&(t[a]=c),a===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!hv.has(r)||t.length!==2)return;const[o,i]=t,s=Xd(o),l=Xd(i);if(s!==l)if(Kd(s)&&Kd(l))for(let a=0;a<t.length;a++){const u=t[a];typeof u=="string"&&(t[a]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)gC(t[o])&&r.push(o);r.length&&DC(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=zr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:o}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const s=o.length-1,l=o[s];o[s]=zr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([a,u])=>{n.getValue(a).set(u)}),this.resolveNoneKeyframes()}}const Qd=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Cn.test(e)||e==="0")&&!e.startsWith("url("));function BC(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function $C(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],s=Qd(o,t),l=Qd(i,t);return!s||!l?!1:BC(e)||(n==="spring"||Uc(n))&&r}const UC=e=>e!==null;function al(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(UC),i=t&&n!=="loop"&&t%2===1?0:o.length-1;return!i||r===void 0?o[i]:r}const WC=40;class Fv{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:i=0,repeatType:s="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Dt.now(),this.options={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:i,repeatType:s,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>WC?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&IC(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=Dt.now(),this.hasAttemptedResolve=!0;const{name:r,type:o,velocity:i,delay:s,onComplete:l,onUpdate:a,isGenerator:u}=this.options;if(!u&&!$C(t,r,o,i))if(s)this.options.duration=0;else{a&&a(al(t,this.options,n)),l&&l(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const se=(e,t,n)=>e+(t-e)*n;function Gl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function HC({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;o=Gl(a,l,e+1/3),i=Gl(a,l,e),s=Gl(a,l,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}function Ds(e,t){return n=>n>0?t:e}const Yl=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},KC=[uu,Fn,yr],GC=e=>KC.find(t=>t.test(e));function Zd(e){const t=GC(e);if(!t)return!1;let n=t.parse(e);return t===yr&&(n=HC(n)),n}const qd=(e,t)=>{const n=Zd(e),r=Zd(t);if(!n||!r)return Ds(e,t);const o={...n};return i=>(o.red=Yl(n.red,r.red,i),o.green=Yl(n.green,r.green,i),o.blue=Yl(n.blue,r.blue,i),o.alpha=se(n.alpha,r.alpha,i),Fn.transform(o))},YC=(e,t)=>n=>t(e(n)),pi=(...e)=>e.reduce(YC),pu=new Set(["none","hidden"]);function XC(e,t){return pu.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function QC(e,t){return n=>se(e,t,n)}function tf(e){return typeof e=="number"?QC:typeof e=="string"?Oc(e)?Ds:be.test(e)?qd:JC:Array.isArray(e)?zv:typeof e=="object"?be.test(e)?qd:ZC:Ds}function zv(e,t){const n=[...e],r=n.length,o=e.map((i,s)=>tf(i)(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}}function ZC(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=tf(e[o])(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}}function qC(e,t){var n;const r=[],o={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const s=t.types[i],l=e.indexes[s][o[s]],a=(n=e.values[l])!==null&&n!==void 0?n:0;r[i]=a,o[s]++}return r}const JC=(e,t)=>{const n=Cn.createTransformer(t),r=ni(e),o=ni(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?pu.has(e)&&!o.values.length||pu.has(t)&&!r.values.length?XC(e,t):pi(zv(qC(r,o),o.values),n):Ds(e,t)};function Bv(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?se(e,t,n):tf(e)(e,t)}const eP=5;function $v(e,t,n){const r=Math.max(t-eP,0);return mv(n-e(r),t-r)}const ue={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Xl=.001;function tP({duration:e=ue.duration,bounce:t=ue.bounce,velocity:n=ue.velocity,mass:r=ue.mass}){let o,i,s=1-t;s=Xt(ue.minDamping,ue.maxDamping,s),e=Xt(ue.minDuration,ue.maxDuration,Wt(e)),s<1?(o=u=>{const c=u*s,f=c*e,d=c-n,v=hu(u,s),w=Math.exp(-f);return Xl-d/v*w},i=u=>{const f=u*s*e,d=f*n+n,v=Math.pow(s,2)*Math.pow(u,2)*e,w=Math.exp(-f),m=hu(Math.pow(u,2),s);return(-o(u)+Xl>0?-1:1)*((d-v)*w)/m}):(o=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-Xl+c*f},i=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=rP(o,i,l);if(e=Ut(e),isNaN(a))return{stiffness:ue.stiffness,damping:ue.damping,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const nP=12;function rP(e,t,n){let r=n;for(let o=1;o<nP;o++)r=r-e(r)/t(r);return r}function hu(e,t){return e*Math.sqrt(1-t*t)}const oP=["duration","bounce"],iP=["stiffness","damping","mass"];function Jd(e,t){return t.some(n=>e[n]!==void 0)}function sP(e){let t={velocity:ue.velocity,stiffness:ue.stiffness,damping:ue.damping,mass:ue.mass,isResolvedFromDuration:!1,...e};if(!Jd(e,iP)&&Jd(e,oP))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),o=r*r,i=2*Xt(.05,1,1-(e.bounce||0))*Math.sqrt(o);t={...t,mass:ue.mass,stiffness:o,damping:i}}else{const n=tP(e);t={...t,...n,mass:ue.mass},t.isResolvedFromDuration=!0}return t}function Uv(e=ue.visualDuration,t=ue.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:o}=n;const i=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],l={done:!1,value:i},{stiffness:a,damping:u,mass:c,duration:f,velocity:d,isResolvedFromDuration:v}=sP({...n,velocity:-Wt(n.velocity||0)}),w=d||0,m=u/(2*Math.sqrt(a*c)),x=s-i,h=Wt(Math.sqrt(a/c)),p=Math.abs(x)<5;r||(r=p?ue.restSpeed.granular:ue.restSpeed.default),o||(o=p?ue.restDelta.granular:ue.restDelta.default);let g;if(m<1){const C=hu(h,m);g=E=>{const P=Math.exp(-m*h*E);return s-P*((w+m*h*x)/C*Math.sin(C*E)+x*Math.cos(C*E))}}else if(m===1)g=C=>s-Math.exp(-h*C)*(x+(w+h*x)*C);else{const C=h*Math.sqrt(m*m-1);g=E=>{const P=Math.exp(-m*h*E),T=Math.min(C*E,300);return s-P*((w+m*h*x)*Math.sinh(T)+C*x*Math.cosh(T))/C}}const S={calculatedDuration:v&&f||null,next:C=>{const E=g(C);if(v)l.done=C>=f;else{let P=0;m<1&&(P=C===0?Ut(w):$v(g,C,E));const T=Math.abs(P)<=r,L=Math.abs(s-E)<=o;l.done=T&&L}return l.value=l.done?s:E,l},toString:()=>{const C=Math.min(lv(S),su),E=av(P=>S.next(C*P).value,C,30);return C+"ms "+E}};return S}function ep({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:c}){const f=e[0],d={done:!1,value:f},v=T=>l!==void 0&&T<l||a!==void 0&&T>a,w=T=>l===void 0?a:a===void 0||Math.abs(l-T)<Math.abs(a-T)?l:a;let m=n*t;const x=f+m,h=s===void 0?x:s(x);h!==x&&(m=h-f);const p=T=>-m*Math.exp(-T/r),g=T=>h+p(T),S=T=>{const L=p(T),M=g(T);d.done=Math.abs(L)<=u,d.value=d.done?h:M};let C,E;const P=T=>{v(d.value)&&(C=T,E=Uv({keyframes:[d.value,w(d.value)],velocity:$v(g,T,d.value),damping:o,stiffness:i,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:T=>{let L=!1;return!E&&C===void 0&&(L=!0,S(T),P(T)),C!==void 0&&T>=C?E.next(T-C):(!L&&S(T),d)}}}const lP=di(.42,0,1,1),aP=di(0,0,.58,1),Wv=di(.42,0,.58,1),uP=e=>Array.isArray(e)&&typeof e[0]!="number",cP={linear:Qe,easeIn:lP,easeInOut:Wv,easeOut:aP,circIn:Qc,circInOut:Ev,circOut:Pv,backIn:Xc,backInOut:Sv,backOut:xv,anticipate:Cv},tp=e=>{if(Wc(e)){Bg(e.length===4);const[t,n,r,o]=e;return di(t,n,r,o)}else if(typeof e=="string")return cP[e];return e};function fP(e,t,n){const r=[],o=n||Bv,i=e.length-1;for(let s=0;s<i;s++){let l=o(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||Qe:t;l=pi(a,l)}r.push(l)}return r}function dP(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(Bg(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const s=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=fP(t,r,o),a=l.length,u=c=>{if(s&&c<e[0])return t[0];let f=0;if(a>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const d=jr(e[f],e[f+1],c);return l[f](d)};return n?c=>u(Xt(e[0],e[i-1],c)):u}function pP(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=jr(0,t,r);e.push(se(n,1,o))}}function hP(e){const t=[0];return pP(t,e.length-1),t}function mP(e,t){return e.map(n=>n*t)}function gP(e,t){return e.map(()=>t||Wv).splice(0,e.length-1)}function _s({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=uP(r)?r.map(tp):tp(r),i={done:!1,value:t[0]},s=mP(n&&n.length===t.length?n:hP(t),e),l=dP(s,t,{ease:Array.isArray(o)?o:gP(t,o)});return{calculatedDuration:e,next:a=>(i.value=l(a),i.done=a>=e,i)}}const vP=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ne.update(t,!0),stop:()=>Sn(t),now:()=>Ce.isProcessing?Ce.timestamp:Dt.now()}},yP={decay:ep,inertia:ep,tween:_s,keyframes:_s,spring:Uv},wP=e=>e/100;class nf extends Fv{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:n,motionValue:r,element:o,keyframes:i}=this.options,s=(o==null?void 0:o.KeyframeResolver)||ef,l=(a,u)=>this.onKeyframesResolved(a,u);this.resolver=new s(i,l,n,r,o),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i,velocity:s=0}=this.options,l=Uc(n)?n:yP[n]||_s;let a,u;l!==_s&&typeof t[0]!="number"&&(a=pi(wP,Bv(t[0],t[1])),t=[0,100]);const c=l({...this.options,keyframes:t});i==="mirror"&&(u=l({...this.options,keyframes:[...t].reverse(),velocity:-s})),c.calculatedDuration===null&&(c.calculatedDuration=lv(c));const{calculatedDuration:f}=c,d=f+o,v=d*(r+1)-o;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:a,calculatedDuration:f,resolvedDuration:d,totalDuration:v}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:T}=this.options;return{done:!0,value:T[T.length-1]}}const{finalKeyframe:o,generator:i,mirroredGenerator:s,mapPercentToKeyframes:l,keyframes:a,calculatedDuration:u,totalDuration:c,resolvedDuration:f}=r;if(this.startTime===null)return i.next(0);const{delay:d,repeat:v,repeatType:w,repeatDelay:m,onUpdate:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const h=this.currentTime-d*(this.speed>=0?1:-1),p=this.speed>=0?h<0:h>c;this.currentTime=Math.max(h,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let g=this.currentTime,S=i;if(v){const T=Math.min(this.currentTime,c)/f;let L=Math.floor(T),M=T%1;!M&&T>=1&&(M=1),M===1&&L--,L=Math.min(L,v+1),!!(L%2)&&(w==="reverse"?(M=1-M,m&&(M-=m/f)):w==="mirror"&&(S=s)),g=Xt(0,1,M)*f}const C=p?{done:!1,value:a[0]}:S.next(g);l&&(C.value=l(C.value));let{done:E}=C;!p&&u!==null&&(E=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const P=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&E);return P&&o!==void 0&&(C.value=al(a,this.options,o)),x&&x(C.value),P&&this.finish(),C}get duration(){const{resolved:t}=this;return t?Wt(t.calculatedDuration):0}get time(){return Wt(this.currentTime)}set time(t){t=Ut(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Wt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=vP,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const o=this.driver.now();this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=o):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const xP=new Set(["opacity","clipPath","filter","transform"]);function SP(e,t,n,{delay:r=0,duration:o=300,repeat:i=0,repeatType:s="loop",ease:l="easeInOut",times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=cv(l,o);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:o,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}const CP=Ac(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Os=10,PP=2e4;function EP(e){return Uc(e.type)||e.type==="spring"||!uv(e.ease)}function TP(e,t){const n=new nf({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const o=[];let i=0;for(;!r.done&&i<PP;)r=n.sample(i),o.push(r.value),i+=Os;return{times:void 0,keyframes:o,duration:i-Os,ease:"linear"}}const Hv={anticipate:Cv,backInOut:Sv,circInOut:Ev};function kP(e){return e in Hv}class np extends Fv{constructor(t){super(t);const{name:n,motionValue:r,element:o,keyframes:i}=this.options;this.resolver=new jv(i,(s,l)=>this.onKeyframesResolved(s,l),n,r,o),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:o,ease:i,type:s,motionValue:l,name:a,startTime:u}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof i=="string"&&Ns()&&kP(i)&&(i=Hv[i]),EP(this.options)){const{onComplete:f,onUpdate:d,motionValue:v,element:w,...m}=this.options,x=TP(t,m);t=x.keyframes,t.length===1&&(t[1]=t[0]),r=x.duration,o=x.times,i=x.ease,s="keyframes"}const c=SP(l.owner.current,a,t,{...this.options,duration:r,times:o,ease:i});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(zd(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:f}=this.options;l.set(al(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:o,type:s,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return Wt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return Wt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Ut(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Qe;const{animation:r}=n;zd(r,t)}return Qe}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:o,type:i,ease:s,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:f,element:d,...v}=this.options,w=new nf({...v,keyframes:r,duration:o,type:i,ease:s,times:l,isGenerator:!0}),m=Ut(this.time);u.setWithVelocity(w.sample(m-Os).value,w.sample(m).value,Os)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:o,repeatType:i,damping:s,type:l}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:u}=n.owner.getProps();return CP()&&r&&xP.has(r)&&!a&&!u&&!o&&i!=="mirror"&&s!==0&&l!=="inertia"}}const RP={type:"spring",stiffness:500,damping:25,restSpeed:10},AP=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),MP={type:"keyframes",duration:.8},bP={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},LP=(e,{keyframes:t})=>t.length>2?MP:er.has(e)?e.startsWith("scale")?AP(t[1]):RP:bP;function NP({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}const rf=(e,t,n,r={},o,i)=>s=>{const l=$c(r,e)||{},a=l.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Ut(a);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:d=>{t.set(d),l.onUpdate&&l.onUpdate(d)},onComplete:()=>{s(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:i?void 0:o};NP(l)||(c={...c,...LP(e,c)}),c.duration&&(c.duration=Ut(c.duration)),c.repeatDelay&&(c.repeatDelay=Ut(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(f=!0)),f&&!i&&t.get()!==void 0){const d=al(c.keyframes,l);if(d!==void 0)return ne.update(()=>{c.onUpdate(d),c.onComplete()}),new ZS([])}return!i&&np.supports(c)?new np(c):new nf(c)};function DP({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Kv(e,t,{delay:n=0,transitionOverride:r,type:o}={}){var i;let{transition:s=e.getDefaultTransition(),transitionEnd:l,...a}=t;r&&(s=r);const u=[],c=o&&e.animationState&&e.animationState.getState()[o];for(const f in a){const d=e.getValue(f,(i=e.latestValues[f])!==null&&i!==void 0?i:null),v=a[f];if(v===void 0||c&&DP(c,f))continue;const w={delay:n,...$c(s||{},f)};let m=!1;if(window.MotionHandoffAnimation){const h=gv(e);if(h){const p=window.MotionHandoffAnimation(h,f,ne);p!==null&&(w.startTime=p,m=!0)}}au(e,f),d.start(rf(f,d,v,e.shouldReduceMotion&&hv.has(f)?{type:!1}:w,e,m));const x=d.animation;x&&u.push(x)}return l&&Promise.all(u).then(()=>{ne.update(()=>{l&&fC(e,l)})}),u}function mu(e,t,n={}){var r;const o=ll(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(i=n.transitionOverride);const s=o?()=>Promise.all(Kv(e,o,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=i;return _P(e,t,c+u,f,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[u,c]=a==="beforeChildren"?[s,l]:[l,s];return u().then(()=>c())}else return Promise.all([s(),l(n.delay)])}function _P(e,t,n=0,r=0,o=1,i){const s=[],l=(e.variantChildren.size-1)*r,a=o===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(OP).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(mu(u,t,{...i,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function OP(e,t){return e.sortNodePosition(t)}function VP(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>mu(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=mu(e,t,n);else{const o=typeof t=="function"?ll(e,t,n.custom):t;r=Promise.all(Kv(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const IP=bc.length;function Gv(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Gv(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<IP;n++){const r=bc[n],o=e.props[r];(Jo(o)||o===!1)&&(t[r]=o)}return t}const jP=[...Mc].reverse(),FP=Mc.length;function zP(e){return t=>Promise.all(t.map(({animation:n,options:r})=>VP(e,n,r)))}function BP(e){let t=zP(e),n=rp(),r=!0;const o=a=>(u,c)=>{var f;const d=ll(e,c,a==="exit"?(f=e.presenceContext)===null||f===void 0?void 0:f.custom:void 0);if(d){const{transition:v,transitionEnd:w,...m}=d;u={...u,...m,...w}}return u};function i(a){t=a(e)}function s(a){const{props:u}=e,c=Gv(e.parent)||{},f=[],d=new Set;let v={},w=1/0;for(let x=0;x<FP;x++){const h=jP[x],p=n[h],g=u[h]!==void 0?u[h]:c[h],S=Jo(g),C=h===a?p.isActive:null;C===!1&&(w=x);let E=g===c[h]&&g!==u[h]&&S;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),p.protectedKeys={...v},!p.isActive&&C===null||!g&&!p.prevProp||il(g)||typeof g=="boolean")continue;const P=$P(p.prevProp,g);let T=P||h===a&&p.isActive&&!E&&S||x>w&&S,L=!1;const M=Array.isArray(g)?g:[g];let j=M.reduce(o(h),{});C===!1&&(j={});const{prevResolvedValues:_={}}=p,U={..._,...j},z=O=>{T=!0,d.has(O)&&(L=!0,d.delete(O)),p.needsAnimating[O]=!0;const R=e.getValue(O);R&&(R.liveStyle=!1)};for(const O in U){const R=j[O],b=_[O];if(v.hasOwnProperty(O))continue;let D=!1;iu(R)&&iu(b)?D=!sv(R,b):D=R!==b,D?R!=null?z(O):d.add(O):R!==void 0&&d.has(O)?z(O):p.protectedKeys[O]=!0}p.prevProp=g,p.prevResolvedValues=j,p.isActive&&(v={...v,...j}),r&&e.blockInitialAnimation&&(T=!1),T&&(!(E&&P)||L)&&f.push(...M.map(O=>({animation:O,options:{type:h}})))}if(d.size){const x={};d.forEach(h=>{const p=e.getBaseTarget(h),g=e.getValue(h);g&&(g.liveStyle=!0),x[h]=p??null}),f.push({animation:x})}let m=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(m=!1),r=!1,m?t(f):Promise.resolve()}function l(a,u){var c;if(n[a].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(d=>{var v;return(v=d.animationState)===null||v===void 0?void 0:v.setActive(a,u)}),n[a].isActive=u;const f=s(a);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:s,setActive:l,setAnimateFunction:i,getState:()=>n,reset:()=>{n=rp(),r=!0}}}function $P(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!sv(t,e):!1}function Ln(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rp(){return{animate:Ln(!0),whileInView:Ln(),whileHover:Ln(),whileTap:Ln(),whileDrag:Ln(),whileFocus:Ln(),exit:Ln()}}class An{constructor(t){this.isMounted=!1,this.node=t}update(){}}class UP extends An{constructor(t){super(t),t.animationState||(t.animationState=BP(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();il(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let WP=0;class HP extends An{constructor(){super(...arguments),this.id=WP++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const KP={animation:{Feature:UP},exit:{Feature:HP}};function ri(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function hi(e){return{point:{x:e.pageX,y:e.pageY}}}const GP=e=>t=>Hc(t)&&e(t,hi(t));function No(e,t,n,r){return ri(e,t,GP(n),r)}const op=(e,t)=>Math.abs(e-t);function YP(e,t){const n=op(e.x,t.x),r=op(e.y,t.y);return Math.sqrt(n**2+r**2)}class Yv{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=Zl(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,v=YP(f.offset,{x:0,y:0})>=3;if(!d&&!v)return;const{point:w}=f,{timestamp:m}=Ce;this.history.push({...w,timestamp:m});const{onStart:x,onMove:h}=this.handlers;d||(x&&x(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=Ql(d,this.transformPagePoint),ne.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:v,onSessionEnd:w,resumeAnimation:m}=this.handlers;if(this.dragSnapToOrigin&&m&&m(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=Zl(f.type==="pointercancel"?this.lastMoveEventInfo:Ql(d,this.transformPagePoint),this.history);this.startEvent&&v&&v(f,x),w&&w(f,x)},!Hc(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const s=hi(t),l=Ql(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=Ce;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Zl(l,this.history)),this.removeListeners=pi(No(this.contextWindow,"pointermove",this.handlePointerMove),No(this.contextWindow,"pointerup",this.handlePointerUp),No(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Sn(this.updatePoint)}}function Ql(e,t){return t?{point:t(e.point)}:e}function ip(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Zl({point:e},t){return{point:e,delta:ip(e,Xv(t)),offset:ip(e,XP(t)),velocity:QP(t,.1)}}function XP(e){return e[0]}function Xv(e){return e[e.length-1]}function QP(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=Xv(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>Ut(t)));)n--;if(!r)return{x:0,y:0};const i=Wt(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}const Qv=1e-4,ZP=1-Qv,qP=1+Qv,Zv=.01,JP=0-Zv,eE=0+Zv;function Je(e){return e.max-e.min}function tE(e,t,n){return Math.abs(e-t)<=n}function sp(e,t,n,r=.5){e.origin=r,e.originPoint=se(t.min,t.max,e.origin),e.scale=Je(n)/Je(t),e.translate=se(n.min,n.max,e.origin)-e.originPoint,(e.scale>=ZP&&e.scale<=qP||isNaN(e.scale))&&(e.scale=1),(e.translate>=JP&&e.translate<=eE||isNaN(e.translate))&&(e.translate=0)}function Do(e,t,n,r){sp(e.x,t.x,n.x,r?r.originX:void 0),sp(e.y,t.y,n.y,r?r.originY:void 0)}function lp(e,t,n){e.min=n.min+t.min,e.max=e.min+Je(t)}function nE(e,t,n){lp(e.x,t.x,n.x),lp(e.y,t.y,n.y)}function ap(e,t,n){e.min=t.min-n.min,e.max=e.min+Je(t)}function _o(e,t,n){ap(e.x,t.x,n.x),ap(e.y,t.y,n.y)}function rE(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?se(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?se(n,e,r.max):Math.min(e,n)),e}function up(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function oE(e,{top:t,left:n,bottom:r,right:o}){return{x:up(e.x,n,o),y:up(e.y,t,r)}}function cp(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function iE(e,t){return{x:cp(e.x,t.x),y:cp(e.y,t.y)}}function sE(e,t){let n=.5;const r=Je(e),o=Je(t);return o>r?n=jr(t.min,t.max-r,e.min):r>o&&(n=jr(e.min,e.max-o,t.min)),Xt(0,1,n)}function lE(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const gu=.35;function aE(e=gu){return e===!1?e=0:e===!0&&(e=gu),{x:fp(e,"left","right"),y:fp(e,"top","bottom")}}function fp(e,t,n){return{min:dp(e,t),max:dp(e,n)}}function dp(e,t){return typeof e=="number"?e:e[t]||0}const pp=()=>({translate:0,scale:1,origin:0,originPoint:0}),wr=()=>({x:pp(),y:pp()}),hp=()=>({min:0,max:0}),fe=()=>({x:hp(),y:hp()});function rt(e){return[e("x"),e("y")]}function qv({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function uE({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function cE(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function ql(e){return e===void 0||e===1}function vu({scale:e,scaleX:t,scaleY:n}){return!ql(e)||!ql(t)||!ql(n)}function _n(e){return vu(e)||Jv(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Jv(e){return mp(e.x)||mp(e.y)}function mp(e){return e&&e!=="0%"}function Vs(e,t,n){const r=e-n,o=t*r;return n+o}function gp(e,t,n,r,o){return o!==void 0&&(e=Vs(e,o,r)),Vs(e,n,r)+t}function yu(e,t=0,n=1,r,o){e.min=gp(e.min,t,n,r,o),e.max=gp(e.max,t,n,r,o)}function ey(e,{x:t,y:n}){yu(e.x,t.translate,t.scale,t.originPoint),yu(e.y,n.translate,n.scale,n.originPoint)}const vp=.999999999999,yp=1.0000000000001;function fE(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,s;for(let l=0;l<o;l++){i=n[l],s=i.projectionDelta;const{visualElement:a}=i.options;a&&a.props.style&&a.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Sr(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,ey(e,s)),r&&_n(i.latestValues)&&Sr(e,i.latestValues))}t.x<yp&&t.x>vp&&(t.x=1),t.y<yp&&t.y>vp&&(t.y=1)}function xr(e,t){e.min=e.min+t,e.max=e.max+t}function wp(e,t,n,r,o=.5){const i=se(e.min,e.max,o);yu(e,t,n,i,r)}function Sr(e,t){wp(e.x,t.x,t.scaleX,t.scale,t.originX),wp(e.y,t.y,t.scaleY,t.scale,t.originY)}function ty(e,t){return qv(cE(e.getBoundingClientRect(),t))}function dE(e,t,n){const r=ty(e,n),{scroll:o}=t;return o&&(xr(r.x,o.offset.x),xr(r.y,o.offset.y)),r}const ny=({current:e})=>e?e.ownerDocument.defaultView:null,pE=new WeakMap;class hE{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=fe(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(hi(c).point)},i=(c,f)=>{const{drag:d,dragPropagation:v,onDragStart:w}=this.getProps();if(d&&!v&&(this.openDragLock&&this.openDragLock(),this.openDragLock=sC(d),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rt(x=>{let h=this.getAxisMotionValue(x).get()||0;if(Nt.test(h)){const{projection:p}=this.visualElement;if(p&&p.layout){const g=p.layout.layoutBox[x];g&&(h=Je(g)*(parseFloat(h)/100))}}this.originPoint[x]=h}),w&&ne.postRender(()=>w(c,f)),au(this.visualElement,"transform");const{animationState:m}=this.visualElement;m&&m.setActive("whileDrag",!0)},s=(c,f)=>{const{dragPropagation:d,dragDirectionLock:v,onDirectionLock:w,onDrag:m}=this.getProps();if(!d&&!this.openDragLock)return;const{offset:x}=f;if(v&&this.currentDirection===null){this.currentDirection=mE(x),this.currentDirection!==null&&w&&w(this.currentDirection);return}this.updateAxis("x",f.point,x),this.updateAxis("y",f.point,x),this.visualElement.render(),m&&m(c,f)},l=(c,f)=>this.stop(c,f),a=()=>rt(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Yv(t,{onSessionStart:o,onStart:i,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:ny(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:i}=this.getProps();i&&ne.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!Vi(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=rE(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&vr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=oE(o.layoutBox,n):this.constraints=!1,this.elastic=aE(r),i!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&rt(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=lE(o.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!vr(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=dE(r,o.root,this.visualElement.getTransformPagePoint());let s=iE(o.layout.layoutBox,i);if(n){const l=n(uE(s));this.hasMutatedConstraints=!!l,l&&(s=qv(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=rt(c=>{if(!Vi(c,n,this.currentDirection))return;let f=a&&a[c]||{};s&&(f={min:0,max:0});const d=o?200:1e6,v=o?40:1e7,w={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:v,timeConstant:750,restDelta:1,restSpeed:10,...i,...f};return this.startAxisValueAnimation(c,w)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return au(this.visualElement,t),r.start(rf(t,r,0,n,this.visualElement,!1))}stopAnimation(){rt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rt(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){rt(n=>{const{drag:r}=this.getProps();if(!Vi(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:l}=o.layout.layoutBox[n];i.set(t[n]-se(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!vr(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};rt(s=>{const l=this.getAxisMotionValue(s);if(l&&this.constraints!==!1){const a=l.get();o[s]=sE({min:a,max:a},this.constraints[s])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rt(s=>{if(!Vi(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(se(a,u,o[s]))})}addListeners(){if(!this.visualElement.current)return;pE.set(this.visualElement,this);const t=this.visualElement.current,n=No(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();vr(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),ne.read(r);const s=ri(window,"resize",()=>this.scalePositionWithinConstraints()),l=o.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(rt(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=a[c].translate,f.set(f.get()+a[c].translate))}),this.visualElement.render())});return()=>{s(),n(),i(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=gu,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:l}}}function Vi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function mE(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class gE extends An{constructor(t){super(t),this.removeGroupControls=Qe,this.removeListeners=Qe,this.controls=new hE(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Qe}unmount(){this.removeGroupControls(),this.removeListeners()}}const xp=e=>(t,n)=>{e&&ne.postRender(()=>e(t,n))};class vE extends An{constructor(){super(...arguments),this.removePointerDownListener=Qe}onPointerDown(t){this.session=new Yv(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ny(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:xp(t),onStart:xp(n),onMove:r,onEnd:(i,s)=>{delete this.session,o&&ne.postRender(()=>o(i,s))}}}mount(){this.removePointerDownListener=No(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const ns={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Sp(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const po={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(V.test(e))e=parseFloat(e);else return e;const n=Sp(e,t.target.x),r=Sp(e,t.target.y);return`${n}% ${r}%`}},yE={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=Cn.parse(e);if(o.length>5)return r;const i=Cn.createTransformer(e),s=typeof o[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;o[0+s]/=l,o[1+s]/=a;const u=se(l,a,.5);return typeof o[2+s]=="number"&&(o[2+s]/=u),typeof o[3+s]=="number"&&(o[3+s]/=u),i(o)}};class wE extends y.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;FS(xE),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),ns.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||ne.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Nc.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ry(e){const[t,n]=eS(),r=y.useContext(Fg);return k.jsx(wE,{...e,layoutGroup:r,switchLayoutGroup:y.useContext(Gg),isPresent:t,safeToRemove:n})}const xE={borderRadius:{...po,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:po,borderTopRightRadius:po,borderBottomLeftRadius:po,borderBottomRightRadius:po,boxShadow:yE};function SE(e,t,n){const r=Ne(e)?e:ti(e);return r.start(rf("",r,t,n)),r.animation}function CE(e){return e instanceof SVGElement&&e.tagName!=="svg"}const PE=(e,t)=>e.depth-t.depth;class EE{constructor(){this.children=[],this.isDirty=!1}add(t){Kc(this.children,t),this.isDirty=!0}remove(t){Gc(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(PE),this.isDirty=!1,this.children.forEach(t)}}function TE(e,t){const n=Dt.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(Sn(r),e(i-t))};return ne.read(r,!0),()=>Sn(r)}const oy=["TopLeft","TopRight","BottomLeft","BottomRight"],kE=oy.length,Cp=e=>typeof e=="string"?parseFloat(e):e,Pp=e=>typeof e=="number"||V.test(e);function RE(e,t,n,r,o,i){o?(e.opacity=se(0,n.opacity!==void 0?n.opacity:1,AE(r)),e.opacityExit=se(t.opacity!==void 0?t.opacity:1,0,ME(r))):i&&(e.opacity=se(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<kE;s++){const l=`border${oy[s]}Radius`;let a=Ep(t,l),u=Ep(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Pp(a)===Pp(u)?(e[l]=Math.max(se(Cp(a),Cp(u),r),0),(Nt.test(u)||Nt.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=se(t.rotate||0,n.rotate||0,r))}function Ep(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const AE=iy(0,.5,Pv),ME=iy(.5,.95,Qe);function iy(e,t,n){return r=>r<e?0:r>t?1:n(jr(e,t,r))}function Tp(e,t){e.min=t.min,e.max=t.max}function nt(e,t){Tp(e.x,t.x),Tp(e.y,t.y)}function kp(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Rp(e,t,n,r,o){return e-=t,e=Vs(e,1/n,r),o!==void 0&&(e=Vs(e,1/o,r)),e}function bE(e,t=0,n=1,r=.5,o,i=e,s=e){if(Nt.test(t)&&(t=parseFloat(t),t=se(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=se(i.min,i.max,r);e===i&&(l-=t),e.min=Rp(e.min,t,n,l,o),e.max=Rp(e.max,t,n,l,o)}function Ap(e,t,[n,r,o],i,s){bE(e,t[n],t[r],t[o],t.scale,i,s)}const LE=["x","scaleX","originX"],NE=["y","scaleY","originY"];function Mp(e,t,n,r){Ap(e.x,t,LE,n?n.x:void 0,r?r.x:void 0),Ap(e.y,t,NE,n?n.y:void 0,r?r.y:void 0)}function bp(e){return e.translate===0&&e.scale===1}function sy(e){return bp(e.x)&&bp(e.y)}function Lp(e,t){return e.min===t.min&&e.max===t.max}function DE(e,t){return Lp(e.x,t.x)&&Lp(e.y,t.y)}function Np(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function ly(e,t){return Np(e.x,t.x)&&Np(e.y,t.y)}function Dp(e){return Je(e.x)/Je(e.y)}function _p(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class _E{constructor(){this.members=[]}add(t){Kc(this.members,t),t.scheduleRender()}remove(t){if(Gc(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function OE(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y,s=(n==null?void 0:n.z)||0;if((o||i||s)&&(r=`translate3d(${o}px, ${i}px, ${s}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:f,rotateY:d,skewX:v,skewY:w}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),f&&(r+=`rotateX(${f}deg) `),d&&(r+=`rotateY(${d}deg) `),v&&(r+=`skewX(${v}deg) `),w&&(r+=`skewY(${w}deg) `)}const l=e.x.scale*t.x,a=e.y.scale*t.y;return(l!==1||a!==1)&&(r+=`scale(${l}, ${a})`),r||"none"}const On={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},xo=typeof window<"u"&&window.MotionDebug!==void 0,Jl=["","X","Y","Z"],VE={visibility:"hidden"},Op=1e3;let IE=0;function ea(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function ay(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=gv(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",ne,!(o||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&ay(r)}function uy({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},l=t==null?void 0:t()){this.id=IE++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,xo&&(On.totalNodes=On.resolvedTargetDeltas=On.recalculatedProjection=0),this.nodes.forEach(zE),this.nodes.forEach(HE),this.nodes.forEach(KE),this.nodes.forEach(BE),xo&&window.MotionDebug.record(On)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new EE)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Yc),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=CE(s),this.instance=s;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const d=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=TE(d,250),ns.hasAnimatedSinceResize&&(ns.hasAnimatedSinceResize=!1,this.nodes.forEach(Ip))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:v,layout:w})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||c.getDefaultTransition()||ZE,{onLayoutAnimationStart:x,onLayoutAnimationComplete:h}=c.getProps(),p=!this.targetLayout||!ly(this.targetLayout,w)||v,g=!d&&v;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||d&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,g);const S={...$c(m,"layout"),onPlay:x,onComplete:h};(c.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else d||Ip(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=w})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Sn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(GE),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ay(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Vp);return}this.isUpdating||this.nodes.forEach(UE),this.isUpdating=!1,this.nodes.forEach(WE),this.nodes.forEach(jE),this.nodes.forEach(FE),this.clearAllSnapshots();const l=Dt.now();Ce.delta=Xt(0,1e3/60,l-Ce.timestamp),Ce.timestamp=l,Ce.isProcessing=!0,Wl.update.process(Ce),Wl.preRender.process(Ce),Wl.render.process(Ce),Ce.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Nc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach($E),this.sharedNodes.forEach(YE)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ne.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ne.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=fe(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l){const a=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!sy(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(l||_n(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),qE(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:l}=this.options;if(!l)return fe();const a=l.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(JE))){const{scroll:c}=this.root;c&&(xr(a.x,c.offset.x),xr(a.y,c.offset.y))}return a}removeElementScroll(s){var l;const a=fe();if(nt(a,s),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:f,options:d}=c;c!==this.root&&f&&d.layoutScroll&&(f.wasRoot&&nt(a,s),xr(a.x,f.offset.x),xr(a.y,f.offset.y))}return a}applyTransform(s,l=!1){const a=fe();nt(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Sr(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),_n(c.latestValues)&&Sr(a,c.latestValues)}return _n(this.latestValues)&&Sr(a,this.latestValues),a}removeTransform(s){const l=fe();nt(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!_n(u.latestValues))continue;vu(u.latestValues)&&u.updateSnapshot();const c=fe(),f=u.measurePageBox();nt(c,f),Mp(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return _n(this.latestValues)&&Mp(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ce.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=Ce.timestamp,!this.targetDelta&&!this.relativeTarget){const v=this.getClosestProjectingParent();v&&v.layout&&this.animationProgress!==1?(this.relativeParent=v,this.forceRelativeParentToResolveTarget(),this.relativeTarget=fe(),this.relativeTargetOrigin=fe(),_o(this.relativeTargetOrigin,this.layout.layoutBox,v.layout.layoutBox),nt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=fe(),this.targetWithTransforms=fe()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),nE(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nt(this.target,this.layout.layoutBox),ey(this.target,this.targetDelta)):nt(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const v=this.getClosestProjectingParent();v&&!!v.resumingFrom==!!this.resumingFrom&&!v.options.layoutScroll&&v.target&&this.animationProgress!==1?(this.relativeParent=v,this.forceRelativeParentToResolveTarget(),this.relativeTarget=fe(),this.relativeTargetOrigin=fe(),_o(this.relativeTargetOrigin,this.target,v.target),nt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}xo&&On.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||vu(this.parent.latestValues)||Jv(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Ce.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;nt(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,v=this.treeScale.y;fE(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=fe());const{target:w}=l;if(!w){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(kp(this.prevProjectionDelta.x,this.projectionDelta.x),kp(this.prevProjectionDelta.y,this.projectionDelta.y)),Do(this.projectionDelta,this.layoutCorrected,w,this.latestValues),(this.treeScale.x!==d||this.treeScale.y!==v||!_p(this.projectionDelta.x,this.prevProjectionDelta.x)||!_p(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",w)),xo&&On.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=wr(),this.projectionDelta=wr(),this.projectionDeltaWithTransform=wr()}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},f=wr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const d=fe(),v=a?a.source:void 0,w=this.layout?this.layout.source:void 0,m=v!==w,x=this.getStack(),h=!x||x.members.length<=1,p=!!(m&&!h&&this.options.crossfade===!0&&!this.path.some(QE));this.animationProgress=0;let g;this.mixTargetDelta=S=>{const C=S/1e3;jp(f.x,s.x,C),jp(f.y,s.y,C),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(_o(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),XE(this.relativeTarget,this.relativeTargetOrigin,d,C),g&&DE(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=fe()),nt(g,this.relativeTarget)),m&&(this.animationValues=c,RE(c,u,this.latestValues,C,p,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Sn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ne.update(()=>{ns.hasAnimatedSinceResize=!0,this.currentAnimation=SE(0,Op,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Op),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&cy(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||fe();const f=Je(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+f;const d=Je(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+d}nt(l,a),Sr(l,c),Do(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new _E),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(l=!0),!l)return;const u={};a.z&&ea("z",s,u,this.animationValues);for(let c=0;c<Jl.length;c++)ea(`rotate${Jl[c]}`,s,u,this.animationValues),ea(`skew${Jl[c]}`,s,u,this.animationValues);s.render();for(const c in u)s.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return VE;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=es(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const m={};return this.options.layoutId&&(m.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,m.pointerEvents=es(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!_n(this.latestValues)&&(m.transform=c?c({},""):"none",this.hasProjected=!1),m}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=OE(this.projectionDeltaWithTransform,this.treeScale,d),c&&(u.transform=c(d,u.transform));const{x:v,y:w}=this.projectionDelta;u.transformOrigin=`${v.origin*100}% ${w.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=d.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const m in Ls){if(d[m]===void 0)continue;const{correct:x,applyTo:h}=Ls[m],p=u.transform==="none"?d[m]:x(d[m],f);if(h){const g=h.length;for(let S=0;S<g;S++)u[h[S]]=p}else u[m]=p}return this.options.layoutId&&(u.pointerEvents=f===this?es(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Vp),this.root.sharedNodes.clear()}}}function jE(e){e.updateLayout()}function FE(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,s=n.source!==e.layout.source;i==="size"?rt(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],v=Je(d);d.min=r[f].min,d.max=d.min+v}):cy(i,n.layoutBox,r)&&rt(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],v=Je(r[f]);d.max=d.min+v,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+v)});const l=wr();Do(l,r,n.layoutBox);const a=wr();s?Do(a,e.applyTransform(o,!0),n.measuredBox):Do(a,r,n.layoutBox);const u=!sy(l);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:v}=f;if(d&&v){const w=fe();_o(w,n.layoutBox,d.layoutBox);const m=fe();_o(m,r,v.layoutBox),ly(w,m)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=m,e.relativeTargetOrigin=w,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function zE(e){xo&&On.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function BE(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function $E(e){e.clearSnapshot()}function Vp(e){e.clearMeasurements()}function UE(e){e.isLayoutDirty=!1}function WE(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ip(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function HE(e){e.resolveTargetDelta()}function KE(e){e.calcProjection()}function GE(e){e.resetSkewAndRotation()}function YE(e){e.removeLeadSnapshot()}function jp(e,t,n){e.translate=se(t.translate,0,n),e.scale=se(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Fp(e,t,n,r){e.min=se(t.min,n.min,r),e.max=se(t.max,n.max,r)}function XE(e,t,n,r){Fp(e.x,t.x,n.x,r),Fp(e.y,t.y,n.y,r)}function QE(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const ZE={duration:.45,ease:[.4,0,.1,1]},zp=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Bp=zp("applewebkit/")&&!zp("chrome/")?Math.round:Qe;function $p(e){e.min=Bp(e.min),e.max=Bp(e.max)}function qE(e){$p(e.x),$p(e.y)}function cy(e,t,n){return e==="position"||e==="preserve-aspect"&&!tE(Dp(t),Dp(n),.2)}function JE(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const eT=uy({attachResizeListener:(e,t)=>ri(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ta={current:void 0},fy=uy({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ta.current){const e=new eT({});e.mount(window),e.setOptions({layoutScroll:!0}),ta.current=e}return ta.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),tT={pan:{Feature:vE},drag:{Feature:gE,ProjectionNode:fy,MeasureLayout:ry}};function Up(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const o="onHover"+n,i=r[o];i&&ne.postRender(()=>i(t,hi(t)))}class nT extends An{mount(){const{current:t}=this.node;t&&(this.unmount=tC(t,n=>(Up(this.node,n,"Start"),r=>Up(this.node,r,"End"))))}unmount(){}}class rT extends An{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=pi(ri(this.node.current,"focus",()=>this.onFocus()),ri(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Wp(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const o="onTap"+(n==="End"?"":n),i=r[o];i&&ne.postRender(()=>i(t,hi(t)))}class oT extends An{mount(){const{current:t}=this.node;t&&(this.unmount=iC(t,n=>(Wp(this.node,n,"Start"),(r,{success:o})=>Wp(this.node,r,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const wu=new WeakMap,na=new WeakMap,iT=e=>{const t=wu.get(e.target);t&&t(e)},sT=e=>{e.forEach(iT)};function lT({root:e,...t}){const n=e||document;na.has(n)||na.set(n,{});const r=na.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(sT,{root:e,...t})),r[o]}function aT(e,t,n){const r=lT(t);return wu.set(e,n),r.observe(e),()=>{wu.delete(e),r.unobserve(e)}}const uT={some:0,all:1};class cT extends An{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:uT[o]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),d=u?c:f;d&&d(a)};return aT(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(fT(t,n))&&this.startObserver()}unmount(){}}function fT({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const dT={inView:{Feature:cT},tap:{Feature:oT},focus:{Feature:rT},hover:{Feature:nT}},pT={layout:{ProjectionNode:fy,MeasureLayout:ry}},xu={current:null},dy={current:!1};function hT(){if(dy.current=!0,!!Rc)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>xu.current=e.matches;e.addListener(t),t()}else xu.current=!1}const mT=[...Iv,be,Cn],gT=e=>mT.find(Vv(e)),Hp=new WeakMap;function vT(e,t,n){for(const r in t){const o=t[r],i=n[r];if(Ne(o))e.addValue(r,o);else if(Ne(i))e.addValue(r,ti(o,{owner:e}));else if(i!==o)if(e.hasValue(r)){const s=e.getValue(r);s.liveStyle===!0?s.jump(o):s.hasAnimated||s.set(o)}else{const s=e.getStaticValue(r);e.addValue(r,ti(s!==void 0?s:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Kp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class yT{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:i,visualState:s},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ef,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const v=Dt.now();this.renderScheduledAt<v&&(this.renderScheduledAt=v,ne.render(this.render,!1,!0))};const{latestValues:a,renderState:u,onUpdate:c}=s;this.onUpdate=c,this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=l,this.blockInitialAnimation=!!i,this.isControllingVariants=sl(n),this.isVariantNode=Hg(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:f,...d}=this.scrapeMotionValuesFromProps(n,{},this);for(const v in d){const w=d[v];a[v]!==void 0&&Ne(w)&&w.set(a[v],!1)}}mount(t){this.current=t,Hp.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),dy.current||hT(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:xu.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Hp.delete(this.current),this.projection&&this.projection.unmount(),Sn(this.notifyUpdate),Sn(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=er.has(t),o=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&ne.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),i(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Fr){const n=Fr[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):fe()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Kp.length;r++){const o=Kp[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i="on"+o,s=t[i];s&&(this.propEventSubscriptions[o]=this.on(o,s))}this.prevMotionValues=vT(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ti(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let o=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return o!=null&&(typeof o=="string"&&(_v(o)||Tv(o))?o=parseFloat(o):!gT(o)&&Cn.test(n)&&(o=Lv(t,n)),this.setBaseTarget(t,Ne(o)?o.get():o)),Ne(o)?o.get():o}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let o;if(typeof r=="string"||typeof r=="object"){const s=_c(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(o=s[t])}if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!Ne(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Yc),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class py extends yT{constructor(){super(...arguments),this.KeyframeResolver=jv}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Ne(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function wT(e){return window.getComputedStyle(e)}class xT extends py{constructor(){super(...arguments),this.type="html",this.renderInstance=ev}readValueFromInstance(t,n){if(er.has(n)){const r=Jc(n);return r&&r.default||0}else{const r=wT(t),o=(Zg(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return ty(t,n)}build(t,n,r){Ic(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Bc(t,n,r)}}class ST extends py{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=fe}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(er.has(n)){const r=Jc(n);return r&&r.default||0}return n=tv.has(n)?n:Lc(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return ov(t,n,r)}build(t,n,r){jc(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,o){nv(t,n,r,o)}mount(t){this.isSVGTag=zc(t.tagName),super.mount(t)}}const CT=(e,t)=>Dc(e)?new ST(t):new xT(t,{allowProjection:e!==y.Fragment}),PT=YS({...KP,...dT,...tT,...pT},CT),hy=uS(PT),ET=xx("inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring/70 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm shadow-black/[0.04] hover:bg-primary/90",outline:"border border-input bg-background shadow-sm shadow-black/[0.04] hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm shadow-black/[0.04] hover:bg-secondary/80",tertiary:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",error:"bg-[#d93036] hover:bg-[#ff6166]",warning:"bg-[#ff990a] text-primary-foreground hover:bg-[#d27504]"},size:{default:"h-9 px-4 py-2",small:"h-8 rounded-lg px-3 text-xs",large:"h-10 rounded-lg px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),TT=kt.forwardRef(({className:e,variant:t="default",size:n="default",asChild:r=!1,prefix:o,suffix:i,disabled:s=!1,loading:l=!1,...a},u)=>{const c=r?px:"button",f=k.jsxs(c,{className:we(ET({variant:l?"secondary":t,size:n}),e),ref:u,disabled:s,...a,children:[l?k.jsx(jg,{className:"mr-2"}):null,o?k.jsx("span",{className:"mr-2 flex items-center justify-center",children:o}):null,a.children,i?k.jsx("span",{className:"ml-2 flex items-center justify-center",children:i}):null]});return k.jsx("div",{className:we(e,s&&"cursor-not-allowed "),children:k.jsx(hy.div,{whileTap:{scale:.93},children:f})})});TT.displayName="Button";function Xr(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}var kT=["color"],RT=y.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=Xr(e,kT);return y.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o,{ref:t}),y.createElement("path",{d:"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",fill:r,fillRule:"evenodd",clipRule:"evenodd"}))}),AT=["color"],MT=y.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=Xr(e,AT);return y.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o,{ref:t}),y.createElement("path",{d:"M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z",fill:r,fillRule:"evenodd",clipRule:"evenodd"}))}),bT=["color"],LT=y.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=Xr(e,bT);return y.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o,{ref:t}),y.createElement("path",{d:"M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z",fill:r,fillRule:"evenodd",clipRule:"evenodd"}))}),NT=["color"],DT=y.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=Xr(e,NT);return y.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o,{ref:t}),y.createElement("path",{d:"M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z",fill:r,fillRule:"evenodd",clipRule:"evenodd"}))}),_T=["color"],OT=y.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=Xr(e,_T);return y.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o,{ref:t}),y.createElement("path",{d:"M14.7649 6.07596C14.9991 6.22231 15.0703 6.53079 14.9239 6.76495C14.4849 7.46743 13.9632 8.10645 13.3702 8.66305L14.5712 9.86406C14.7664 10.0593 14.7664 10.3759 14.5712 10.5712C14.3759 10.7664 14.0593 10.7664 13.8641 10.5712L12.6011 9.30817C11.805 9.90283 10.9089 10.3621 9.93375 10.651L10.383 12.3277C10.4544 12.5944 10.2961 12.8685 10.0294 12.94C9.76267 13.0115 9.4885 12.8532 9.41704 12.5865L8.95917 10.8775C8.48743 10.958 8.00036 10.9999 7.50001 10.9999C6.99965 10.9999 6.51257 10.958 6.04082 10.8775L5.58299 12.5864C5.51153 12.8532 5.23737 13.0115 4.97064 12.94C4.7039 12.8686 4.5456 12.5944 4.61706 12.3277L5.06625 10.651C4.09111 10.3621 3.19503 9.90282 2.3989 9.30815L1.1359 10.5712C0.940638 10.7664 0.624058 10.7664 0.428798 10.5712C0.233537 10.3759 0.233537 10.0593 0.428798 9.86405L1.62982 8.66303C1.03682 8.10643 0.515113 7.46742 0.0760677 6.76495C-0.0702867 6.53079 0.000898544 6.22231 0.235065 6.07596C0.469231 5.9296 0.777703 6.00079 0.924058 6.23496C1.40354 7.00213 1.989 7.68057 2.66233 8.2427C2.67315 8.25096 2.6837 8.25972 2.69397 8.26898C4.00897 9.35527 5.65537 9.99991 7.50001 9.99991C10.3078 9.99991 12.6564 8.5063 14.076 6.23495C14.2223 6.00079 14.5308 5.9296 14.7649 6.07596Z",fill:r,fillRule:"evenodd",clipRule:"evenodd"}))}),VT=["color"],IT=y.forwardRef(function(e,t){var n=e.color,r=n===void 0?"currentColor":n,o=Xr(e,VT);return y.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o,{ref:t}),y.createElement("path",{d:"M7.5 11C4.80285 11 2.52952 9.62184 1.09622 7.50001C2.52952 5.37816 4.80285 4 7.5 4C10.1971 4 12.4705 5.37816 13.9038 7.50001C12.4705 9.62183 10.1971 11 7.5 11ZM7.5 3C4.30786 3 1.65639 4.70638 0.0760002 7.23501C-0.0253338 7.39715 -0.0253334 7.60288 0.0760014 7.76501C1.65639 10.2936 4.30786 12 7.5 12C10.6921 12 13.3436 10.2936 14.924 7.76501C15.0253 7.60288 15.0253 7.39715 14.924 7.23501C13.3436 4.70638 10.6921 3 7.5 3ZM7.5 9.5C8.60457 9.5 9.5 8.60457 9.5 7.5C9.5 6.39543 8.60457 5.5 7.5 5.5C6.39543 5.5 5.5 6.39543 5.5 7.5C5.5 8.60457 6.39543 9.5 7.5 9.5Z",fill:r,fillRule:"evenodd",clipRule:"evenodd"}))});const my=y.forwardRef(({className:e,iclassName:t,prefix:n,suffix:r,prefixStyling:o=!0,suffixStyling:i=!0,label:s,type:l,error:a,...u},c)=>{const f=y.useRef(null),d=y.useRef(null),[v,w]=y.useState(0),[m,x]=y.useState(0);return y.useEffect(()=>{f.current&&w(f.current.offsetWidth),d.current&&x(d.current.offsetWidth)},[n,r]),k.jsxs("div",{className:we("relative",e),children:[s&&k.jsx("label",{className:`text-sm ${a?"text-[#ff6166]":"text-muted-foreground"} `,htmlFor:u.id,children:s}),n&&k.jsxs("div",{ref:f,className:we("absolute top-0 left-0 h-full flex items-center justify-center pl-2 text-muted-foreground",`${o?"rounded-l-md":""}`),children:[n,o&&k.jsx("div",{className:"h-[94%] w-px ml-2 bg-border "})]}),k.jsx("input",{type:l,className:we("flex w-full h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-shadow file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:border-primary/75 focus-visible:ring-primary/35 disabled:cursor-not-allowed disabled:opacity-50",t,`${a?"outline-none ring-2 ring-[#ffe6e6] border-[#ff6166] dark:ring-[#561a1e] focus-visible:dark:ring-primary/35 dark:hover:ring-[#832126] hover:ring-[#f8b9b9]":""}`),style:{paddingLeft:n?`${v+12}px`:"0.75rem",paddingRight:r?`${m+12}px`:"0.75rem"},ref:c,...u}),r&&k.jsxs("div",{ref:d,className:we("absolute top-0 right-0 h-full flex items-center justify-center pr-2 text-muted-foreground",`${i?"rounded-r-md":""}`),children:[i&&k.jsx("div",{className:"h-[94%] w-[1px] mr-2 bg-border "}),r]}),a&&k.jsxs("div",{className:"flex items-center text-sm text-[#ff6166] mt-1",role:"alert",children:[k.jsx("svg",{"data-testid":"geist-icon",height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",style:{color:"currentcolor"},children:k.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.30761 1.5L1.5 5.30761L1.5 10.6924L5.30761 14.5H10.6924L14.5 10.6924V5.30761L10.6924 1.5H5.30761ZM5.10051 0C4.83529 0 4.58094 0.105357 4.3934 0.292893L0.292893 4.3934C0.105357 4.58094 0 4.83529 0 5.10051V10.8995C0 11.1647 0.105357 11.4191 0.292894 11.6066L4.3934 15.7071C4.58094 15.8946 4.83529 16 5.10051 16H10.8995C11.1647 16 11.4191 15.8946 11.6066 15.7071L15.7071 11.6066C15.8946 11.4191 16 11.1647 16 10.8995V5.10051C16 4.83529 15.8946 4.58093 15.7071 4.3934L11.6066 0.292893C11.4191 0.105357 11.1647 0 10.8995 0H5.10051ZM8.75 3.75V4.5V8L8.75 8.75H7.25V8V4.5V3.75H8.75ZM8 12C8.55229 12 9 11.5523 9 11C9 10.4477 8.55229 10 8 10C7.44772 10 7 10.4477 7 11C7 11.5523 7.44772 12 8 12Z",fill:"currentColor"})}),k.jsx("label",{className:"ml-1",htmlFor:"error",children:a})]})]})});my.displayName="Input";const jT=y.forwardRef(({className:e,...t},n)=>{const[r,o]=y.useState(!1);return k.jsxs("div",{className:"relative",children:[k.jsx(my,{type:r?"text":"password",iclassName:"hide-password-toggle",className:e,ref:n,...t}),k.jsxs("button",{type:"button",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>o(i=>!i),disabled:t.disabled,children:[k.jsxs("div",{className:"flex",children:[k.jsx("div",{className:"bg-background w-4 h-4"}),r?k.jsx(IT,{className:"h-4 w-4 bg-background","aria-hidden":"true"}):k.jsx(OT,{className:"h-4 w-4 bg-background","aria-hidden":"true"})]}),k.jsx("span",{className:"sr-only",children:r?"Hide password":"Show password"})]}),k.jsx("style",{children:`
					.hide-password-toggle::-ms-reveal,
					.hide-password-toggle::-ms-clear {
						visibility: hidden;
						pointer-events: none;
						display: none;
					}
				`})]})});jT.displayName="HideApiKey";function of(e,t=[]){let n=[];function r(i,s){const l=y.createContext(s),a=n.length;n=[...n,s];const u=f=>{var h;const{scope:d,children:v,...w}=f,m=((h=d==null?void 0:d[e])==null?void 0:h[a])||l,x=y.useMemo(()=>w,Object.values(w));return k.jsx(m.Provider,{value:x,children:v})};u.displayName=i+"Provider";function c(f,d){var m;const v=((m=d==null?void 0:d[e])==null?void 0:m[a])||l,w=y.useContext(v);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${f}\` must be used within \`${i}\``)}return[u,c]}const o=()=>{const i=n.map(s=>y.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return y.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,FT(o,...t)]}function FT(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const f=a(i)[`__scope${u}`];return{...l,...f}},{});return y.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function zT(e){const t=e+"CollectionProvider",[n,r]=of(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:x,children:h}=m,p=kt.useRef(null),g=kt.useRef(new Map).current;return k.jsx(o,{scope:x,itemMap:g,collectionRef:p,children:h})};s.displayName=t;const l=e+"CollectionSlot",a=qo(l),u=kt.forwardRef((m,x)=>{const{scope:h,children:p}=m,g=i(l,h),S=Te(x,g.collectionRef);return k.jsx(a,{ref:S,children:p})});u.displayName=l;const c=e+"CollectionItemSlot",f="data-radix-collection-item",d=qo(c),v=kt.forwardRef((m,x)=>{const{scope:h,children:p,...g}=m,S=kt.useRef(null),C=Te(x,S),E=i(c,h);return kt.useEffect(()=>(E.itemMap.set(S,{ref:S,...g}),()=>void E.itemMap.delete(S))),k.jsx(d,{[f]:"",ref:C,children:p})});v.displayName=c;function w(m){const x=i(e+"CollectionConsumer",m);return kt.useCallback(()=>{const p=x.collectionRef.current;if(!p)return[];const g=Array.from(p.querySelectorAll(`[${f}]`));return Array.from(x.itemMap.values()).sort((E,P)=>g.indexOf(E.ref.current)-g.indexOf(P.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:s,Slot:u,ItemSlot:v},w,r]}function he(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}var We=globalThis!=null&&globalThis.document?y.useLayoutEffect:()=>{},BT=Ah[" useInsertionEffect ".trim().toString()]||We;function Gp({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=$T({defaultProp:t,onChange:n}),l=e!==void 0,a=l?e:o;{const c=y.useRef(e!==void 0);y.useEffect(()=>{const f=c.current;f!==l&&console.warn(`${r} is changing from ${f?"controlled":"uncontrolled"} to ${l?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),c.current=l},[l,r])}const u=y.useCallback(c=>{var f;if(l){const d=UT(c)?c(e):c;d!==e&&((f=s.current)==null||f.call(s,d))}else i(c)},[l,e,i,s]);return[a,u]}function $T({defaultProp:e,onChange:t}){const[n,r]=y.useState(e),o=y.useRef(n),i=y.useRef(t);return BT(()=>{i.current=t},[t]),y.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function UT(e){return typeof e=="function"}var WT=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],pe=WT.reduce((e,t)=>{const n=qo(`Primitive.${t}`),r=y.forwardRef((o,i)=>{const{asChild:s,...l}=o,a=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),k.jsx(a,{...l,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function HT(e,t){e&&Hr.flushSync(()=>e.dispatchEvent(t))}var KT=Ah[" useId ".trim().toString()]||(()=>{}),GT=0;function sf(e){const[t,n]=y.useState(KT());return We(()=>{n(r=>r??String(GT++))},[e]),t?`radix-${t}`:""}var YT=y.createContext(void 0);function XT(e){const t=y.useContext(YT);return e||t||"ltr"}const iA=[{model:"gpt-3.5-turbo",name:"openai_3.5_turbo",display:"GPT-3.5 Turbo"},{model:"gpt-4o",name:"openai_4o",display:"GPT-4 Optimized"},{model:"gemini-1.5-pro-latest",name:"gemini_1.5_pro",display:"Gemini 1.5 Pro (Latest)"}],sA=()=>({setKeyModel:async(e,t)=>{chrome.storage.local.set({[t]:e})},getKeyModel:async e=>{const t=await chrome.storage.local.get(e);return{model:e,apiKey:t[e]}},setSelectModel:async e=>{await chrome.storage.local.set({selectedModel:e})},selectModel:async()=>(await chrome.storage.local.get("selectedModel")).selectedModel});function Yn(e){const t=y.useRef(e);return y.useEffect(()=>{t.current=e}),y.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Yp(e,[t,n]){return Math.min(n,Math.max(t,e))}function QT(e,t=globalThis==null?void 0:globalThis.document){const n=Yn(e);y.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var ZT="DismissableLayer",Su="dismissableLayer.update",qT="dismissableLayer.pointerDownOutside",JT="dismissableLayer.focusOutside",Xp,gy=y.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),vy=y.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l,...a}=e,u=y.useContext(gy),[c,f]=y.useState(null),d=(c==null?void 0:c.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=y.useState({}),w=Te(t,P=>f(P)),m=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),h=m.indexOf(x),p=c?m.indexOf(c):-1,g=u.layersWithOutsidePointerEventsDisabled.size>0,S=p>=h,C=nk(P=>{const T=P.target,L=[...u.branches].some(M=>M.contains(T));!S||L||(o==null||o(P),s==null||s(P),P.defaultPrevented||l==null||l())},d),E=rk(P=>{const T=P.target;[...u.branches].some(M=>M.contains(T))||(i==null||i(P),s==null||s(P),P.defaultPrevented||l==null||l())},d);return QT(P=>{p===u.layers.size-1&&(r==null||r(P),!P.defaultPrevented&&l&&(P.preventDefault(),l()))},d),y.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Xp=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),Qp(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=Xp)}},[c,d,n,u]),y.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),Qp())},[c,u]),y.useEffect(()=>{const P=()=>v({});return document.addEventListener(Su,P),()=>document.removeEventListener(Su,P)},[]),k.jsx(pe.div,{...a,ref:w,style:{pointerEvents:g?S?"auto":"none":void 0,...e.style},onFocusCapture:he(e.onFocusCapture,E.onFocusCapture),onBlurCapture:he(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:he(e.onPointerDownCapture,C.onPointerDownCapture)})});vy.displayName=ZT;var ek="DismissableLayerBranch",tk=y.forwardRef((e,t)=>{const n=y.useContext(gy),r=y.useRef(null),o=Te(t,r);return y.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),k.jsx(pe.div,{...e,ref:o})});tk.displayName=ek;function nk(e,t=globalThis==null?void 0:globalThis.document){const n=Yn(e),r=y.useRef(!1),o=y.useRef(()=>{});return y.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){yy(qT,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function rk(e,t=globalThis==null?void 0:globalThis.document){const n=Yn(e),r=y.useRef(!1);return y.useEffect(()=>{const o=i=>{i.target&&!r.current&&yy(JT,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Qp(){const e=new CustomEvent(Su);document.dispatchEvent(e)}function yy(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?HT(o,i):o.dispatchEvent(i)}var ra=0;function ok(){y.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Zp()),document.body.insertAdjacentElement("beforeend",e[1]??Zp()),ra++,()=>{ra===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ra--}},[])}function Zp(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var oa="focusScope.autoFocusOnMount",ia="focusScope.autoFocusOnUnmount",qp={bubbles:!1,cancelable:!0},ik="FocusScope",wy=y.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[l,a]=y.useState(null),u=Yn(o),c=Yn(i),f=y.useRef(null),d=Te(t,m=>a(m)),v=y.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;y.useEffect(()=>{if(r){let m=function(g){if(v.paused||!l)return;const S=g.target;l.contains(S)?f.current=S:rn(f.current,{select:!0})},x=function(g){if(v.paused||!l)return;const S=g.relatedTarget;S!==null&&(l.contains(S)||rn(f.current,{select:!0}))},h=function(g){if(document.activeElement===document.body)for(const C of g)C.removedNodes.length>0&&rn(l)};document.addEventListener("focusin",m),document.addEventListener("focusout",x);const p=new MutationObserver(h);return l&&p.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",x),p.disconnect()}}},[r,l,v.paused]),y.useEffect(()=>{if(l){eh.add(v);const m=document.activeElement;if(!l.contains(m)){const h=new CustomEvent(oa,qp);l.addEventListener(oa,u),l.dispatchEvent(h),h.defaultPrevented||(sk(fk(xy(l)),{select:!0}),document.activeElement===m&&rn(l))}return()=>{l.removeEventListener(oa,u),setTimeout(()=>{const h=new CustomEvent(ia,qp);l.addEventListener(ia,c),l.dispatchEvent(h),h.defaultPrevented||rn(m??document.body,{select:!0}),l.removeEventListener(ia,c),eh.remove(v)},0)}}},[l,u,c,v]);const w=y.useCallback(m=>{if(!n&&!r||v.paused)return;const x=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,h=document.activeElement;if(x&&h){const p=m.currentTarget,[g,S]=lk(p);g&&S?!m.shiftKey&&h===S?(m.preventDefault(),n&&rn(g,{select:!0})):m.shiftKey&&h===g&&(m.preventDefault(),n&&rn(S,{select:!0})):h===p&&m.preventDefault()}},[n,r,v.paused]);return k.jsx(pe.div,{tabIndex:-1,...s,ref:d,onKeyDown:w})});wy.displayName=ik;function sk(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(rn(r,{select:t}),document.activeElement!==n)return}function lk(e){const t=xy(e),n=Jp(t,e),r=Jp(t.reverse(),e);return[n,r]}function xy(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Jp(e,t){for(const n of e)if(!ak(n,{upTo:t}))return n}function ak(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function uk(e){return e instanceof HTMLInputElement&&"select"in e}function rn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&uk(e)&&t&&e.select()}}var eh=ck();function ck(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=th(e,t),e.unshift(t)},remove(t){var n;e=th(e,t),(n=e[0])==null||n.resume()}}}function th(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function fk(e){return e.filter(t=>t.tagName!=="A")}const dk=["top","right","bottom","left"],Pn=Math.min,Ge=Math.max,Is=Math.round,Ii=Math.floor,_t=e=>({x:e,y:e}),pk={left:"right",right:"left",bottom:"top",top:"bottom"},hk={start:"end",end:"start"};function Cu(e,t,n){return Ge(e,Pn(t,n))}function Qt(e,t){return typeof e=="function"?e(t):e}function Zt(e){return e.split("-")[0]}function Qr(e){return e.split("-")[1]}function lf(e){return e==="x"?"y":"x"}function af(e){return e==="y"?"height":"width"}const mk=new Set(["top","bottom"]);function Mt(e){return mk.has(Zt(e))?"y":"x"}function uf(e){return lf(Mt(e))}function gk(e,t,n){n===void 0&&(n=!1);const r=Qr(e),o=uf(e),i=af(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=js(s)),[s,js(s)]}function vk(e){const t=js(e);return[Pu(e),t,Pu(t)]}function Pu(e){return e.replace(/start|end/g,t=>hk[t])}const nh=["left","right"],rh=["right","left"],yk=["top","bottom"],wk=["bottom","top"];function xk(e,t,n){switch(e){case"top":case"bottom":return n?t?rh:nh:t?nh:rh;case"left":case"right":return t?yk:wk;default:return[]}}function Sk(e,t,n,r){const o=Qr(e);let i=xk(Zt(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Pu)))),i}function js(e){return e.replace(/left|right|bottom|top/g,t=>pk[t])}function Ck(e){return{top:0,right:0,bottom:0,left:0,...e}}function Sy(e){return typeof e!="number"?Ck(e):{top:e,right:e,bottom:e,left:e}}function Fs(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function oh(e,t,n){let{reference:r,floating:o}=e;const i=Mt(t),s=uf(t),l=af(s),a=Zt(t),u=i==="y",c=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,d=r[l]/2-o[l]/2;let v;switch(a){case"top":v={x:c,y:r.y-o.height};break;case"bottom":v={x:c,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:f};break;case"left":v={x:r.x-o.width,y:f};break;default:v={x:r.x,y:r.y}}switch(Qr(t)){case"start":v[s]-=d*(n&&u?-1:1);break;case"end":v[s]+=d*(n&&u?-1:1);break}return v}const Pk=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=oh(u,r,a),d=r,v={},w=0;for(let m=0;m<l.length;m++){const{name:x,fn:h}=l[m],{x:p,y:g,data:S,reset:C}=await h({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:v,rects:u,platform:s,elements:{reference:e,floating:t}});c=p??c,f=g??f,v={...v,[x]:{...v[x],...S}},C&&w<=50&&(w++,typeof C=="object"&&(C.placement&&(d=C.placement),C.rects&&(u=C.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:c,y:f}=oh(u,d,a)),m=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:v}};async function oi(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:v=0}=Qt(t,e),w=Sy(v),x=l[d?f==="floating"?"reference":"floating":f],h=Fs(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(x)))==null||n?x:x.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:a})),p=f==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,g=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),S=await(i.isElement==null?void 0:i.isElement(g))?await(i.getScale==null?void 0:i.getScale(g))||{x:1,y:1}:{x:1,y:1},C=Fs(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:p,offsetParent:g,strategy:a}):p);return{top:(h.top-C.top+w.top)/S.y,bottom:(C.bottom-h.bottom+w.bottom)/S.y,left:(h.left-C.left+w.left)/S.x,right:(C.right-h.right+w.right)/S.x}}const Ek=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:l,middlewareData:a}=t,{element:u,padding:c=0}=Qt(e,t)||{};if(u==null)return{};const f=Sy(c),d={x:n,y:r},v=uf(o),w=af(v),m=await s.getDimensions(u),x=v==="y",h=x?"top":"left",p=x?"bottom":"right",g=x?"clientHeight":"clientWidth",S=i.reference[w]+i.reference[v]-d[v]-i.floating[w],C=d[v]-i.reference[v],E=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let P=E?E[g]:0;(!P||!await(s.isElement==null?void 0:s.isElement(E)))&&(P=l.floating[g]||i.floating[w]);const T=S/2-C/2,L=P/2-m[w]/2-1,M=Pn(f[h],L),j=Pn(f[p],L),_=M,U=P-m[w]-j,z=P/2-m[w]/2+T,K=Cu(_,z,U),F=!a.arrow&&Qr(o)!=null&&z!==K&&i.reference[w]/2-(z<_?M:j)-m[w]/2<0,O=F?z<_?z-_:z-U:0;return{[v]:d[v]+O,data:{[v]:K,centerOffset:z-K-O,...F&&{alignmentOffset:O}},reset:F}}}),Tk=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:c=!0,crossAxis:f=!0,fallbackPlacements:d,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:m=!0,...x}=Qt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const h=Zt(o),p=Mt(l),g=Zt(l)===l,S=await(a.isRTL==null?void 0:a.isRTL(u.floating)),C=d||(g||!m?[js(l)]:vk(l)),E=w!=="none";!d&&E&&C.push(...Sk(l,m,w,S));const P=[l,...C],T=await oi(t,x),L=[];let M=((r=i.flip)==null?void 0:r.overflows)||[];if(c&&L.push(T[h]),f){const z=gk(o,s,S);L.push(T[z[0]],T[z[1]])}if(M=[...M,{placement:o,overflows:L}],!L.every(z=>z<=0)){var j,_;const z=(((j=i.flip)==null?void 0:j.index)||0)+1,K=P[z];if(K&&(!(f==="alignment"?p!==Mt(K):!1)||M.every(R=>Mt(R.placement)===p?R.overflows[0]>0:!0)))return{data:{index:z,overflows:M},reset:{placement:K}};let F=(_=M.filter(O=>O.overflows[0]<=0).sort((O,R)=>O.overflows[1]-R.overflows[1])[0])==null?void 0:_.placement;if(!F)switch(v){case"bestFit":{var U;const O=(U=M.filter(R=>{if(E){const b=Mt(R.placement);return b===p||b==="y"}return!0}).map(R=>[R.placement,R.overflows.filter(b=>b>0).reduce((b,D)=>b+D,0)]).sort((R,b)=>R[1]-b[1])[0])==null?void 0:U[0];O&&(F=O);break}case"initialPlacement":F=l;break}if(o!==F)return{reset:{placement:F}}}return{}}}};function ih(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function sh(e){return dk.some(t=>e[t]>=0)}const kk=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Qt(e,t);switch(r){case"referenceHidden":{const i=await oi(t,{...o,elementContext:"reference"}),s=ih(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:sh(s)}}}case"escaped":{const i=await oi(t,{...o,altBoundary:!0}),s=ih(i,n.floating);return{data:{escapedOffsets:s,escaped:sh(s)}}}default:return{}}}}},Cy=new Set(["left","top"]);async function Rk(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Zt(n),l=Qr(n),a=Mt(n)==="y",u=Cy.has(s)?-1:1,c=i&&a?-1:1,f=Qt(t,e);let{mainAxis:d,crossAxis:v,alignmentAxis:w}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&typeof w=="number"&&(v=l==="end"?w*-1:w),a?{x:v*c,y:d*u}:{x:d*u,y:v*c}}const Ak=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:l}=t,a=await Rk(t,e);return s===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:s}}}}},Mk=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:x=>{let{x:h,y:p}=x;return{x:h,y:p}}},...a}=Qt(e,t),u={x:n,y:r},c=await oi(t,a),f=Mt(Zt(o)),d=lf(f);let v=u[d],w=u[f];if(i){const x=d==="y"?"top":"left",h=d==="y"?"bottom":"right",p=v+c[x],g=v-c[h];v=Cu(p,v,g)}if(s){const x=f==="y"?"top":"left",h=f==="y"?"bottom":"right",p=w+c[x],g=w-c[h];w=Cu(p,w,g)}const m=l.fn({...t,[d]:v,[f]:w});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:s}}}}}},bk=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:l=0,mainAxis:a=!0,crossAxis:u=!0}=Qt(e,t),c={x:n,y:r},f=Mt(o),d=lf(f);let v=c[d],w=c[f];const m=Qt(l,t),x=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(a){const g=d==="y"?"height":"width",S=i.reference[d]-i.floating[g]+x.mainAxis,C=i.reference[d]+i.reference[g]-x.mainAxis;v<S?v=S:v>C&&(v=C)}if(u){var h,p;const g=d==="y"?"width":"height",S=Cy.has(Zt(o)),C=i.reference[f]-i.floating[g]+(S&&((h=s.offset)==null?void 0:h[f])||0)+(S?0:x.crossAxis),E=i.reference[f]+i.reference[g]+(S?0:((p=s.offset)==null?void 0:p[f])||0)-(S?x.crossAxis:0);w<C?w=C:w>E&&(w=E)}return{[d]:v,[f]:w}}}},Lk=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:l}=t,{apply:a=()=>{},...u}=Qt(e,t),c=await oi(t,u),f=Zt(o),d=Qr(o),v=Mt(o)==="y",{width:w,height:m}=i.floating;let x,h;f==="top"||f==="bottom"?(x=f,h=d===(await(s.isRTL==null?void 0:s.isRTL(l.floating))?"start":"end")?"left":"right"):(h=f,x=d==="end"?"top":"bottom");const p=m-c.top-c.bottom,g=w-c.left-c.right,S=Pn(m-c[x],p),C=Pn(w-c[h],g),E=!t.middlewareData.shift;let P=S,T=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(T=g),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(P=p),E&&!d){const M=Ge(c.left,0),j=Ge(c.right,0),_=Ge(c.top,0),U=Ge(c.bottom,0);v?T=w-2*(M!==0||j!==0?M+j:Ge(c.left,c.right)):P=m-2*(_!==0||U!==0?_+U:Ge(c.top,c.bottom))}await a({...t,availableWidth:T,availableHeight:P});const L=await s.getDimensions(l.floating);return w!==L.width||m!==L.height?{reset:{rects:!0}}:{}}}};function ul(){return typeof window<"u"}function Zr(e){return Py(e)?(e.nodeName||"").toLowerCase():"#document"}function Ze(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Vt(e){var t;return(t=(Py(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Py(e){return ul()?e instanceof Node||e instanceof Ze(e).Node:!1}function wt(e){return ul()?e instanceof Element||e instanceof Ze(e).Element:!1}function Ot(e){return ul()?e instanceof HTMLElement||e instanceof Ze(e).HTMLElement:!1}function lh(e){return!ul()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ze(e).ShadowRoot}const Nk=new Set(["inline","contents"]);function mi(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=xt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Nk.has(o)}const Dk=new Set(["table","td","th"]);function _k(e){return Dk.has(Zr(e))}const Ok=[":popover-open",":modal"];function cl(e){return Ok.some(t=>{try{return e.matches(t)}catch{return!1}})}const Vk=["transform","translate","scale","rotate","perspective"],Ik=["transform","translate","scale","rotate","perspective","filter"],jk=["paint","layout","strict","content"];function cf(e){const t=ff(),n=wt(e)?xt(e):e;return Vk.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Ik.some(r=>(n.willChange||"").includes(r))||jk.some(r=>(n.contain||"").includes(r))}function Fk(e){let t=En(e);for(;Ot(t)&&!Br(t);){if(cf(t))return t;if(cl(t))return null;t=En(t)}return null}function ff(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const zk=new Set(["html","body","#document"]);function Br(e){return zk.has(Zr(e))}function xt(e){return Ze(e).getComputedStyle(e)}function fl(e){return wt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function En(e){if(Zr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||lh(e)&&e.host||Vt(e);return lh(t)?t.host:t}function Ey(e){const t=En(e);return Br(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ot(t)&&mi(t)?t:Ey(t)}function ii(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Ey(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Ze(o);if(i){const l=Eu(s);return t.concat(s,s.visualViewport||[],mi(o)?o:[],l&&n?ii(l):[])}return t.concat(o,ii(o,[],n))}function Eu(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ty(e){const t=xt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Ot(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,l=Is(n)!==i||Is(r)!==s;return l&&(n=i,r=s),{width:n,height:r,$:l}}function df(e){return wt(e)?e:e.contextElement}function Mr(e){const t=df(e);if(!Ot(t))return _t(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Ty(t);let s=(i?Is(n.width):n.width)/r,l=(i?Is(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}const Bk=_t(0);function ky(e){const t=Ze(e);return!ff()||!t.visualViewport?Bk:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function $k(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ze(e)?!1:t}function Xn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=df(e);let s=_t(1);t&&(r?wt(r)&&(s=Mr(r)):s=Mr(e));const l=$k(i,n,r)?ky(i):_t(0);let a=(o.left+l.x)/s.x,u=(o.top+l.y)/s.y,c=o.width/s.x,f=o.height/s.y;if(i){const d=Ze(i),v=r&&wt(r)?Ze(r):r;let w=d,m=Eu(w);for(;m&&r&&v!==w;){const x=Mr(m),h=m.getBoundingClientRect(),p=xt(m),g=h.left+(m.clientLeft+parseFloat(p.paddingLeft))*x.x,S=h.top+(m.clientTop+parseFloat(p.paddingTop))*x.y;a*=x.x,u*=x.y,c*=x.x,f*=x.y,a+=g,u+=S,w=Ze(m),m=Eu(w)}}return Fs({width:c,height:f,x:a,y:u})}function pf(e,t){const n=fl(e).scrollLeft;return t?t.left+n:Xn(Vt(e)).left+n}function Ry(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:pf(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function Uk(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Vt(r),l=t?cl(t.floating):!1;if(r===s||l&&i)return n;let a={scrollLeft:0,scrollTop:0},u=_t(1);const c=_t(0),f=Ot(r);if((f||!f&&!i)&&((Zr(r)!=="body"||mi(s))&&(a=fl(r)),Ot(r))){const v=Xn(r);u=Mr(r),c.x=v.x+r.clientLeft,c.y=v.y+r.clientTop}const d=s&&!f&&!i?Ry(s,a,!0):_t(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-a.scrollTop*u.y+c.y+d.y}}function Wk(e){return Array.from(e.getClientRects())}function Hk(e){const t=Vt(e),n=fl(e),r=e.ownerDocument.body,o=Ge(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Ge(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+pf(e);const l=-n.scrollTop;return xt(r).direction==="rtl"&&(s+=Ge(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:l}}function Kk(e,t){const n=Ze(e),r=Vt(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,l=0,a=0;if(o){i=o.width,s=o.height;const u=ff();(!u||u&&t==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:i,height:s,x:l,y:a}}const Gk=new Set(["absolute","fixed"]);function Yk(e,t){const n=Xn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Ot(e)?Mr(e):_t(1),s=e.clientWidth*i.x,l=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:s,height:l,x:a,y:u}}function ah(e,t,n){let r;if(t==="viewport")r=Kk(e,n);else if(t==="document")r=Hk(Vt(e));else if(wt(t))r=Yk(t,n);else{const o=ky(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Fs(r)}function Ay(e,t){const n=En(e);return n===t||!wt(n)||Br(n)?!1:xt(n).position==="fixed"||Ay(n,t)}function Xk(e,t){const n=t.get(e);if(n)return n;let r=ii(e,[],!1).filter(l=>wt(l)&&Zr(l)!=="body"),o=null;const i=xt(e).position==="fixed";let s=i?En(e):e;for(;wt(s)&&!Br(s);){const l=xt(s),a=cf(s);!a&&l.position==="fixed"&&(o=null),(i?!a&&!o:!a&&l.position==="static"&&!!o&&Gk.has(o.position)||mi(s)&&!a&&Ay(e,s))?r=r.filter(c=>c!==s):o=l,s=En(s)}return t.set(e,r),r}function Qk(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?cl(t)?[]:Xk(t,this._c):[].concat(n),r],l=s[0],a=s.reduce((u,c)=>{const f=ah(t,c,o);return u.top=Ge(f.top,u.top),u.right=Pn(f.right,u.right),u.bottom=Pn(f.bottom,u.bottom),u.left=Ge(f.left,u.left),u},ah(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Zk(e){const{width:t,height:n}=Ty(e);return{width:t,height:n}}function qk(e,t,n){const r=Ot(t),o=Vt(t),i=n==="fixed",s=Xn(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const a=_t(0);function u(){a.x=pf(o)}if(r||!r&&!i)if((Zr(t)!=="body"||mi(o))&&(l=fl(t)),r){const v=Xn(t,!0,i,t);a.x=v.x+t.clientLeft,a.y=v.y+t.clientTop}else o&&u();i&&!r&&o&&u();const c=o&&!r&&!i?Ry(o,l):_t(0),f=s.left+l.scrollLeft-a.x-c.x,d=s.top+l.scrollTop-a.y-c.y;return{x:f,y:d,width:s.width,height:s.height}}function sa(e){return xt(e).position==="static"}function uh(e,t){if(!Ot(e)||xt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Vt(e)===n&&(n=n.ownerDocument.body),n}function My(e,t){const n=Ze(e);if(cl(e))return n;if(!Ot(e)){let o=En(e);for(;o&&!Br(o);){if(wt(o)&&!sa(o))return o;o=En(o)}return n}let r=uh(e,t);for(;r&&_k(r)&&sa(r);)r=uh(r,t);return r&&Br(r)&&sa(r)&&!cf(r)?n:r||Fk(e)||n}const Jk=async function(e){const t=this.getOffsetParent||My,n=this.getDimensions,r=await n(e.floating);return{reference:qk(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function e2(e){return xt(e).direction==="rtl"}const t2={convertOffsetParentRelativeRectToViewportRelativeRect:Uk,getDocumentElement:Vt,getClippingRect:Qk,getOffsetParent:My,getElementRects:Jk,getClientRects:Wk,getDimensions:Zk,getScale:Mr,isElement:wt,isRTL:e2};function by(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function n2(e,t){let n=null,r;const o=Vt(e);function i(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),i();const u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:v}=u;if(l||t(),!d||!v)return;const w=Ii(f),m=Ii(o.clientWidth-(c+d)),x=Ii(o.clientHeight-(f+v)),h=Ii(c),g={rootMargin:-w+"px "+-m+"px "+-x+"px "+-h+"px",threshold:Ge(0,Pn(1,a))||1};let S=!0;function C(E){const P=E[0].intersectionRatio;if(P!==a){if(!S)return s();P?s(!1,P):r=setTimeout(()=>{s(!1,1e-7)},1e3)}P===1&&!by(u,e.getBoundingClientRect())&&s(),S=!1}try{n=new IntersectionObserver(C,{...g,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,g)}n.observe(e)}return s(!0),i}function r2(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=df(e),c=o||i?[...u?ii(u):[],...ii(t)]:[];c.forEach(h=>{o&&h.addEventListener("scroll",n,{passive:!0}),i&&h.addEventListener("resize",n)});const f=u&&l?n2(u,n):null;let d=-1,v=null;s&&(v=new ResizeObserver(h=>{let[p]=h;p&&p.target===u&&v&&(v.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var g;(g=v)==null||g.observe(t)})),n()}),u&&!a&&v.observe(u),v.observe(t));let w,m=a?Xn(e):null;a&&x();function x(){const h=Xn(e);m&&!by(m,h)&&n(),m=h,w=requestAnimationFrame(x)}return n(),()=>{var h;c.forEach(p=>{o&&p.removeEventListener("scroll",n),i&&p.removeEventListener("resize",n)}),f==null||f(),(h=v)==null||h.disconnect(),v=null,a&&cancelAnimationFrame(w)}}const o2=Ak,i2=Mk,s2=Tk,l2=Lk,a2=kk,ch=Ek,u2=bk,c2=(e,t,n)=>{const r=new Map,o={platform:t2,...n},i={...o.platform,_c:r};return Pk(e,t,{...o,platform:i})};var f2=typeof document<"u",d2=function(){},rs=f2?y.useLayoutEffect:d2;function zs(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!zs(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!zs(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Ly(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function fh(e,t){const n=Ly(e);return Math.round(t*n)/n}function la(e){const t=y.useRef(e);return rs(()=>{t.current=e}),t}function p2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:u}=e,[c,f]=y.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,v]=y.useState(r);zs(d,r)||v(r);const[w,m]=y.useState(null),[x,h]=y.useState(null),p=y.useCallback(R=>{R!==E.current&&(E.current=R,m(R))},[]),g=y.useCallback(R=>{R!==P.current&&(P.current=R,h(R))},[]),S=i||w,C=s||x,E=y.useRef(null),P=y.useRef(null),T=y.useRef(c),L=a!=null,M=la(a),j=la(o),_=la(u),U=y.useCallback(()=>{if(!E.current||!P.current)return;const R={placement:t,strategy:n,middleware:d};j.current&&(R.platform=j.current),c2(E.current,P.current,R).then(b=>{const D={...b,isPositioned:_.current!==!1};z.current&&!zs(T.current,D)&&(T.current=D,Hr.flushSync(()=>{f(D)}))})},[d,t,n,j,_]);rs(()=>{u===!1&&T.current.isPositioned&&(T.current.isPositioned=!1,f(R=>({...R,isPositioned:!1})))},[u]);const z=y.useRef(!1);rs(()=>(z.current=!0,()=>{z.current=!1}),[]),rs(()=>{if(S&&(E.current=S),C&&(P.current=C),S&&C){if(M.current)return M.current(S,C,U);U()}},[S,C,U,M,L]);const K=y.useMemo(()=>({reference:E,floating:P,setReference:p,setFloating:g}),[p,g]),F=y.useMemo(()=>({reference:S,floating:C}),[S,C]),O=y.useMemo(()=>{const R={position:n,left:0,top:0};if(!F.floating)return R;const b=fh(F.floating,c.x),D=fh(F.floating,c.y);return l?{...R,transform:"translate("+b+"px, "+D+"px)",...Ly(F.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:b,top:D}},[n,l,F.floating,c.x,c.y]);return y.useMemo(()=>({...c,update:U,refs:K,elements:F,floatingStyles:O}),[c,U,K,F,O])}const h2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?ch({element:r.current,padding:o}).fn(n):{}:r?ch({element:r,padding:o}).fn(n):{}}}},m2=(e,t)=>({...o2(e),options:[e,t]}),g2=(e,t)=>({...i2(e),options:[e,t]}),v2=(e,t)=>({...u2(e),options:[e,t]}),y2=(e,t)=>({...s2(e),options:[e,t]}),w2=(e,t)=>({...l2(e),options:[e,t]}),x2=(e,t)=>({...a2(e),options:[e,t]}),S2=(e,t)=>({...h2(e),options:[e,t]});var C2="Arrow",Ny=y.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return k.jsx(pe.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:k.jsx("polygon",{points:"0,0 30,0 15,10"})})});Ny.displayName=C2;var P2=Ny;function E2(e){const[t,n]=y.useState(void 0);return We(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,l;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;s=u.inlineSize,l=u.blockSize}else s=e.offsetWidth,l=e.offsetHeight;n({width:s,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var hf="Popper",[Dy,_y]=of(hf),[T2,Oy]=Dy(hf),Vy=e=>{const{__scopePopper:t,children:n}=e,[r,o]=y.useState(null);return k.jsx(T2,{scope:t,anchor:r,onAnchorChange:o,children:n})};Vy.displayName=hf;var Iy="PopperAnchor",jy=y.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Oy(Iy,n),s=y.useRef(null),l=Te(t,s),a=y.useRef(null);return y.useEffect(()=>{const u=a.current;a.current=(r==null?void 0:r.current)||s.current,u!==a.current&&i.onAnchorChange(a.current)}),r?null:k.jsx(pe.div,{...o,ref:l})});jy.displayName=Iy;var mf="PopperContent",[k2,R2]=Dy(mf),Fy=y.forwardRef((e,t)=>{var I,q,Re,Q,Y,X;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:c=0,sticky:f="partial",hideWhenDetached:d=!1,updatePositionStrategy:v="optimized",onPlaced:w,...m}=e,x=Oy(mf,n),[h,p]=y.useState(null),g=Te(t,He=>p(He)),[S,C]=y.useState(null),E=E2(S),P=(E==null?void 0:E.width)??0,T=(E==null?void 0:E.height)??0,L=r+(i!=="center"?"-"+i:""),M=typeof c=="number"?c:{top:0,right:0,bottom:0,left:0,...c},j=Array.isArray(u)?u:[u],_=j.length>0,U={padding:M,boundary:j.filter(M2),altBoundary:_},{refs:z,floatingStyles:K,placement:F,isPositioned:O,middlewareData:R}=p2({strategy:"fixed",placement:L,whileElementsMounted:(...He)=>r2(...He,{animationFrame:v==="always"}),elements:{reference:x.anchor},middleware:[m2({mainAxis:o+T,alignmentAxis:s}),a&&g2({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?v2():void 0,...U}),a&&y2({...U}),w2({...U,apply:({elements:He,rects:Pt,availableWidth:Jr,availableHeight:eo})=>{const{width:to,height:j0}=Pt.reference,gi=He.floating.style;gi.setProperty("--radix-popper-available-width",`${Jr}px`),gi.setProperty("--radix-popper-available-height",`${eo}px`),gi.setProperty("--radix-popper-anchor-width",`${to}px`),gi.setProperty("--radix-popper-anchor-height",`${j0}px`)}}),S&&S2({element:S,padding:l}),b2({arrowWidth:P,arrowHeight:T}),d&&x2({strategy:"referenceHidden",...U})]}),[b,D]=$y(F),B=Yn(w);We(()=>{O&&(B==null||B())},[O,B]);const re=(I=R.arrow)==null?void 0:I.x,St=(q=R.arrow)==null?void 0:q.y,ke=((Re=R.arrow)==null?void 0:Re.centerOffset)!==0,[Ct,_e]=y.useState();return We(()=>{h&&_e(window.getComputedStyle(h).zIndex)},[h]),k.jsx("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:O?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ct,"--radix-popper-transform-origin":[(Q=R.transformOrigin)==null?void 0:Q.x,(Y=R.transformOrigin)==null?void 0:Y.y].join(" "),...((X=R.hide)==null?void 0:X.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:k.jsx(k2,{scope:n,placedSide:b,onArrowChange:C,arrowX:re,arrowY:St,shouldHideArrow:ke,children:k.jsx(pe.div,{"data-side":b,"data-align":D,...m,ref:g,style:{...m.style,animation:O?void 0:"none"}})})})});Fy.displayName=mf;var zy="PopperArrow",A2={top:"bottom",right:"left",bottom:"top",left:"right"},By=y.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=R2(zy,r),s=A2[i.placedSide];return k.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:k.jsx(P2,{...o,ref:n,style:{...o.style,display:"block"}})})});By.displayName=zy;function M2(e){return e!==null}var b2=e=>({name:"transformOrigin",options:e,fn(t){var x,h,p;const{placement:n,rects:r,middlewareData:o}=t,s=((x=o.arrow)==null?void 0:x.centerOffset)!==0,l=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[u,c]=$y(n),f={start:"0%",center:"50%",end:"100%"}[c],d=(((h=o.arrow)==null?void 0:h.x)??0)+l/2,v=(((p=o.arrow)==null?void 0:p.y)??0)+a/2;let w="",m="";return u==="bottom"?(w=s?f:`${d}px`,m=`${-a}px`):u==="top"?(w=s?f:`${d}px`,m=`${r.floating.height+a}px`):u==="right"?(w=`${-a}px`,m=s?f:`${v}px`):u==="left"&&(w=`${r.floating.width+a}px`,m=s?f:`${v}px`),{data:{x:w,y:m}}}});function $y(e){const[t,n="center"]=e.split("-");return[t,n]}var L2=Vy,N2=jy,D2=Fy,_2=By,O2="Portal",Uy=y.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,i]=y.useState(!1);We(()=>i(!0),[]);const s=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return s?fx.createPortal(k.jsx(pe.div,{...r,ref:t}),s):null});Uy.displayName=O2;function V2(e){const t=y.useRef({value:e,previous:e});return y.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Wy=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),I2="VisuallyHidden",j2=y.forwardRef((e,t)=>k.jsx(pe.span,{...e,ref:t,style:{...Wy,...e.style}}));j2.displayName=I2;var F2=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},nr=new WeakMap,ji=new WeakMap,Fi={},aa=0,Hy=function(e){return e&&(e.host||Hy(e.parentNode))},z2=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Hy(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},B2=function(e,t,n,r){var o=z2(t,Array.isArray(e)?e:[e]);Fi[n]||(Fi[n]=new WeakMap);var i=Fi[n],s=[],l=new Set,a=new Set(o),u=function(f){!f||l.has(f)||(l.add(f),u(f.parentNode))};o.forEach(u);var c=function(f){!f||a.has(f)||Array.prototype.forEach.call(f.children,function(d){if(l.has(d))c(d);else try{var v=d.getAttribute(r),w=v!==null&&v!=="false",m=(nr.get(d)||0)+1,x=(i.get(d)||0)+1;nr.set(d,m),i.set(d,x),s.push(d),m===1&&w&&ji.set(d,!0),x===1&&d.setAttribute(n,"true"),w||d.setAttribute(r,"true")}catch(h){console.error("aria-hidden: cannot operate on ",d,h)}})};return c(t),l.clear(),aa++,function(){s.forEach(function(f){var d=nr.get(f)-1,v=i.get(f)-1;nr.set(f,d),i.set(f,v),d||(ji.has(f)||f.removeAttribute(r),ji.delete(f)),v||f.removeAttribute(n)}),aa--,aa||(nr=new WeakMap,nr=new WeakMap,ji=new WeakMap,Fi={})}},$2=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=F2(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),B2(r,o,n,"aria-hidden")):function(){return null}},At=function(){return At=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},At.apply(this,arguments)};function Ky(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function U2(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var os="right-scroll-bar-position",is="width-before-scroll-bar",W2="with-scroll-bars-hidden",H2="--removed-body-scroll-bar-size";function ua(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function K2(e,t){var n=y.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var G2=typeof window<"u"?y.useLayoutEffect:y.useEffect,dh=new WeakMap;function Y2(e,t){var n=K2(null,function(r){return e.forEach(function(o){return ua(o,r)})});return G2(function(){var r=dh.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(l){i.has(l)||ua(l,null)}),i.forEach(function(l){o.has(l)||ua(l,s)})}dh.set(n,e)},[e]),n}function X2(e){return e}function Q2(e,t){t===void 0&&(t=X2);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(l){return l!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(l){return i(l)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var l=n;n=[],l.forEach(i),s=n}var a=function(){var c=s;s=[],c.forEach(i)},u=function(){return Promise.resolve().then(a)};u(),n={push:function(c){s.push(c),u()},filter:function(c){return s=s.filter(c),n}}}};return o}function Z2(e){e===void 0&&(e={});var t=Q2(null);return t.options=At({async:!0,ssr:!1},e),t}var Gy=function(e){var t=e.sideCar,n=Ky(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return y.createElement(r,At({},n))};Gy.isSideCarExport=!0;function q2(e,t){return e.useMedium(t),Gy}var Yy=Z2(),ca=function(){},dl=y.forwardRef(function(e,t){var n=y.useRef(null),r=y.useState({onScrollCapture:ca,onWheelCapture:ca,onTouchMoveCapture:ca}),o=r[0],i=r[1],s=e.forwardProps,l=e.children,a=e.className,u=e.removeScrollBar,c=e.enabled,f=e.shards,d=e.sideCar,v=e.noRelative,w=e.noIsolation,m=e.inert,x=e.allowPinchZoom,h=e.as,p=h===void 0?"div":h,g=e.gapMode,S=Ky(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=d,E=Y2([n,t]),P=At(At({},S),o);return y.createElement(y.Fragment,null,c&&y.createElement(C,{sideCar:Yy,removeScrollBar:u,shards:f,noRelative:v,noIsolation:w,inert:m,setCallbacks:i,allowPinchZoom:!!x,lockRef:n,gapMode:g}),s?y.cloneElement(y.Children.only(l),At(At({},P),{ref:E})):y.createElement(p,At({},P,{className:a,ref:E}),l))});dl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};dl.classNames={fullWidth:is,zeroRight:os};var J2=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function eR(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=J2();return t&&e.setAttribute("nonce",t),e}function tR(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function nR(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var rR=function(){var e=0,t=null;return{add:function(n){e==0&&(t=eR())&&(tR(t,n),nR(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},oR=function(){var e=rR();return function(t,n){y.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Xy=function(){var e=oR(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},iR={left:0,top:0,right:0,gap:0},fa=function(e){return parseInt(e||"",10)||0},sR=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[fa(n),fa(r),fa(o)]},lR=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return iR;var t=sR(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},aR=Xy(),br="data-scroll-locked",uR=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(W2,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(br,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(os,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(is,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(os," .").concat(os,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(is," .").concat(is,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(br,`] {
    `).concat(H2,": ").concat(l,`px;
  }
`)},ph=function(){var e=parseInt(document.body.getAttribute(br)||"0",10);return isFinite(e)?e:0},cR=function(){y.useEffect(function(){return document.body.setAttribute(br,(ph()+1).toString()),function(){var e=ph()-1;e<=0?document.body.removeAttribute(br):document.body.setAttribute(br,e.toString())}},[])},fR=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;cR();var i=y.useMemo(function(){return lR(o)},[o]);return y.createElement(aR,{styles:uR(i,!t,o,n?"":"!important")})},Tu=!1;if(typeof window<"u")try{var zi=Object.defineProperty({},"passive",{get:function(){return Tu=!0,!0}});window.addEventListener("test",zi,zi),window.removeEventListener("test",zi,zi)}catch{Tu=!1}var rr=Tu?{passive:!1}:!1,dR=function(e){return e.tagName==="TEXTAREA"},Qy=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!dR(e)&&n[t]==="visible")},pR=function(e){return Qy(e,"overflowY")},hR=function(e){return Qy(e,"overflowX")},hh=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Zy(e,r);if(o){var i=qy(e,r),s=i[1],l=i[2];if(s>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},mR=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},gR=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Zy=function(e,t){return e==="v"?pR(t):hR(t)},qy=function(e,t){return e==="v"?mR(t):gR(t)},vR=function(e,t){return e==="h"&&t==="rtl"?-1:1},yR=function(e,t,n,r,o){var i=vR(e,window.getComputedStyle(t).direction),s=i*r,l=n.target,a=t.contains(l),u=!1,c=s>0,f=0,d=0;do{if(!l)break;var v=qy(e,l),w=v[0],m=v[1],x=v[2],h=m-x-i*w;(w||h)&&Zy(e,l)&&(f+=h,d+=w);var p=l.parentNode;l=p&&p.nodeType===Node.DOCUMENT_FRAGMENT_NODE?p.host:p}while(!a&&l!==document.body||a&&(t.contains(l)||t===l));return(c&&Math.abs(f)<1||!c&&Math.abs(d)<1)&&(u=!0),u},Bi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},mh=function(e){return[e.deltaX,e.deltaY]},gh=function(e){return e&&"current"in e?e.current:e},wR=function(e,t){return e[0]===t[0]&&e[1]===t[1]},xR=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},SR=0,or=[];function CR(e){var t=y.useRef([]),n=y.useRef([0,0]),r=y.useRef(),o=y.useState(SR++)[0],i=y.useState(Xy)[0],s=y.useRef(e);y.useEffect(function(){s.current=e},[e]),y.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=U2([e.lockRef.current],(e.shards||[]).map(gh),!0).filter(Boolean);return m.forEach(function(x){return x.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=y.useCallback(function(m,x){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!s.current.allowPinchZoom;var h=Bi(m),p=n.current,g="deltaX"in m?m.deltaX:p[0]-h[0],S="deltaY"in m?m.deltaY:p[1]-h[1],C,E=m.target,P=Math.abs(g)>Math.abs(S)?"h":"v";if("touches"in m&&P==="h"&&E.type==="range")return!1;var T=hh(P,E);if(!T)return!0;if(T?C=P:(C=P==="v"?"h":"v",T=hh(P,E)),!T)return!1;if(!r.current&&"changedTouches"in m&&(g||S)&&(r.current=C),!C)return!0;var L=r.current||C;return yR(L,x,m,L==="h"?g:S)},[]),a=y.useCallback(function(m){var x=m;if(!(!or.length||or[or.length-1]!==i)){var h="deltaY"in x?mh(x):Bi(x),p=t.current.filter(function(C){return C.name===x.type&&(C.target===x.target||x.target===C.shadowParent)&&wR(C.delta,h)})[0];if(p&&p.should){x.cancelable&&x.preventDefault();return}if(!p){var g=(s.current.shards||[]).map(gh).filter(Boolean).filter(function(C){return C.contains(x.target)}),S=g.length>0?l(x,g[0]):!s.current.noIsolation;S&&x.cancelable&&x.preventDefault()}}},[]),u=y.useCallback(function(m,x,h,p){var g={name:m,delta:x,target:h,should:p,shadowParent:PR(h)};t.current.push(g),setTimeout(function(){t.current=t.current.filter(function(S){return S!==g})},1)},[]),c=y.useCallback(function(m){n.current=Bi(m),r.current=void 0},[]),f=y.useCallback(function(m){u(m.type,mh(m),m.target,l(m,e.lockRef.current))},[]),d=y.useCallback(function(m){u(m.type,Bi(m),m.target,l(m,e.lockRef.current))},[]);y.useEffect(function(){return or.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:d}),document.addEventListener("wheel",a,rr),document.addEventListener("touchmove",a,rr),document.addEventListener("touchstart",c,rr),function(){or=or.filter(function(m){return m!==i}),document.removeEventListener("wheel",a,rr),document.removeEventListener("touchmove",a,rr),document.removeEventListener("touchstart",c,rr)}},[]);var v=e.removeScrollBar,w=e.inert;return y.createElement(y.Fragment,null,w?y.createElement(i,{styles:xR(o)}):null,v?y.createElement(fR,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function PR(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ER=q2(Yy,CR);var Jy=y.forwardRef(function(e,t){return y.createElement(dl,At({},e,{ref:t,sideCar:ER}))});Jy.classNames=dl.classNames;var TR=[" ","Enter","ArrowUp","ArrowDown"],kR=[" ","Enter"],Qn="Select",[pl,hl,RR]=zT(Qn),[qr,lA]=of(Qn,[RR,_y]),ml=_y(),[AR,Mn]=qr(Qn),[MR,bR]=qr(Qn),e0=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:s,defaultValue:l,onValueChange:a,dir:u,name:c,autoComplete:f,disabled:d,required:v,form:w}=e,m=ml(t),[x,h]=y.useState(null),[p,g]=y.useState(null),[S,C]=y.useState(!1),E=XT(u),[P,T]=Gp({prop:r,defaultProp:o??!1,onChange:i,caller:Qn}),[L,M]=Gp({prop:s,defaultProp:l,onChange:a,caller:Qn}),j=y.useRef(null),_=x?w||!!x.closest("form"):!0,[U,z]=y.useState(new Set),K=Array.from(U).map(F=>F.props.value).join(";");return k.jsx(L2,{...m,children:k.jsxs(AR,{required:v,scope:t,trigger:x,onTriggerChange:h,valueNode:p,onValueNodeChange:g,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:sf(),value:L,onValueChange:M,open:P,onOpenChange:T,dir:E,triggerPointerDownPosRef:j,disabled:d,children:[k.jsx(pl.Provider,{scope:t,children:k.jsx(MR,{scope:e.__scopeSelect,onNativeOptionAdd:y.useCallback(F=>{z(O=>new Set(O).add(F))},[]),onNativeOptionRemove:y.useCallback(F=>{z(O=>{const R=new Set(O);return R.delete(F),R})},[]),children:n})}),_?k.jsxs(T0,{"aria-hidden":!0,required:v,tabIndex:-1,name:c,autoComplete:f,value:L,onChange:F=>M(F.target.value),disabled:d,form:w,children:[L===void 0?k.jsx("option",{value:""}):null,Array.from(U)]},K):null]})})};e0.displayName=Qn;var t0="SelectTrigger",n0=y.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,i=ml(n),s=Mn(t0,n),l=s.disabled||r,a=Te(t,s.onTriggerChange),u=hl(n),c=y.useRef("touch"),[f,d,v]=R0(m=>{const x=u().filter(g=>!g.disabled),h=x.find(g=>g.value===s.value),p=A0(x,m,h);p!==void 0&&s.onValueChange(p.value)}),w=m=>{l||(s.onOpenChange(!0),v()),m&&(s.triggerPointerDownPosRef.current={x:Math.round(m.pageX),y:Math.round(m.pageY)})};return k.jsx(N2,{asChild:!0,...i,children:k.jsx(pe.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":k0(s.value)?"":void 0,...o,ref:a,onClick:he(o.onClick,m=>{m.currentTarget.focus(),c.current!=="mouse"&&w(m)}),onPointerDown:he(o.onPointerDown,m=>{c.current=m.pointerType;const x=m.target;x.hasPointerCapture(m.pointerId)&&x.releasePointerCapture(m.pointerId),m.button===0&&m.ctrlKey===!1&&m.pointerType==="mouse"&&(w(m),m.preventDefault())}),onKeyDown:he(o.onKeyDown,m=>{const x=f.current!=="";!(m.ctrlKey||m.altKey||m.metaKey)&&m.key.length===1&&d(m.key),!(x&&m.key===" ")&&TR.includes(m.key)&&(w(),m.preventDefault())})})})});n0.displayName=t0;var r0="SelectValue",o0=y.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:s="",...l}=e,a=Mn(r0,n),{onValueNodeHasChildrenChange:u}=a,c=i!==void 0,f=Te(t,a.onValueNodeChange);return We(()=>{u(c)},[u,c]),k.jsx(pe.span,{...l,ref:f,style:{pointerEvents:"none"},children:k0(a.value)?k.jsx(k.Fragment,{children:s}):i})});o0.displayName=r0;var LR="SelectIcon",i0=y.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return k.jsx(pe.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});i0.displayName=LR;var NR="SelectPortal",s0=e=>k.jsx(Uy,{asChild:!0,...e});s0.displayName=NR;var Zn="SelectContent",l0=y.forwardRef((e,t)=>{const n=Mn(Zn,e.__scopeSelect),[r,o]=y.useState();if(We(()=>{o(new DocumentFragment)},[]),!n.open){const i=r;return i?Hr.createPortal(k.jsx(a0,{scope:e.__scopeSelect,children:k.jsx(pl.Slot,{scope:e.__scopeSelect,children:k.jsx("div",{children:e.children})})}),i):null}return k.jsx(u0,{...e,ref:t})});l0.displayName=Zn;var dt=10,[a0,bn]=qr(Zn),DR="SelectContentImpl",_R=qo("SelectContent.RemoveScroll"),u0=y.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:l,sideOffset:a,align:u,alignOffset:c,arrowPadding:f,collisionBoundary:d,collisionPadding:v,sticky:w,hideWhenDetached:m,avoidCollisions:x,...h}=e,p=Mn(Zn,n),[g,S]=y.useState(null),[C,E]=y.useState(null),P=Te(t,I=>S(I)),[T,L]=y.useState(null),[M,j]=y.useState(null),_=hl(n),[U,z]=y.useState(!1),K=y.useRef(!1);y.useEffect(()=>{if(g)return $2(g)},[g]),ok();const F=y.useCallback(I=>{const[q,...Re]=_().map(X=>X.ref.current),[Q]=Re.slice(-1),Y=document.activeElement;for(const X of I)if(X===Y||(X==null||X.scrollIntoView({block:"nearest"}),X===q&&C&&(C.scrollTop=0),X===Q&&C&&(C.scrollTop=C.scrollHeight),X==null||X.focus(),document.activeElement!==Y))return},[_,C]),O=y.useCallback(()=>F([T,g]),[F,T,g]);y.useEffect(()=>{U&&O()},[U,O]);const{onOpenChange:R,triggerPointerDownPosRef:b}=p;y.useEffect(()=>{if(g){let I={x:0,y:0};const q=Q=>{var Y,X;I={x:Math.abs(Math.round(Q.pageX)-(((Y=b.current)==null?void 0:Y.x)??0)),y:Math.abs(Math.round(Q.pageY)-(((X=b.current)==null?void 0:X.y)??0))}},Re=Q=>{I.x<=10&&I.y<=10?Q.preventDefault():g.contains(Q.target)||R(!1),document.removeEventListener("pointermove",q),b.current=null};return b.current!==null&&(document.addEventListener("pointermove",q),document.addEventListener("pointerup",Re,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",q),document.removeEventListener("pointerup",Re,{capture:!0})}}},[g,R,b]),y.useEffect(()=>{const I=()=>R(!1);return window.addEventListener("blur",I),window.addEventListener("resize",I),()=>{window.removeEventListener("blur",I),window.removeEventListener("resize",I)}},[R]);const[D,B]=R0(I=>{const q=_().filter(Y=>!Y.disabled),Re=q.find(Y=>Y.ref.current===document.activeElement),Q=A0(q,I,Re);Q&&setTimeout(()=>Q.ref.current.focus())}),re=y.useCallback((I,q,Re)=>{const Q=!K.current&&!Re;(p.value!==void 0&&p.value===q||Q)&&(L(I),Q&&(K.current=!0))},[p.value]),St=y.useCallback(()=>g==null?void 0:g.focus(),[g]),ke=y.useCallback((I,q,Re)=>{const Q=!K.current&&!Re;(p.value!==void 0&&p.value===q||Q)&&j(I)},[p.value]),Ct=r==="popper"?ku:c0,_e=Ct===ku?{side:l,sideOffset:a,align:u,alignOffset:c,arrowPadding:f,collisionBoundary:d,collisionPadding:v,sticky:w,hideWhenDetached:m,avoidCollisions:x}:{};return k.jsx(a0,{scope:n,content:g,viewport:C,onViewportChange:E,itemRefCallback:re,selectedItem:T,onItemLeave:St,itemTextRefCallback:ke,focusSelectedItem:O,selectedItemText:M,position:r,isPositioned:U,searchRef:D,children:k.jsx(Jy,{as:_R,allowPinchZoom:!0,children:k.jsx(wy,{asChild:!0,trapped:p.open,onMountAutoFocus:I=>{I.preventDefault()},onUnmountAutoFocus:he(o,I=>{var q;(q=p.trigger)==null||q.focus({preventScroll:!0}),I.preventDefault()}),children:k.jsx(vy,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:I=>I.preventDefault(),onDismiss:()=>p.onOpenChange(!1),children:k.jsx(Ct,{role:"listbox",id:p.contentId,"data-state":p.open?"open":"closed",dir:p.dir,onContextMenu:I=>I.preventDefault(),...h,..._e,onPlaced:()=>z(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...h.style},onKeyDown:he(h.onKeyDown,I=>{const q=I.ctrlKey||I.altKey||I.metaKey;if(I.key==="Tab"&&I.preventDefault(),!q&&I.key.length===1&&B(I.key),["ArrowUp","ArrowDown","Home","End"].includes(I.key)){let Q=_().filter(Y=>!Y.disabled).map(Y=>Y.ref.current);if(["ArrowUp","End"].includes(I.key)&&(Q=Q.slice().reverse()),["ArrowUp","ArrowDown"].includes(I.key)){const Y=I.target,X=Q.indexOf(Y);Q=Q.slice(X+1)}setTimeout(()=>F(Q)),I.preventDefault()}})})})})})})});u0.displayName=DR;var OR="SelectItemAlignedPosition",c0=y.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,i=Mn(Zn,n),s=bn(Zn,n),[l,a]=y.useState(null),[u,c]=y.useState(null),f=Te(t,P=>c(P)),d=hl(n),v=y.useRef(!1),w=y.useRef(!0),{viewport:m,selectedItem:x,selectedItemText:h,focusSelectedItem:p}=s,g=y.useCallback(()=>{if(i.trigger&&i.valueNode&&l&&u&&m&&x&&h){const P=i.trigger.getBoundingClientRect(),T=u.getBoundingClientRect(),L=i.valueNode.getBoundingClientRect(),M=h.getBoundingClientRect();if(i.dir!=="rtl"){const Y=M.left-T.left,X=L.left-Y,He=P.left-X,Pt=P.width+He,Jr=Math.max(Pt,T.width),eo=window.innerWidth-dt,to=Yp(X,[dt,Math.max(dt,eo-Jr)]);l.style.minWidth=Pt+"px",l.style.left=to+"px"}else{const Y=T.right-M.right,X=window.innerWidth-L.right-Y,He=window.innerWidth-P.right-X,Pt=P.width+He,Jr=Math.max(Pt,T.width),eo=window.innerWidth-dt,to=Yp(X,[dt,Math.max(dt,eo-Jr)]);l.style.minWidth=Pt+"px",l.style.right=to+"px"}const j=d(),_=window.innerHeight-dt*2,U=m.scrollHeight,z=window.getComputedStyle(u),K=parseInt(z.borderTopWidth,10),F=parseInt(z.paddingTop,10),O=parseInt(z.borderBottomWidth,10),R=parseInt(z.paddingBottom,10),b=K+F+U+R+O,D=Math.min(x.offsetHeight*5,b),B=window.getComputedStyle(m),re=parseInt(B.paddingTop,10),St=parseInt(B.paddingBottom,10),ke=P.top+P.height/2-dt,Ct=_-ke,_e=x.offsetHeight/2,I=x.offsetTop+_e,q=K+F+I,Re=b-q;if(q<=ke){const Y=j.length>0&&x===j[j.length-1].ref.current;l.style.bottom="0px";const X=u.clientHeight-m.offsetTop-m.offsetHeight,He=Math.max(Ct,_e+(Y?St:0)+X+O),Pt=q+He;l.style.height=Pt+"px"}else{const Y=j.length>0&&x===j[0].ref.current;l.style.top="0px";const He=Math.max(ke,K+m.offsetTop+(Y?re:0)+_e)+Re;l.style.height=He+"px",m.scrollTop=q-ke+m.offsetTop}l.style.margin=`${dt}px 0`,l.style.minHeight=D+"px",l.style.maxHeight=_+"px",r==null||r(),requestAnimationFrame(()=>v.current=!0)}},[d,i.trigger,i.valueNode,l,u,m,x,h,i.dir,r]);We(()=>g(),[g]);const[S,C]=y.useState();We(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);const E=y.useCallback(P=>{P&&w.current===!0&&(g(),p==null||p(),w.current=!1)},[g,p]);return k.jsx(IR,{scope:n,contentWrapper:l,shouldExpandOnScrollRef:v,onScrollButtonChange:E,children:k.jsx("div",{ref:a,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:k.jsx(pe.div,{...o,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});c0.displayName=OR;var VR="SelectPopperPosition",ku=y.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=dt,...i}=e,s=ml(n);return k.jsx(D2,{...s,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ku.displayName=VR;var[IR,gf]=qr(Zn,{}),Ru="SelectViewport",f0=y.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,i=bn(Ru,n),s=gf(Ru,n),l=Te(t,i.onViewportChange),a=y.useRef(0);return k.jsxs(k.Fragment,{children:[k.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),k.jsx(pl.Slot,{scope:n,children:k.jsx(pe.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:he(o.onScroll,u=>{const c=u.currentTarget,{contentWrapper:f,shouldExpandOnScrollRef:d}=s;if(d!=null&&d.current&&f){const v=Math.abs(a.current-c.scrollTop);if(v>0){const w=window.innerHeight-dt*2,m=parseFloat(f.style.minHeight),x=parseFloat(f.style.height),h=Math.max(m,x);if(h<w){const p=h+v,g=Math.min(w,p),S=p-g;f.style.height=g+"px",f.style.bottom==="0px"&&(c.scrollTop=S>0?S:0,f.style.justifyContent="flex-end")}}}a.current=c.scrollTop})})})]})});f0.displayName=Ru;var d0="SelectGroup",[jR,FR]=qr(d0),p0=y.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=sf();return k.jsx(jR,{scope:n,id:o,children:k.jsx(pe.div,{role:"group","aria-labelledby":o,...r,ref:t})})});p0.displayName=d0;var h0="SelectLabel",m0=y.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=FR(h0,n);return k.jsx(pe.div,{id:o.id,...r,ref:t})});m0.displayName=h0;var Bs="SelectItem",[zR,g0]=qr(Bs),v0=y.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...s}=e,l=Mn(Bs,n),a=bn(Bs,n),u=l.value===r,[c,f]=y.useState(i??""),[d,v]=y.useState(!1),w=Te(t,p=>{var g;return(g=a.itemRefCallback)==null?void 0:g.call(a,p,r,o)}),m=sf(),x=y.useRef("touch"),h=()=>{o||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return k.jsx(zR,{scope:n,value:r,disabled:o,textId:m,isSelected:u,onItemTextChange:y.useCallback(p=>{f(g=>g||((p==null?void 0:p.textContent)??"").trim())},[]),children:k.jsx(pl.ItemSlot,{scope:n,value:r,disabled:o,textValue:c,children:k.jsx(pe.div,{role:"option","aria-labelledby":m,"data-highlighted":d?"":void 0,"aria-selected":u&&d,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:w,onFocus:he(s.onFocus,()=>v(!0)),onBlur:he(s.onBlur,()=>v(!1)),onClick:he(s.onClick,()=>{x.current!=="mouse"&&h()}),onPointerUp:he(s.onPointerUp,()=>{x.current==="mouse"&&h()}),onPointerDown:he(s.onPointerDown,p=>{x.current=p.pointerType}),onPointerMove:he(s.onPointerMove,p=>{var g;x.current=p.pointerType,o?(g=a.onItemLeave)==null||g.call(a):x.current==="mouse"&&p.currentTarget.focus({preventScroll:!0})}),onPointerLeave:he(s.onPointerLeave,p=>{var g;p.currentTarget===document.activeElement&&((g=a.onItemLeave)==null||g.call(a))}),onKeyDown:he(s.onKeyDown,p=>{var S;((S=a.searchRef)==null?void 0:S.current)!==""&&p.key===" "||(kR.includes(p.key)&&h(),p.key===" "&&p.preventDefault())})})})})});v0.displayName=Bs;var So="SelectItemText",y0=y.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...i}=e,s=Mn(So,n),l=bn(So,n),a=g0(So,n),u=bR(So,n),[c,f]=y.useState(null),d=Te(t,h=>f(h),a.onItemTextChange,h=>{var p;return(p=l.itemTextRefCallback)==null?void 0:p.call(l,h,a.value,a.disabled)}),v=c==null?void 0:c.textContent,w=y.useMemo(()=>k.jsx("option",{value:a.value,disabled:a.disabled,children:v},a.value),[a.disabled,a.value,v]),{onNativeOptionAdd:m,onNativeOptionRemove:x}=u;return We(()=>(m(w),()=>x(w)),[m,x,w]),k.jsxs(k.Fragment,{children:[k.jsx(pe.span,{id:a.textId,...i,ref:d}),a.isSelected&&s.valueNode&&!s.valueNodeHasChildren?Hr.createPortal(i.children,s.valueNode):null]})});y0.displayName=So;var w0="SelectItemIndicator",x0=y.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return g0(w0,n).isSelected?k.jsx(pe.span,{"aria-hidden":!0,...r,ref:t}):null});x0.displayName=w0;var Au="SelectScrollUpButton",S0=y.forwardRef((e,t)=>{const n=bn(Au,e.__scopeSelect),r=gf(Au,e.__scopeSelect),[o,i]=y.useState(!1),s=Te(t,r.onScrollButtonChange);return We(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=a.scrollTop>0;i(u)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?k.jsx(P0,{...e,ref:s,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop-a.offsetHeight)}}):null});S0.displayName=Au;var Mu="SelectScrollDownButton",C0=y.forwardRef((e,t)=>{const n=bn(Mu,e.__scopeSelect),r=gf(Mu,e.__scopeSelect),[o,i]=y.useState(!1),s=Te(t,r.onScrollButtonChange);return We(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=a.scrollHeight-a.clientHeight,c=Math.ceil(a.scrollTop)<u;i(c)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?k.jsx(P0,{...e,ref:s,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop+a.offsetHeight)}}):null});C0.displayName=Mu;var P0=y.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,i=bn("SelectScrollButton",n),s=y.useRef(null),l=hl(n),a=y.useCallback(()=>{s.current!==null&&(window.clearInterval(s.current),s.current=null)},[]);return y.useEffect(()=>()=>a(),[a]),We(()=>{var c;const u=l().find(f=>f.ref.current===document.activeElement);(c=u==null?void 0:u.ref.current)==null||c.scrollIntoView({block:"nearest"})},[l]),k.jsx(pe.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:he(o.onPointerDown,()=>{s.current===null&&(s.current=window.setInterval(r,50))}),onPointerMove:he(o.onPointerMove,()=>{var u;(u=i.onItemLeave)==null||u.call(i),s.current===null&&(s.current=window.setInterval(r,50))}),onPointerLeave:he(o.onPointerLeave,()=>{a()})})}),BR="SelectSeparator",E0=y.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return k.jsx(pe.div,{"aria-hidden":!0,...r,ref:t})});E0.displayName=BR;var bu="SelectArrow",$R=y.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=ml(n),i=Mn(bu,n),s=bn(bu,n);return i.open&&s.position==="popper"?k.jsx(_2,{...o,...r,ref:t}):null});$R.displayName=bu;var UR="SelectBubbleInput",T0=y.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{const o=y.useRef(null),i=Te(r,o),s=V2(t);return y.useEffect(()=>{const l=o.current;if(!l)return;const a=window.HTMLSelectElement.prototype,c=Object.getOwnPropertyDescriptor(a,"value").set;if(s!==t&&c){const f=new Event("change",{bubbles:!0});c.call(l,t),l.dispatchEvent(f)}},[s,t]),k.jsx(pe.select,{...n,style:{...Wy,...n.style},ref:i,defaultValue:t})});T0.displayName=UR;function k0(e){return e===""||e===void 0}function R0(e){const t=Yn(e),n=y.useRef(""),r=y.useRef(0),o=y.useCallback(s=>{const l=n.current+s;t(l),function a(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>a(""),1e3))}(l)},[t]),i=y.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return y.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function A0(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let s=WR(e,Math.max(i,0));o.length===1&&(s=s.filter(u=>u!==n));const a=s.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}function WR(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var HR=e0,M0=n0,KR=o0,GR=i0,b0=s0,gl=l0,L0=f0,YR=p0,N0=m0,D0=v0,XR=y0,QR=x0,_0=S0,O0=C0,V0=E0;const aA=HR,uA=YR,cA=KR,ZR={zoom:{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{type:"spring",stiffness:600,damping:25}},scaleBounce:{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:[1.2,.9,1]},exit:{opacity:0,scale:.5},transition:{type:"spring",stiffness:600,damping:20}},fade:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3}},slideUp:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{type:"spring",stiffness:500,damping:20}},slideDown:{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{type:"spring",stiffness:500,damping:20}},slideRight:{initial:{opacity:0,x:-30},animate:{opacity:1,x:0},exit:{opacity:0,x:-30},transition:{type:"spring",stiffness:400,damping:20}},slideLeft:{initial:{opacity:0,x:30},animate:{opacity:1,x:0},exit:{opacity:0,x:30},transition:{type:"spring",stiffness:400,damping:20}},flip:{initial:{opacity:0,rotateY:90},animate:{opacity:1,rotateY:0},exit:{opacity:0,rotateY:90},transition:{type:"spring",stiffness:500,damping:30}},rotate:{initial:{opacity:0,rotate:-180},animate:{opacity:1,rotate:0},exit:{opacity:0,rotate:-180},transition:{type:"spring",stiffness:500,damping:25}}},qR=y.forwardRef(({className:e,children:t,...n},r)=>k.jsxs(M0,{ref:r,className:we("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:border-primary/75 focus:dark:ring-primary/35 focus:ring-primary/35 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,k.jsx(GR,{asChild:!0,children:k.jsx(RT,{className:"h-4 w-4 opacity-50"})})]}));qR.displayName=M0.displayName;const vf=y.forwardRef(({className:e,...t},n)=>k.jsx(_0,{ref:n,className:we("flex cursor-default items-center justify-center py-1 w-full absolute top-0 bg-popover z-10",e),...t,children:k.jsx(DT,{})}));vf.displayName=_0.displayName;const yf=y.forwardRef(({className:e,...t},n)=>k.jsx(O0,{ref:n,className:we("flex cursor-default items-center justify-center py-1 w-full absolute bottom-0 bg-popover z-10",e),...t,children:k.jsx(LT,{})}));yf.displayName=O0.displayName;const I0=y.forwardRef(({className:e,children:t,position:n="popper",selectedVariant:r,...o},i)=>k.jsx(b0,{children:k.jsx(gl,{className:we("relative z-50 max-h-96 my-2 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),ref:i,asChild:!0,position:n,...o,children:k.jsxs(hy.div,{className:we("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md",e),...ZR[r],children:[k.jsx(vf,{}),k.jsx(L0,{className:we("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),k.jsx(yf,{})]})})}));I0.displayName=gl.displayName;const JR=y.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>k.jsx(b0,{children:k.jsxs(gl,{ref:o,className:we("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[k.jsx(vf,{}),k.jsx(L0,{className:we("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),k.jsx(yf,{})]})}));JR.displayName=gl.displayName;const eA=y.forwardRef((e,t)=>{const n=e.variants||"zoom";return k.jsx(I0,{selectedVariant:n,ref:t,...e})});eA.displayName="SelectContent";const tA=y.forwardRef(({className:e,...t},n)=>k.jsx(N0,{ref:n,className:we("px-2 py-1.5 text-sm font-semibold",e),...t}));tA.displayName=N0.displayName;const nA=y.forwardRef(({className:e,children:t,...n},r)=>k.jsxs(D0,{ref:r,className:we("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-primary focus:text-primary-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[k.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:k.jsx(QR,{children:k.jsx(MT,{className:"h-4 w-4"})})}),k.jsx(XR,{children:t})]}));nA.displayName=D0.displayName;const rA=y.forwardRef(({className:e,...t},n)=>k.jsx(V0,{ref:n,className:we("-mx-1 my-1 h-px bg-muted",e),...t}));rA.displayName=V0.displayName;export{N2 as A,TT as B,D2 as C,vy as D,qR as E,wy as F,cA as G,eA as H,uA as I,tA as J,rA as K,nA as L,my as M,dx as N,jg as O,pe as P,jT as Q,kt as R,aA as S,iA as V,We as a,of as b,wx as c,Gp as d,sf as e,he as f,zT as g,XT as h,we as i,k as j,vh as k,Yn as l,Yp as m,_y as n,Lg as o,Uy as p,ok as q,y as r,Jy as s,qo as t,Te as u,$2 as v,HT as w,L2 as x,_2 as y,sA as z};
