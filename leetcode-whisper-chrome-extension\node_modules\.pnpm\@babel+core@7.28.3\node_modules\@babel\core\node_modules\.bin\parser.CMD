@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\parser\bin\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\parser\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\parser\bin\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\parser\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\@babel+parser@7.28.3\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\@babel+parser@7.28.3\node_modules\@babel\parser\bin\babel-parser.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\@babel+parser@7.28.3\node_modules\@babel\parser\bin\babel-parser.js" %*
)
