var oc=Object.defineProperty;var ic=(e,t,n)=>t in e?oc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var We=(e,t,n)=>ic(e,typeof t!="symbol"?t+"":t,n);import{r as y,c as js,u as xe,a as fn,b as wt,d as xt,j as p,e as ot,P as ye,f as z,R as ie,g as ea,h as Fn,i as me,k as lc,V as ft,l as Me,m as cc,n as Ms,o as Ds,p as uc,A as dc,q as pc,s as fc,t as mc,F as gc,D as hc,C as yc,v as vc,w as bc,x as Fs,y as _c,z as Kn,B as Tt,S as wc,E as xc,G as kc,H as Sc,I as Ec,J as Ac,K as Cc,L as Tc,M as Ic,N as Rc}from"./select-DdeiFjEC.js";/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nc=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),$s=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Pc={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oc=y.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:a="",children:s,iconNode:o,...i},l)=>y.createElement("svg",{ref:l,...Pc,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:$s("lucide",a),...i},[...o.map(([c,u])=>y.createElement(c,u)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=(e,t)=>{const n=y.forwardRef(({className:r,...a},s)=>y.createElement(Oc,{ref:s,iconNode:t,className:$s(`lucide-${Nc(e)}`,r),...a}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=ze("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jc=ze("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mc=ze("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dc=ze("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=ze("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $c=ze("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lc=ze("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bc=ze("Eraser",[["path",{d:"m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21",key:"182aya"}],["path",{d:"M22 21H7",key:"t4ddhn"}],["path",{d:"m5 11 9 9",key:"1mo9qw"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=ze("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.456.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zc=ze("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var Zc=Object.create,$n=Object.defineProperty,Gc=Object.defineProperties,Vc=Object.getOwnPropertyDescriptor,qc=Object.getOwnPropertyDescriptors,Ls=Object.getOwnPropertyNames,mn=Object.getOwnPropertySymbols,Hc=Object.getPrototypeOf,ta=Object.prototype.hasOwnProperty,Bs=Object.prototype.propertyIsEnumerable,Ba=(e,t,n)=>t in e?$n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Le=(e,t)=>{for(var n in t||(t={}))ta.call(t,n)&&Ba(e,n,t[n]);if(mn)for(var n of mn(t))Bs.call(t,n)&&Ba(e,n,t[n]);return e},Ln=(e,t)=>Gc(e,qc(t)),Us=(e,t)=>{var n={};for(var r in e)ta.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&mn)for(var r of mn(e))t.indexOf(r)<0&&Bs.call(e,r)&&(n[r]=e[r]);return n},Wc=(e,t)=>function(){return t||(0,e[Ls(e)[0]])((t={exports:{}}).exports,t),t.exports},Kc=(e,t)=>{for(var n in t)$n(e,n,{get:t[n],enumerable:!0})},Yc=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Ls(t))!ta.call(e,a)&&a!==n&&$n(e,a,{get:()=>t[a],enumerable:!(r=Vc(t,a))||r.enumerable});return e},Jc=(e,t,n)=>(n=e!=null?Zc(Hc(e)):{},Yc(!e||!e.__esModule?$n(n,"default",{value:e,enumerable:!0}):n,e)),Xc=Wc({"../../node_modules/.pnpm/prismjs@1.29.0_patch_hash=vrxx3pzkik6jpmgpayxfjunetu/node_modules/prismjs/prism.js"(e,t){var n=function(){var r=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,a=0,s={},o={util:{encode:function f(m){return m instanceof i?new i(m.type,f(m.content),m.alias):Array.isArray(m)?m.map(f):m.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(f){return Object.prototype.toString.call(f).slice(8,-1)},objId:function(f){return f.__id||Object.defineProperty(f,"__id",{value:++a}),f.__id},clone:function f(m,h){h=h||{};var b,_;switch(o.util.type(m)){case"Object":if(_=o.util.objId(m),h[_])return h[_];b={},h[_]=b;for(var w in m)m.hasOwnProperty(w)&&(b[w]=f(m[w],h));return b;case"Array":return _=o.util.objId(m),h[_]?h[_]:(b=[],h[_]=b,m.forEach(function(A,O){b[O]=f(A,h)}),b);default:return m}},getLanguage:function(f){for(;f;){var m=r.exec(f.className);if(m)return m[1].toLowerCase();f=f.parentElement}return"none"},setLanguage:function(f,m){f.className=f.className.replace(RegExp(r,"gi"),""),f.classList.add("language-"+m)},isActive:function(f,m,h){for(var b="no-"+m;f;){var _=f.classList;if(_.contains(m))return!0;if(_.contains(b))return!1;f=f.parentElement}return!!h}},languages:{plain:s,plaintext:s,text:s,txt:s,extend:function(f,m){var h=o.util.clone(o.languages[f]);for(var b in m)h[b]=m[b];return h},insertBefore:function(f,m,h,b){b=b||o.languages;var _=b[f],w={};for(var A in _)if(_.hasOwnProperty(A)){if(A==m)for(var O in h)h.hasOwnProperty(O)&&(w[O]=h[O]);h.hasOwnProperty(A)||(w[A]=_[A])}var N=b[f];return b[f]=w,o.languages.DFS(o.languages,function(F,D){D===N&&F!=f&&(this[F]=w)}),w},DFS:function f(m,h,b,_){_=_||{};var w=o.util.objId;for(var A in m)if(m.hasOwnProperty(A)){h.call(m,A,m[A],b||A);var O=m[A],N=o.util.type(O);N==="Object"&&!_[w(O)]?(_[w(O)]=!0,f(O,h,null,_)):N==="Array"&&!_[w(O)]&&(_[w(O)]=!0,f(O,h,A,_))}}},plugins:{},highlight:function(f,m,h){var b={code:f,grammar:m,language:h};if(o.hooks.run("before-tokenize",b),!b.grammar)throw new Error('The language "'+b.language+'" has no grammar.');return b.tokens=o.tokenize(b.code,b.grammar),o.hooks.run("after-tokenize",b),i.stringify(o.util.encode(b.tokens),b.language)},tokenize:function(f,m){var h=m.rest;if(h){for(var b in h)m[b]=h[b];delete m.rest}var _=new u;return d(_,_.head,f),c(f,_,m,_.head,0),v(_)},hooks:{all:{},add:function(f,m){var h=o.hooks.all;h[f]=h[f]||[],h[f].push(m)},run:function(f,m){var h=o.hooks.all[f];if(!(!h||!h.length))for(var b=0,_;_=h[b++];)_(m)}},Token:i};function i(f,m,h,b){this.type=f,this.content=m,this.alias=h,this.length=(b||"").length|0}i.stringify=function f(m,h){if(typeof m=="string")return m;if(Array.isArray(m)){var b="";return m.forEach(function(N){b+=f(N,h)}),b}var _={type:m.type,content:f(m.content,h),tag:"span",classes:["token",m.type],attributes:{},language:h},w=m.alias;w&&(Array.isArray(w)?Array.prototype.push.apply(_.classes,w):_.classes.push(w)),o.hooks.run("wrap",_);var A="";for(var O in _.attributes)A+=" "+O+'="'+(_.attributes[O]||"").replace(/"/g,"&quot;")+'"';return"<"+_.tag+' class="'+_.classes.join(" ")+'"'+A+">"+_.content+"</"+_.tag+">"};function l(f,m,h,b){f.lastIndex=m;var _=f.exec(h);if(_&&b&&_[1]){var w=_[1].length;_.index+=w,_[0]=_[0].slice(w)}return _}function c(f,m,h,b,_,w){for(var A in h)if(!(!h.hasOwnProperty(A)||!h[A])){var O=h[A];O=Array.isArray(O)?O:[O];for(var N=0;N<O.length;++N){if(w&&w.cause==A+","+N)return;var F=O[N],D=F.inside,I=!!F.lookbehind,$=!!F.greedy,G=F.alias;if($&&!F.pattern.global){var J=F.pattern.toString().match(/[imsuy]*$/)[0];F.pattern=RegExp(F.pattern.source,J+"g")}for(var he=F.pattern||F,X=b.next,ue=_;X!==m.tail&&!(w&&ue>=w.reach);ue+=X.value.length,X=X.next){var le=X.value;if(m.length>f.length)return;if(!(le instanceof i)){var de=1,E;if($){if(E=l(he,ue,f,I),!E||E.index>=f.length)break;var B=E.index,C=E.index+E[0].length,L=ue;for(L+=X.value.length;B>=L;)X=X.next,L+=X.value.length;if(L-=X.value.length,ue=L,X.value instanceof i)continue;for(var W=X;W!==m.tail&&(L<C||typeof W.value=="string");W=W.next)de++,L+=W.value.length;de--,le=f.slice(ue,L),E.index-=ue}else if(E=l(he,0,le,I),!E)continue;var B=E.index,te=E[0],oe=le.slice(0,B),ae=le.slice(B+te.length),pe=ue+le.length;w&&pe>w.reach&&(w.reach=pe);var se=X.prev;oe&&(se=d(m,se,oe),ue+=oe.length),g(m,se,de);var be=new i(A,D?o.tokenize(te,D):te,G,te);if(X=d(m,se,be),ae&&d(m,X,ae),de>1){var K={cause:A+","+N,reach:pe};c(f,m,h,X.prev,ue,K),w&&K.reach>w.reach&&(w.reach=K.reach)}}}}}}function u(){var f={value:null,prev:null,next:null},m={value:null,prev:f,next:null};f.next=m,this.head=f,this.tail=m,this.length=0}function d(f,m,h){var b=m.next,_={value:h,prev:m,next:b};return m.next=_,b.prev=_,f.length++,_}function g(f,m,h){for(var b=m.next,_=0;_<h&&b!==f.tail;_++)b=b.next;m.next=b,b.prev=m,f.length-=_}function v(f){for(var m=[],h=f.head.next;h!==f.tail;)m.push(h.value),h=h.next;return m}return o}();t.exports=n,n.default=n}}),x=Jc(Xc());x.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},x.languages.markup.tag.inside["attr-value"].inside.entity=x.languages.markup.entity,x.languages.markup.doctype.inside["internal-subset"].inside=x.languages.markup,x.hooks.add("wrap",function(e){e.type==="entity"&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(x.languages.markup.tag,"addInlined",{value:function(e,r){var n={},n=(n["language-"+r]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:x.languages[r]},n.cdata=/^<!\[CDATA\[|\]\]>$/i,{"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}}),r=(n["language-"+r]={pattern:/[\s\S]+/,inside:x.languages[r]},{});r[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return e}),"i"),lookbehind:!0,greedy:!0,inside:n},x.languages.insertBefore("markup","cdata",r)}}),Object.defineProperty(x.languages.markup.tag,"addAttribute",{value:function(e,t){x.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:x.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),x.languages.html=x.languages.markup,x.languages.mathml=x.languages.markup,x.languages.svg=x.languages.markup,x.languages.xml=x.languages.extend("markup",{}),x.languages.ssml=x.languages.xml,x.languages.atom=x.languages.xml,x.languages.rss=x.languages.xml,function(e){var t={pattern:/\\[\\(){}[\]^$+*?|.]/,alias:"escape"},n=/\\(?:x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]+\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/,r="(?:[^\\\\-]|"+n.source+")",r=RegExp(r+"-"+r),a={pattern:/(<|')[^<>']+(?=[>']$)/,lookbehind:!0,alias:"variable"};e.languages.regex={"char-class":{pattern:/((?:^|[^\\])(?:\\\\)*)\[(?:[^\\\]]|\\[\s\S])*\]/,lookbehind:!0,inside:{"char-class-negation":{pattern:/(^\[)\^/,lookbehind:!0,alias:"operator"},"char-class-punctuation":{pattern:/^\[|\]$/,alias:"punctuation"},range:{pattern:r,inside:{escape:n,"range-punctuation":{pattern:/-/,alias:"operator"}}},"special-escape":t,"char-set":{pattern:/\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},escape:n}},"special-escape":t,"char-set":{pattern:/\.|\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},backreference:[{pattern:/\\(?![123][0-7]{2})[1-9]/,alias:"keyword"},{pattern:/\\k<[^<>']+>/,alias:"keyword",inside:{"group-name":a}}],anchor:{pattern:/[$^]|\\[ABbGZz]/,alias:"function"},escape:n,group:[{pattern:/\((?:\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,alias:"punctuation",inside:{"group-name":a}},{pattern:/\)/,alias:"punctuation"}],quantifier:{pattern:/(?:[+*?]|\{\d+(?:,\d*)?\})[?+]?/,alias:"number"},alternation:{pattern:/\|/,alias:"keyword"}}}(x),x.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},x.languages.javascript=x.languages.extend("clike",{"class-name":[x.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),x.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,x.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:x.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:x.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:x.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:x.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:x.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),x.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:x.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),x.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),x.languages.markup&&(x.languages.markup.tag.addInlined("script","javascript"),x.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),x.languages.js=x.languages.javascript,x.languages.actionscript=x.languages.extend("javascript",{keyword:/\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\b/,operator:/\+\+|--|(?:[+\-*\/%^]|&&?|\|\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/}),x.languages.actionscript["class-name"].alias="function",delete x.languages.actionscript.parameter,delete x.languages.actionscript["literal-property"],x.languages.markup&&x.languages.insertBefore("actionscript","string",{xml:{pattern:/(^|[^.])<\/?\w+(?:\s+[^\s>\/=]+=("|')(?:\\[\s\S]|(?!\2)[^\\])*\2)*\s*\/?>/,lookbehind:!0,inside:x.languages.markup}}),function(e){var t=/#(?!\{).+/,n={pattern:/#\{[^}]+\}/,alias:"variable"};e.languages.coffeescript=e.languages.extend("javascript",{comment:t,string:[{pattern:/'(?:\\[\s\S]|[^\\'])*'/,greedy:!0},{pattern:/"(?:\\[\s\S]|[^\\"])*"/,greedy:!0,inside:{interpolation:n}}],keyword:/\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\b/,"class-member":{pattern:/@(?!\d)\w+/,alias:"variable"}}),e.languages.insertBefore("coffeescript","comment",{"multiline-comment":{pattern:/###[\s\S]+?###/,alias:"comment"},"block-regex":{pattern:/\/{3}[\s\S]*?\/{3}/,alias:"regex",inside:{comment:t,interpolation:n}}}),e.languages.insertBefore("coffeescript","string",{"inline-javascript":{pattern:/`(?:\\[\s\S]|[^\\`])*`/,inside:{delimiter:{pattern:/^`|`$/,alias:"punctuation"},script:{pattern:/[\s\S]+/,alias:"language-javascript",inside:e.languages.javascript}}},"multiline-string":[{pattern:/'''[\s\S]*?'''/,greedy:!0,alias:"string"},{pattern:/"""[\s\S]*?"""/,greedy:!0,alias:"string",inside:{interpolation:n}}]}),e.languages.insertBefore("coffeescript","keyword",{property:/(?!\d)\w+(?=\s*:(?!:))/}),delete e.languages.coffeescript["template-string"],e.languages.coffee=e.languages.coffeescript}(x),function(e){var t=e.languages.javadoclike={parameter:{pattern:/(^[\t ]*(?:\/{3}|\*|\/\*\*)\s*@(?:arg|arguments|param)\s+)\w+/m,lookbehind:!0},keyword:{pattern:/(^[\t ]*(?:\/{3}|\*|\/\*\*)\s*|\{)@[a-z][a-zA-Z-]+\b/m,lookbehind:!0},punctuation:/[{}]/};Object.defineProperty(t,"addSupport",{value:function(n,r){(n=typeof n=="string"?[n]:n).forEach(function(a){var s=function(d){d.inside||(d.inside={}),d.inside.rest=r},o="doc-comment";if(i=e.languages[a]){var i,l=i[o];if((l=l||(i=e.languages.insertBefore(a,"comment",{"doc-comment":{pattern:/(^|[^\\])\/\*\*[^/][\s\S]*?(?:\*\/|$)/,lookbehind:!0,alias:"comment"}}))[o])instanceof RegExp&&(l=i[o]={pattern:l}),Array.isArray(l))for(var c=0,u=l.length;c<u;c++)l[c]instanceof RegExp&&(l[c]={pattern:l[c]}),s(l[c]);else s(l)}})}}),t.addSupport(["java","javascript","php"],t)}(x),function(e){var t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,t=(e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+t.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css,e.languages.markup);t&&(t.tag.addInlined("style","css"),t.tag.addAttribute("style","css"))}(x),function(e){var t=/("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,t=(e.languages.css.selector={pattern:e.languages.css.selector.pattern,lookbehind:!0,inside:t={"pseudo-element":/:(?:after|before|first-letter|first-line|selection)|::[-\w]+/,"pseudo-class":/:[-\w]+/,class:/\.[-\w]+/,id:/#[-\w]+/,attribute:{pattern:RegExp(`\\[(?:[^[\\]"']|`+t.source+")*\\]"),greedy:!0,inside:{punctuation:/^\[|\]$/,"case-sensitivity":{pattern:/(\s)[si]$/i,lookbehind:!0,alias:"keyword"},namespace:{pattern:/^(\s*)(?:(?!\s)[-*\w\xA0-\uFFFF])*\|(?!=)/,lookbehind:!0,inside:{punctuation:/\|$/}},"attr-name":{pattern:/^(\s*)(?:(?!\s)[-\w\xA0-\uFFFF])+/,lookbehind:!0},"attr-value":[t,{pattern:/(=\s*)(?:(?!\s)[-\w\xA0-\uFFFF])+(?=\s*$)/,lookbehind:!0}],operator:/[|~*^$]?=/}},"n-th":[{pattern:/(\(\s*)[+-]?\d*[\dn](?:\s*[+-]\s*\d+)?(?=\s*\))/,lookbehind:!0,inside:{number:/[\dn]+/,operator:/[+-]/}},{pattern:/(\(\s*)(?:even|odd)(?=\s*\))/i,lookbehind:!0}],combinator:/>|\+|~|\|\|/,punctuation:/[(),]/}},e.languages.css.atrule.inside["selector-function-argument"].inside=t,e.languages.insertBefore("css","property",{variable:{pattern:/(^|[^-\w\xA0-\uFFFF])--(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*/i,lookbehind:!0}}),{pattern:/(\b\d+)(?:%|[a-z]+(?![\w-]))/,lookbehind:!0}),n={pattern:/(^|[^\w.-])-?(?:\d+(?:\.\d+)?|\.\d+)/,lookbehind:!0};e.languages.insertBefore("css","function",{operator:{pattern:/(\s)[+\-*\/](?=\s)/,lookbehind:!0},hexcode:{pattern:/\B#[\da-f]{3,8}\b/i,alias:"color"},color:[{pattern:/(^|[^\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|RebeccaPurple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\w-])/i,lookbehind:!0},{pattern:/\b(?:hsl|rgb)\(\s*\d{1,3}\s*,\s*\d{1,3}%?\s*,\s*\d{1,3}%?\s*\)\B|\b(?:hsl|rgb)a\(\s*\d{1,3}\s*,\s*\d{1,3}%?\s*,\s*\d{1,3}%?\s*,\s*(?:0|0?\.\d+|1)\s*\)\B/i,inside:{unit:t,number:n,function:/[\w-]+(?=\()/,punctuation:/[(),]/}}],entity:/\\[\da-f]{1,8}/i,unit:t,number:n})}(x),function(e){var t=/[*&][^\s[\]{},]+/,n=/!(?:<[\w\-%#;/?:@&=+$,.!~*'()[\]]+>|(?:[a-zA-Z\d-]*!)?[\w\-%#;/?:@&=+$.~*'()]+)?/,r="(?:"+n.source+"(?:[ 	]+"+t.source+")?|"+t.source+"(?:[ 	]+"+n.source+")?)",a=/(?:[^\s\x00-\x08\x0e-\x1f!"#%&'*,\-:>?@[\]`{|}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]|[?:-]<PLAIN>)(?:[ \t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,function(){return/[^\s\x00-\x08\x0e-\x1f,[\]{}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]/.source}),s=/"(?:[^"\\\r\n]|\\.)*"|'(?:[^'\\\r\n]|\\.)*'/.source;function o(i,l){l=(l||"").replace(/m/g,"")+"m";var c=/([:\-,[{]\s*(?:\s<<prop>>[ \t]+)?)(?:<<value>>)(?=[ \t]*(?:$|,|\]|\}|(?:[\r\n]\s*)?#))/.source.replace(/<<prop>>/g,function(){return r}).replace(/<<value>>/g,function(){return i});return RegExp(c,l)}e.languages.yaml={scalar:{pattern:RegExp(/([\-:]\s*(?:\s<<prop>>[ \t]+)?[|>])[ \t]*(?:((?:\r?\n|\r)[ \t]+)\S[^\r\n]*(?:\2[^\r\n]+)*)/.source.replace(/<<prop>>/g,function(){return r})),lookbehind:!0,alias:"string"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\-,[{\r\n?])[ \t]*(?:<<prop>>[ \t]+)?)<<key>>(?=\s*:\s)/.source.replace(/<<prop>>/g,function(){return r}).replace(/<<key>>/g,function(){return"(?:"+a+"|"+s+")"})),lookbehind:!0,greedy:!0,alias:"atrule"},directive:{pattern:/(^[ \t]*)%.+/m,lookbehind:!0,alias:"important"},datetime:{pattern:o(/\d{4}-\d\d?-\d\d?(?:[tT]|[ \t]+)\d\d?:\d{2}:\d{2}(?:\.\d*)?(?:[ \t]*(?:Z|[-+]\d\d?(?::\d{2})?))?|\d{4}-\d{2}-\d{2}|\d\d?:\d{2}(?::\d{2}(?:\.\d*)?)?/.source),lookbehind:!0,alias:"number"},boolean:{pattern:o(/false|true/.source,"i"),lookbehind:!0,alias:"important"},null:{pattern:o(/null|~/.source,"i"),lookbehind:!0,alias:"important"},string:{pattern:o(s),lookbehind:!0,greedy:!0},number:{pattern:o(/[+-]?(?:0x[\da-f]+|0o[0-7]+|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?|\.inf|\.nan)/.source,"i"),lookbehind:!0},tag:n,important:t,punctuation:/---|[:[\]{}\-,|>?]|\.\.\./},e.languages.yml=e.languages.yaml}(x),function(e){var t=/(?:\\.|[^\\\n\r]|(?:\n|\r\n?)(?![\r\n]))/.source;function n(c){return c=c.replace(/<inner>/g,function(){return t}),RegExp(/((?:^|[^\\])(?:\\{2})*)/.source+"(?:"+c+")")}var r=/(?:\\.|``(?:[^`\r\n]|`(?!`))+``|`[^`\r\n]+`|[^\\|\r\n`])+/.source,a=/\|?__(?:\|__)+\|?(?:(?:\n|\r\n?)|(?![\s\S]))/.source.replace(/__/g,function(){return r}),s=/\|?[ \t]*:?-{3,}:?[ \t]*(?:\|[ \t]*:?-{3,}:?[ \t]*)+\|?(?:\n|\r\n?)/.source,o=(e.languages.markdown=e.languages.extend("markup",{}),e.languages.insertBefore("markdown","prolog",{"front-matter-block":{pattern:/(^(?:\s*[\r\n])?)---(?!.)[\s\S]*?[\r\n]---(?!.)/,lookbehind:!0,greedy:!0,inside:{punctuation:/^---|---$/,"front-matter":{pattern:/\S+(?:\s+\S+)*/,alias:["yaml","language-yaml"],inside:e.languages.yaml}}},blockquote:{pattern:/^>(?:[\t ]*>)*/m,alias:"punctuation"},table:{pattern:RegExp("^"+a+s+"(?:"+a+")*","m"),inside:{"table-data-rows":{pattern:RegExp("^("+a+s+")(?:"+a+")*$"),lookbehind:!0,inside:{"table-data":{pattern:RegExp(r),inside:e.languages.markdown},punctuation:/\|/}},"table-line":{pattern:RegExp("^("+a+")"+s+"$"),lookbehind:!0,inside:{punctuation:/\||:?-{3,}:?/}},"table-header-row":{pattern:RegExp("^"+a+"$"),inside:{"table-header":{pattern:RegExp(r),alias:"important",inside:e.languages.markdown},punctuation:/\|/}}}},code:[{pattern:/((?:^|\n)[ \t]*\n|(?:^|\r\n?)[ \t]*\r\n?)(?: {4}|\t).+(?:(?:\n|\r\n?)(?: {4}|\t).+)*/,lookbehind:!0,alias:"keyword"},{pattern:/^```[\s\S]*?^```$/m,greedy:!0,inside:{"code-block":{pattern:/^(```.*(?:\n|\r\n?))[\s\S]+?(?=(?:\n|\r\n?)^```$)/m,lookbehind:!0},"code-language":{pattern:/^(```).+/,lookbehind:!0},punctuation:/```/}}],title:[{pattern:/\S.*(?:\n|\r\n?)(?:==+|--+)(?=[ \t]*$)/m,alias:"important",inside:{punctuation:/==+$|--+$/}},{pattern:/(^\s*)#.+/m,lookbehind:!0,alias:"important",inside:{punctuation:/^#+|#+$/}}],hr:{pattern:/(^\s*)([*-])(?:[\t ]*\2){2,}(?=\s*$)/m,lookbehind:!0,alias:"punctuation"},list:{pattern:/(^\s*)(?:[*+-]|\d+\.)(?=[\t ].)/m,lookbehind:!0,alias:"punctuation"},"url-reference":{pattern:/!?\[[^\]]+\]:[\t ]+(?:\S+|<(?:\\.|[^>\\])+>)(?:[\t ]+(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\)))?/,inside:{variable:{pattern:/^(!?\[)[^\]]+/,lookbehind:!0},string:/(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\))$/,punctuation:/^[\[\]!:]|[<>]/},alias:"url"},bold:{pattern:n(/\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\b|\*\*(?:(?!\*)<inner>|\*(?:(?!\*)<inner>)+\*)+\*\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^..)[\s\S]+(?=..$)/,lookbehind:!0,inside:{}},punctuation:/\*\*|__/}},italic:{pattern:n(/\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\b|\*(?:(?!\*)<inner>|\*\*(?:(?!\*)<inner>)+\*\*)+\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^.)[\s\S]+(?=.$)/,lookbehind:!0,inside:{}},punctuation:/[*_]/}},strike:{pattern:n(/(~~?)(?:(?!~)<inner>)+\2/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^~~?)[\s\S]+(?=\1$)/,lookbehind:!0,inside:{}},punctuation:/~~?/}},"code-snippet":{pattern:/(^|[^\\`])(?:``[^`\r\n]+(?:`[^`\r\n]+)*``(?!`)|`[^`\r\n]+`(?!`))/,lookbehind:!0,greedy:!0,alias:["code","keyword"]},url:{pattern:n(/!?\[(?:(?!\])<inner>)+\](?:\([^\s)]+(?:[\t ]+"(?:\\.|[^"\\])*")?\)|[ \t]?\[(?:(?!\])<inner>)+\])/.source),lookbehind:!0,greedy:!0,inside:{operator:/^!/,content:{pattern:/(^\[)[^\]]+(?=\])/,lookbehind:!0,inside:{}},variable:{pattern:/(^\][ \t]?\[)[^\]]+(?=\]$)/,lookbehind:!0},url:{pattern:/(^\]\()[^\s)]+/,lookbehind:!0},string:{pattern:/(^[ \t]+)"(?:\\.|[^"\\])*"(?=\)$)/,lookbehind:!0}}}}),["url","bold","italic","strike"].forEach(function(c){["url","bold","italic","strike","code-snippet"].forEach(function(u){c!==u&&(e.languages.markdown[c].inside.content.inside[u]=e.languages.markdown[u])})}),e.hooks.add("after-tokenize",function(c){c.language!=="markdown"&&c.language!=="md"||function u(d){if(d&&typeof d!="string")for(var g=0,v=d.length;g<v;g++){var f,m=d[g];m.type!=="code"?u(m.content):(f=m.content[1],m=m.content[3],f&&m&&f.type==="code-language"&&m.type==="code-block"&&typeof f.content=="string"&&(f=f.content.replace(/\b#/g,"sharp").replace(/\b\+\+/g,"pp"),f="language-"+(f=(/[a-z][\w-]*/i.exec(f)||[""])[0].toLowerCase()),m.alias?typeof m.alias=="string"?m.alias=[m.alias,f]:m.alias.push(f):m.alias=[f]))}}(c.tokens)}),e.hooks.add("wrap",function(c){if(c.type==="code-block"){for(var u="",d=0,g=c.classes.length;d<g;d++){var v=c.classes[d],v=/language-(.+)/.exec(v);if(v){u=v[1];break}}var f,m=e.languages[u];m?c.content=e.highlight(function(h){return h=h.replace(o,""),h=h.replace(/&(\w{1,8}|#x?[\da-f]{1,8});/gi,function(b,_){var w;return(_=_.toLowerCase())[0]==="#"?(w=_[1]==="x"?parseInt(_.slice(2),16):Number(_.slice(1)),l(w)):i[_]||b})}(c.content),m,u):u&&u!=="none"&&e.plugins.autoloader&&(f="md-"+new Date().valueOf()+"-"+Math.floor(1e16*Math.random()),c.attributes.id=f,e.plugins.autoloader.loadLanguages(u,function(){var h=document.getElementById(f);h&&(h.innerHTML=e.highlight(h.textContent,e.languages[u],u))}))}}),RegExp(e.languages.markup.tag.pattern.source,"gi")),i={amp:"&",lt:"<",gt:">",quot:'"'},l=String.fromCodePoint||String.fromCharCode;e.languages.md=e.languages.markdown}(x),x.languages.graphql={comment:/#.*/,description:{pattern:/(?:"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*")(?=\s*[a-z_])/i,greedy:!0,alias:"string",inside:{"language-markdown":{pattern:/(^"(?:"")?)(?!\1)[\s\S]+(?=\1$)/,lookbehind:!0,inside:x.languages.markdown}}},string:{pattern:/"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*"/,greedy:!0},number:/(?:\B-|\b)\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,boolean:/\b(?:false|true)\b/,variable:/\$[a-z_]\w*/i,directive:{pattern:/@[a-z_]\w*/i,alias:"function"},"attr-name":{pattern:/\b[a-z_]\w*(?=\s*(?:\((?:[^()"]|"(?:\\.|[^\\"\r\n])*")*\))?:)/i,greedy:!0},"atom-input":{pattern:/\b[A-Z]\w*Input\b/,alias:"class-name"},scalar:/\b(?:Boolean|Float|ID|Int|String)\b/,constant:/\b[A-Z][A-Z_\d]*\b/,"class-name":{pattern:/(\b(?:enum|implements|interface|on|scalar|type|union)\s+|&\s*|:\s*|\[)[A-Z_]\w*/,lookbehind:!0},fragment:{pattern:/(\bfragment\s+|\.{3}\s*(?!on\b))[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-mutation":{pattern:/(\bmutation\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-query":{pattern:/(\bquery\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},keyword:/\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\b/,operator:/[!=|&]|\.{3}/,"property-query":/\w+(?=\s*\()/,object:/\w+(?=\s*\{)/,punctuation:/[!(){}\[\]:=,]/,property:/\w+/},x.hooks.add("after-tokenize",function(e){if(e.language==="graphql")for(var t=e.tokens.filter(function(f){return typeof f!="string"&&f.type!=="comment"&&f.type!=="scalar"}),n=0;n<t.length;){var r=t[n++];if(r.type==="keyword"&&r.content==="mutation"){var a=[];if(d(["definition-mutation","punctuation"])&&u(1).content==="("){n+=2;var s=g(/^\($/,/^\)$/);if(s===-1)continue;for(;n<s;n++){var o=u(0);o.type==="variable"&&(v(o,"variable-input"),a.push(o.content))}n=s+1}if(d(["punctuation","property-query"])&&u(0).content==="{"&&(n++,v(u(0),"property-mutation"),0<a.length)){var i=g(/^\{$/,/^\}$/);if(i!==-1)for(var l=n;l<i;l++){var c=t[l];c.type==="variable"&&0<=a.indexOf(c.content)&&v(c,"variable-input")}}}}function u(f){return t[n+f]}function d(f,m){m=m||0;for(var h=0;h<f.length;h++){var b=u(h+m);if(!b||b.type!==f[h])return}return 1}function g(f,m){for(var h=1,b=n;b<t.length;b++){var _=t[b],w=_.content;if(_.type==="punctuation"&&typeof w=="string"){if(f.test(w))h++;else if(m.test(w)&&--h===0)return b}}return-1}function v(f,m){var h=f.alias;h?Array.isArray(h)||(f.alias=h=[h]):f.alias=h=[],h.push(m)}}),x.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},function(e){var t=e.languages.javascript["template-string"],n=t.pattern.source,r=t.inside.interpolation,a=r.inside["interpolation-punctuation"],s=r.pattern.source;function o(d,g){if(e.languages[d])return{pattern:RegExp("((?:"+g+")\\s*)"+n),lookbehind:!0,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},"embedded-code":{pattern:/[\s\S]+/,alias:d}}}}function i(d,g,v){return d={code:d,grammar:g,language:v},e.hooks.run("before-tokenize",d),d.tokens=e.tokenize(d.code,d.grammar),e.hooks.run("after-tokenize",d),d.tokens}function l(d,g,v){var h=e.tokenize(d,{interpolation:{pattern:RegExp(s),lookbehind:!0}}),f=0,m={},h=i(h.map(function(_){if(typeof _=="string")return _;for(var w,A,_=_.content;d.indexOf((A=f++,w="___"+v.toUpperCase()+"_"+A+"___"))!==-1;);return m[w]=_,w}).join(""),g,v),b=Object.keys(m);return f=0,function _(w){for(var A=0;A<w.length;A++){if(f>=b.length)return;var O,N,F,D,I,$,G,J=w[A];typeof J=="string"||typeof J.content=="string"?(O=b[f],(G=($=typeof J=="string"?J:J.content).indexOf(O))!==-1&&(++f,N=$.substring(0,G),I=m[O],F=void 0,(D={})["interpolation-punctuation"]=a,(D=e.tokenize(I,D)).length===3&&((F=[1,1]).push.apply(F,i(D[1],e.languages.javascript,"javascript")),D.splice.apply(D,F)),F=new e.Token("interpolation",D,r.alias,I),D=$.substring(G+O.length),I=[],N&&I.push(N),I.push(F),D&&(_($=[D]),I.push.apply(I,$)),typeof J=="string"?(w.splice.apply(w,[A,1].concat(I)),A+=I.length-1):J.content=I)):(G=J.content,Array.isArray(G)?_(G):_([G]))}}(h),new e.Token(v,h,"language-"+v,d)}e.languages.javascript["template-string"]=[o("css",/\b(?:styled(?:\([^)]*\))?(?:\s*\.\s*\w+(?:\([^)]*\))*)*|css(?:\s*\.\s*(?:global|resolve))?|createGlobalStyle|keyframes)/.source),o("html",/\bhtml|\.\s*(?:inner|outer)HTML\s*\+?=/.source),o("svg",/\bsvg/.source),o("markdown",/\b(?:markdown|md)/.source),o("graphql",/\b(?:gql|graphql(?:\s*\.\s*experimental)?)/.source),o("sql",/\bsql/.source),t].filter(Boolean);var c={javascript:!0,js:!0,typescript:!0,ts:!0,jsx:!0,tsx:!0};function u(d){return typeof d=="string"?d:Array.isArray(d)?d.map(u).join(""):u(d.content)}e.hooks.add("after-tokenize",function(d){d.language in c&&function g(v){for(var f=0,m=v.length;f<m;f++){var h,b,_,w=v[f];typeof w!="string"&&(h=w.content,Array.isArray(h)?w.type==="template-string"?(w=h[1],h.length===3&&typeof w!="string"&&w.type==="embedded-code"&&(b=u(w),w=w.alias,w=Array.isArray(w)?w[0]:w,_=e.languages[w])&&(h[1]=l(b,_,w))):g(h):typeof h!="string"&&g([h]))}}(d.tokens)})}(x),function(e){e.languages.typescript=e.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),e.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete e.languages.typescript.parameter,delete e.languages.typescript["literal-property"];var t=e.languages.extend("typescript",{});delete t["class-name"],e.languages.typescript["class-name"].inside=t,e.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:t}}}}),e.languages.ts=e.languages.typescript}(x),function(e){var t=e.languages.javascript,n=/\{(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})+\}/.source,r="(@(?:arg|argument|param|property)\\s+(?:"+n+"\\s+)?)";e.languages.jsdoc=e.languages.extend("javadoclike",{parameter:{pattern:RegExp(r+/(?:(?!\s)[$\w\xA0-\uFFFF.])+(?=\s|$)/.source),lookbehind:!0,inside:{punctuation:/\./}}}),e.languages.insertBefore("jsdoc","keyword",{"optional-parameter":{pattern:RegExp(r+/\[(?:(?!\s)[$\w\xA0-\uFFFF.])+(?:=[^[\]]+)?\](?=\s|$)/.source),lookbehind:!0,inside:{parameter:{pattern:/(^\[)[$\w\xA0-\uFFFF\.]+/,lookbehind:!0,inside:{punctuation:/\./}},code:{pattern:/(=)[\s\S]*(?=\]$)/,lookbehind:!0,inside:t,alias:"language-javascript"},punctuation:/[=[\]]/}},"class-name":[{pattern:RegExp(/(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\s+(?:<TYPE>\s+)?)[A-Z]\w*(?:\.[A-Z]\w*)*/.source.replace(/<TYPE>/g,function(){return n})),lookbehind:!0,inside:{punctuation:/\./}},{pattern:RegExp("(@[a-z]+\\s+)"+n),lookbehind:!0,inside:{string:t.string,number:t.number,boolean:t.boolean,keyword:e.languages.typescript.keyword,operator:/=>|\.\.\.|[&|?:*]/,punctuation:/[.,;=<>{}()[\]]/}}],example:{pattern:/(@example\s+(?!\s))(?:[^@\s]|\s+(?!\s))+?(?=\s*(?:\*\s*)?(?:@\w|\*\/))/,lookbehind:!0,inside:{code:{pattern:/^([\t ]*(?:\*\s*)?)\S.*$/m,lookbehind:!0,inside:t,alias:"language-javascript"}}}}),e.languages.javadoclike.addSupport("javascript",e.languages.jsdoc)}(x),function(e){e.languages.flow=e.languages.extend("javascript",{}),e.languages.insertBefore("flow","keyword",{type:[{pattern:/\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|[Ss]ymbol|any|mixed|null|void)\b/,alias:"class-name"}]}),e.languages.flow["function-variable"].pattern=/(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=\s*(?:function\b|(?:\([^()]*\)(?:\s*:\s*\w+)?|(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/i,delete e.languages.flow.parameter,e.languages.insertBefore("flow","operator",{"flow-punctuation":{pattern:/\{\||\|\}/,alias:"punctuation"}}),Array.isArray(e.languages.flow.keyword)||(e.languages.flow.keyword=[e.languages.flow.keyword]),e.languages.flow.keyword.unshift({pattern:/(^|[^$]\b)(?:Class|declare|opaque|type)\b(?!\$)/,lookbehind:!0},{pattern:/(^|[^$]\B)\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\b(?!\$)/,lookbehind:!0})}(x),x.languages.n4js=x.languages.extend("javascript",{keyword:/\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\b/}),x.languages.insertBefore("n4js","constant",{annotation:{pattern:/@+\w+/,alias:"operator"}}),x.languages.n4jsd=x.languages.n4js,function(e){function t(o,i){return RegExp(o.replace(/<ID>/g,function(){return/(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/.source}),i)}e.languages.insertBefore("javascript","function-variable",{"method-variable":{pattern:RegExp("(\\.\\s*)"+e.languages.javascript["function-variable"].pattern.source),lookbehind:!0,alias:["function-variable","method","function","property-access"]}}),e.languages.insertBefore("javascript","function",{method:{pattern:RegExp("(\\.\\s*)"+e.languages.javascript.function.source),lookbehind:!0,alias:["function","property-access"]}}),e.languages.insertBefore("javascript","constant",{"known-class-name":[{pattern:/\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\b/,alias:"class-name"},{pattern:/\b(?:[A-Z]\w*)Error\b/,alias:"class-name"}]}),e.languages.insertBefore("javascript","keyword",{imports:{pattern:t(/(\bimport\b\s*)(?:<ID>(?:\s*,\s*(?:\*\s*as\s+<ID>|\{[^{}]*\}))?|\*\s*as\s+<ID>|\{[^{}]*\})(?=\s*\bfrom\b)/.source),lookbehind:!0,inside:e.languages.javascript},exports:{pattern:t(/(\bexport\b\s*)(?:\*(?:\s*as\s+<ID>)?(?=\s*\bfrom\b)|\{[^{}]*\})/.source),lookbehind:!0,inside:e.languages.javascript}}),e.languages.javascript.keyword.unshift({pattern:/\b(?:as|default|export|from|import)\b/,alias:"module"},{pattern:/\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\b/,alias:"control-flow"},{pattern:/\bnull\b/,alias:["null","nil"]},{pattern:/\bundefined\b/,alias:"nil"}),e.languages.insertBefore("javascript","operator",{spread:{pattern:/\.{3}/,alias:"operator"},arrow:{pattern:/=>/,alias:"operator"}}),e.languages.insertBefore("javascript","punctuation",{"property-access":{pattern:t(/(\.\s*)#?<ID>/.source),lookbehind:!0},"maybe-class-name":{pattern:/(^|[^$\w\xA0-\uFFFF])[A-Z][$\w\xA0-\uFFFF]+/,lookbehind:!0},dom:{pattern:/\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\b/,alias:"variable"},console:{pattern:/\bconsole(?=\s*\.)/,alias:"class-name"}});for(var n=["function","function-variable","method","method-variable","property-access"],r=0;r<n.length;r++){var s=n[r],a=e.languages.javascript[s],s=(a=e.util.type(a)==="RegExp"?e.languages.javascript[s]={pattern:a}:a).inside||{};(a.inside=s)["maybe-class-name"]=/^[A-Z][\s\S]*/}}(x),function(e){var t=e.util.clone(e.languages.javascript),n=/(?:\s|\/\/.*(?!.)|\/\*(?:[^*]|\*(?!\/))\*\/)/.source,r=/(?:\{(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])*\})/.source,a=/(?:\{<S>*\.{3}(?:[^{}]|<BRACES>)*\})/.source;function s(l,c){return l=l.replace(/<S>/g,function(){return n}).replace(/<BRACES>/g,function(){return r}).replace(/<SPREAD>/g,function(){return a}),RegExp(l,c)}a=s(a).source,e.languages.jsx=e.languages.extend("markup",t),e.languages.jsx.tag.pattern=s(/<\/?(?:[\w.:-]+(?:<S>+(?:[\w.:$-]+(?:=(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s{'"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\/?)?>/.source),e.languages.jsx.tag.inside.tag.pattern=/^<\/?[^\s>\/]*/,e.languages.jsx.tag.inside["attr-value"].pattern=/=(?!\{)(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s'">]+)/,e.languages.jsx.tag.inside.tag.inside["class-name"]=/^[A-Z]\w*(?:\.[A-Z]\w*)*$/,e.languages.jsx.tag.inside.comment=t.comment,e.languages.insertBefore("inside","attr-name",{spread:{pattern:s(/<SPREAD>/.source),inside:e.languages.jsx}},e.languages.jsx.tag),e.languages.insertBefore("inside","special-attr",{script:{pattern:s(/=<BRACES>/.source),alias:"language-javascript",inside:{"script-punctuation":{pattern:/^=(?=\{)/,alias:"punctuation"},rest:e.languages.jsx}}},e.languages.jsx.tag);function o(l){for(var c=[],u=0;u<l.length;u++){var d=l[u],g=!1;typeof d!="string"&&(d.type==="tag"&&d.content[0]&&d.content[0].type==="tag"?d.content[0].content[0].content==="</"?0<c.length&&c[c.length-1].tagName===i(d.content[0].content[1])&&c.pop():d.content[d.content.length-1].content!=="/>"&&c.push({tagName:i(d.content[0].content[1]),openedBraces:0}):0<c.length&&d.type==="punctuation"&&d.content==="{"?c[c.length-1].openedBraces++:0<c.length&&0<c[c.length-1].openedBraces&&d.type==="punctuation"&&d.content==="}"?c[c.length-1].openedBraces--:g=!0),(g||typeof d=="string")&&0<c.length&&c[c.length-1].openedBraces===0&&(g=i(d),u<l.length-1&&(typeof l[u+1]=="string"||l[u+1].type==="plain-text")&&(g+=i(l[u+1]),l.splice(u+1,1)),0<u&&(typeof l[u-1]=="string"||l[u-1].type==="plain-text")&&(g=i(l[u-1])+g,l.splice(u-1,1),u--),l[u]=new e.Token("plain-text",g,null,g)),d.content&&typeof d.content!="string"&&o(d.content)}}var i=function(l){return l?typeof l=="string"?l:typeof l.content=="string"?l.content:l.content.map(i).join(""):""};e.hooks.add("after-tokenize",function(l){l.language!=="jsx"&&l.language!=="tsx"||o(l.tokens)})}(x),function(e){var t=e.util.clone(e.languages.typescript),t=(e.languages.tsx=e.languages.extend("jsx",t),delete e.languages.tsx.parameter,delete e.languages.tsx["literal-property"],e.languages.tsx.tag);t.pattern=RegExp(/(^|[^\w$]|(?=<\/))/.source+"(?:"+t.pattern.source+")",t.pattern.flags),t.lookbehind=!0}(x),x.languages.swift={comment:{pattern:/(^|[^\\:])(?:\/\/.*|\/\*(?:[^/*]|\/(?!\*)|\*(?!\/)|\/\*(?:[^*]|\*(?!\/))*\*\/)*\*\/)/,lookbehind:!0,greedy:!0},"string-literal":[{pattern:RegExp(/(^|[^"#])/.source+"(?:"+/"(?:\\(?:\((?:[^()]|\([^()]*\))*\)|\r\n|[^(])|[^\\\r\n"])*"/.source+"|"+/"""(?:\\(?:\((?:[^()]|\([^()]*\))*\)|[^(])|[^\\"]|"(?!""))*"""/.source+")"+/(?!["#])/.source),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\\($/,alias:"punctuation"},punctuation:/\\(?=[\r\n])/,string:/[\s\S]+/}},{pattern:RegExp(/(^|[^"#])(#+)/.source+"(?:"+/"(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|\r\n|[^#])|[^\\\r\n])*?"/.source+"|"+/"""(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|[^#])|[^\\])*?"""/.source+")\\2"),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\#+\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\#+\($/,alias:"punctuation"},string:/[\s\S]+/}}],directive:{pattern:RegExp(/#/.source+"(?:"+/(?:elseif|if)\b/.source+"(?:[ 	]*"+/(?:![ \t]*)?(?:\b\w+\b(?:[ \t]*\((?:[^()]|\([^()]*\))*\))?|\((?:[^()]|\([^()]*\))*\))(?:[ \t]*(?:&&|\|\|))?/.source+")+|"+/(?:else|endif)\b/.source+")"),alias:"property",inside:{"directive-name":/^#\w+/,boolean:/\b(?:false|true)\b/,number:/\b\d+(?:\.\d+)*\b/,operator:/!|&&|\|\||[<>]=?/,punctuation:/[(),]/}},literal:{pattern:/#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\b/,alias:"constant"},"other-directive":{pattern:/#\w+\b/,alias:"property"},attribute:{pattern:/@\w+/,alias:"atrule"},"function-definition":{pattern:/(\bfunc\s+)\w+/,lookbehind:!0,alias:"function"},label:{pattern:/\b(break|continue)\s+\w+|\b[a-zA-Z_]\w*(?=\s*:\s*(?:for|repeat|while)\b)/,lookbehind:!0,alias:"important"},keyword:/\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\b/,boolean:/\b(?:false|true)\b/,nil:{pattern:/\bnil\b/,alias:"constant"},"short-argument":/\$\d+\b/,omit:{pattern:/\b_\b/,alias:"keyword"},number:/\b(?:[\d_]+(?:\.[\de_]+)?|0x[a-f0-9_]+(?:\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\b/i,"class-name":/\b[A-Z](?:[A-Z_\d]*[a-z]\w*)?\b/,function:/\b[a-z_]\w*(?=\s*\()/i,constant:/\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\b/,operator:/[-+*/%=!<>&|^~?]+|\.[.\-+*/%=!<>&|^~?]+/,punctuation:/[{}[\]();,.:\\]/},x.languages.swift["string-literal"].forEach(function(e){e.inside.interpolation.inside=x.languages.swift}),function(e){e.languages.kotlin=e.languages.extend("clike",{keyword:{pattern:/(^|[^.])\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\b/,lookbehind:!0},function:[{pattern:/(?:`[^\r\n`]+`|\b\w+)(?=\s*\()/,greedy:!0},{pattern:/(\.)(?:`[^\r\n`]+`|\w+)(?=\s*\{)/,lookbehind:!0,greedy:!0}],number:/\b(?:0[xX][\da-fA-F]+(?:_[\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\d+(?:_\d+)*(?:\.\d+(?:_\d+)*)?(?:[eE][+-]?\d+(?:_\d+)*)?[fFL]?)\b/,operator:/\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\/*%<>]=?|[?:]:?|\.\.|&&|\|\||\b(?:and|inv|or|shl|shr|ushr|xor)\b/}),delete e.languages.kotlin["class-name"];var t={"interpolation-punctuation":{pattern:/^\$\{?|\}$/,alias:"punctuation"},expression:{pattern:/[\s\S]+/,inside:e.languages.kotlin}};e.languages.insertBefore("kotlin","string",{"string-literal":[{pattern:/"""(?:[^$]|\$(?:(?!\{)|\{[^{}]*\}))*?"""/,alias:"multiline",inside:{interpolation:{pattern:/\$(?:[a-z_]\w*|\{[^{}]*\})/i,inside:t},string:/[\s\S]+/}},{pattern:/"(?:[^"\\\r\n$]|\\.|\$(?:(?!\{)|\{[^{}]*\}))*"/,alias:"singleline",inside:{interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$(?:[a-z_]\w*|\{[^{}]*\})/i,lookbehind:!0,inside:t},string:/[\s\S]+/}}],char:{pattern:/'(?:[^'\\\r\n]|\\(?:.|u[a-fA-F0-9]{0,4}))'/,greedy:!0}}),delete e.languages.kotlin.string,e.languages.insertBefore("kotlin","keyword",{annotation:{pattern:/\B@(?:\w+:)?(?:[A-Z]\w*|\[[^\]]+\])/,alias:"builtin"}}),e.languages.insertBefore("kotlin","function",{label:{pattern:/\b\w+@|@\w+\b/,alias:"symbol"}}),e.languages.kt=e.languages.kotlin,e.languages.kts=e.languages.kotlin}(x),x.languages.c=x.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/}),x.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}}),x.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},x.languages.c.string],char:x.languages.c.char,comment:x.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:x.languages.c}}}}),x.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/}),delete x.languages.c.boolean,x.languages.objectivec=x.languages.extend("c",{string:{pattern:/@?"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},keyword:/\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\b/,operator:/-[->]?|\+\+?|!=?|<<?=?|>>?=?|==?|&&?|\|\|?|[~^%?*\/@]/}),delete x.languages.objectivec["class-name"],x.languages.objc=x.languages.objectivec,x.languages.reason=x.languages.extend("clike",{string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^\\\r\n"])*"/,greedy:!0},"class-name":/\b[A-Z]\w*/,keyword:/\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\b/,operator:/\.{3}|:[:=]|\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\-*\/]\.?|\b(?:asr|land|lor|lsl|lsr|lxor|mod)\b/}),x.languages.insertBefore("reason","class-name",{char:{pattern:/'(?:\\x[\da-f]{2}|\\o[0-3][0-7][0-7]|\\\d{3}|\\.|[^'\\\r\n])'/,greedy:!0},constructor:/\b[A-Z]\w*\b(?!\s*\.)/,label:{pattern:/\b[a-z]\w*(?=::)/,alias:"symbol"}}),delete x.languages.reason.function,function(e){for(var t=/\/\*(?:[^*/]|\*(?!\/)|\/(?!\*)|<self>)*\*\//.source,n=0;n<2;n++)t=t.replace(/<self>/g,function(){return t});t=t.replace(/<self>/g,function(){return/[^\s\S]/.source}),e.languages.rust={comment:[{pattern:RegExp(/(^|[^\\])/.source+t),lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/b?"(?:\\[\s\S]|[^\\"])*"|b?r(#*)"(?:[^"]|"(?!\1))*"\1/,greedy:!0},char:{pattern:/b?'(?:\\(?:x[0-7][\da-fA-F]|u\{(?:[\da-fA-F]_*){1,6}\}|.)|[^\\\r\n\t'])'/,greedy:!0},attribute:{pattern:/#!?\[(?:[^\[\]"]|"(?:\\[\s\S]|[^\\"])*")*\]/,greedy:!0,alias:"attr-name",inside:{string:null}},"closure-params":{pattern:/([=(,:]\s*|\bmove\s*)\|[^|]*\||\|[^|]*\|(?=\s*(?:\{|->))/,lookbehind:!0,greedy:!0,inside:{"closure-punctuation":{pattern:/^\||\|$/,alias:"punctuation"},rest:null}},"lifetime-annotation":{pattern:/'\w+/,alias:"symbol"},"fragment-specifier":{pattern:/(\$\w+:)[a-z]+/,lookbehind:!0,alias:"punctuation"},variable:/\$\w+/,"function-definition":{pattern:/(\bfn\s+)\w+/,lookbehind:!0,alias:"function"},"type-definition":{pattern:/(\b(?:enum|struct|trait|type|union)\s+)\w+/,lookbehind:!0,alias:"class-name"},"module-declaration":[{pattern:/(\b(?:crate|mod)\s+)[a-z][a-z_\d]*/,lookbehind:!0,alias:"namespace"},{pattern:/(\b(?:crate|self|super)\s*)::\s*[a-z][a-z_\d]*\b(?:\s*::(?:\s*[a-z][a-z_\d]*\s*::)*)?/,lookbehind:!0,alias:"namespace",inside:{punctuation:/::/}}],keyword:[/\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\b/,/\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\b/],function:/\b[a-z_]\w*(?=\s*(?:::\s*<|\())/,macro:{pattern:/\b\w+!/,alias:"property"},constant:/\b[A-Z_][A-Z_\d]+\b/,"class-name":/\b[A-Z]\w*\b/,namespace:{pattern:/(?:\b[a-z][a-z_\d]*\s*::\s*)*\b[a-z][a-z_\d]*\s*::(?!\s*<)/,inside:{punctuation:/::/}},number:/\b(?:0x[\dA-Fa-f](?:_?[\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\d(?:_?\d)*)?\.)?\d(?:_?\d)*(?:[Ee][+-]?\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\b/,boolean:/\b(?:false|true)\b/,punctuation:/->|\.\.=|\.{1,3}|::|[{}[\];(),:]/,operator:/[-+*\/%!^]=?|=[=>]?|&[&=]?|\|[|=]?|<<?=?|>>?=?|[@?]/},e.languages.rust["closure-params"].inside.rest=e.languages.rust,e.languages.rust.attribute.inside.string=e.languages.rust.string}(x),x.languages.go=x.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"|`[^`]*`/,lookbehind:!0,greedy:!0},keyword:/\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/,boolean:/\b(?:_|false|iota|nil|true)\b/,number:[/\b0(?:b[01_]+|o[0-7_]+)i?\b/i,/\b0x(?:[a-f\d_]+(?:\.[a-f\d_]*)?|\.[a-f\d_]+)(?:p[+-]?\d+(?:_\d+)*)?i?(?!\w)/i,/(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?[\d_]+)?i?(?!\w)/i],operator:/[*\/%^!=]=?|\+[=+]?|-[=-]?|\|[=|]?|&(?:=|&|\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\.\.\./,builtin:/\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\b/}),x.languages.insertBefore("go","string",{char:{pattern:/'(?:\\.|[^'\\\r\n]){0,10}'/,greedy:!0}}),delete x.languages.go["class-name"],function(e){var t=/\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\b/,n=/\b(?!<keyword>)\w+(?:\s*\.\s*\w+)*\b/.source.replace(/<keyword>/g,function(){return t.source});e.languages.cpp=e.languages.extend("c",{"class-name":[{pattern:RegExp(/(\b(?:class|concept|enum|struct|typename)\s+)(?!<keyword>)\w+/.source.replace(/<keyword>/g,function(){return t.source})),lookbehind:!0},/\b[A-Z]\w*(?=\s*::\s*\w+\s*\()/,/\b[A-Z_]\w*(?=\s*::\s*~\w+\s*\()/i,/\b\w+(?=\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\s*::\s*\w+\s*\()/],keyword:t,number:{pattern:/(?:\b0b[01']+|\b0x(?:[\da-f']+(?:\.[\da-f']*)?|\.[\da-f']+)(?:p[+-]?[\d']+)?|(?:\b[\d']+(?:\.[\d']*)?|\B\.[\d']+)(?:e[+-]?[\d']+)?)[ful]{0,4}/i,greedy:!0},operator:/>>=?|<<=?|->|--|\+\+|&&|\|\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\b/,boolean:/\b(?:false|true)\b/}),e.languages.insertBefore("cpp","string",{module:{pattern:RegExp(/(\b(?:import|module)\s+)/.source+"(?:"+/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|<[^<>\r\n]*>/.source+"|"+/<mod-name>(?:\s*:\s*<mod-name>)?|:\s*<mod-name>/.source.replace(/<mod-name>/g,function(){return n})+")"),lookbehind:!0,greedy:!0,inside:{string:/^[<"][\s\S]+/,operator:/:/,punctuation:/\./}},"raw-string":{pattern:/R"([^()\\ ]{0,16})\([\s\S]*?\)\1"/,alias:"string",greedy:!0}}),e.languages.insertBefore("cpp","keyword",{"generic-function":{pattern:/\b(?!operator\b)[a-z_]\w*\s*<(?:[^<>]|<[^<>]*>)*>(?=\s*\()/i,inside:{function:/^\w+/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e.languages.cpp}}}}),e.languages.insertBefore("cpp","operator",{"double-colon":{pattern:/::/,alias:"punctuation"}}),e.languages.insertBefore("cpp","class-name",{"base-clause":{pattern:/(\b(?:class|struct)\s+\w+\s*:\s*)[^;{}"'\s]+(?:\s+[^;{}"'\s]+)*(?=\s*[;{])/,lookbehind:!0,greedy:!0,inside:e.languages.extend("cpp",{})}}),e.languages.insertBefore("inside","double-colon",{"class-name":/\b[a-z_]\w*\b(?!\s*::)/i},e.languages.cpp["base-clause"])}(x),x.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/},x.languages.python["string-interpolation"].inside.interpolation.inside.rest=x.languages.python,x.languages.py=x.languages.python,x.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},x.languages.webmanifest=x.languages.json;var zs={};Kc(zs,{dracula:()=>eu,duotoneDark:()=>nu,duotoneLight:()=>au,github:()=>ou,gruvboxMaterialDark:()=>Mu,gruvboxMaterialLight:()=>Fu,jettwaveDark:()=>Cu,jettwaveLight:()=>Iu,nightOwl:()=>lu,nightOwlLight:()=>uu,oceanicNext:()=>pu,okaidia:()=>mu,oneDark:()=>Nu,oneLight:()=>Ou,palenight:()=>hu,shadesOfPurple:()=>vu,synthwave84:()=>_u,ultramin:()=>xu,vsDark:()=>Zs,vsLight:()=>Eu});var Qc={plain:{color:"#F8F8F2",backgroundColor:"#282A36"},styles:[{types:["prolog","constant","builtin"],style:{color:"rgb(189, 147, 249)"}},{types:["inserted","function"],style:{color:"rgb(80, 250, 123)"}},{types:["deleted"],style:{color:"rgb(255, 85, 85)"}},{types:["changed"],style:{color:"rgb(255, 184, 108)"}},{types:["punctuation","symbol"],style:{color:"rgb(248, 248, 242)"}},{types:["string","char","tag","selector"],style:{color:"rgb(255, 121, 198)"}},{types:["keyword","variable"],style:{color:"rgb(189, 147, 249)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(98, 114, 164)"}},{types:["attr-name"],style:{color:"rgb(241, 250, 140)"}}]},eu=Qc,tu={plain:{backgroundColor:"#2a2734",color:"#9a86fd"},styles:[{types:["comment","prolog","doctype","cdata","punctuation"],style:{color:"#6c6783"}},{types:["namespace"],style:{opacity:.7}},{types:["tag","operator","number"],style:{color:"#e09142"}},{types:["property","function"],style:{color:"#9a86fd"}},{types:["tag-id","selector","atrule-id"],style:{color:"#eeebff"}},{types:["attr-name"],style:{color:"#c4b9fe"}},{types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","atrule","placeholder","variable"],style:{color:"#ffcc99"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"#c4b9fe"}}]},nu=tu,ru={plain:{backgroundColor:"#faf8f5",color:"#728fcb"},styles:[{types:["comment","prolog","doctype","cdata","punctuation"],style:{color:"#b6ad9a"}},{types:["namespace"],style:{opacity:.7}},{types:["tag","operator","number"],style:{color:"#063289"}},{types:["property","function"],style:{color:"#b29762"}},{types:["tag-id","selector","atrule-id"],style:{color:"#2d2006"}},{types:["attr-name"],style:{color:"#896724"}},{types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","atrule"],style:{color:"#728fcb"}},{types:["placeholder","variable"],style:{color:"#93abdc"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"#896724"}}]},au=ru,su={plain:{color:"#393A34",backgroundColor:"#f6f8fa"},styles:[{types:["comment","prolog","doctype","cdata"],style:{color:"#999988",fontStyle:"italic"}},{types:["namespace"],style:{opacity:.7}},{types:["string","attr-value"],style:{color:"#e3116c"}},{types:["punctuation","operator"],style:{color:"#393A34"}},{types:["entity","url","symbol","number","boolean","variable","constant","property","regex","inserted"],style:{color:"#36acaa"}},{types:["atrule","keyword","attr-name","selector"],style:{color:"#00a4db"}},{types:["function","deleted","tag"],style:{color:"#d73a49"}},{types:["function-variable"],style:{color:"#6f42c1"}},{types:["tag","selector","keyword"],style:{color:"#00009f"}}]},ou=su,iu={plain:{color:"#d6deeb",backgroundColor:"#011627"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)",fontStyle:"italic"}},{types:["inserted","attr-name"],style:{color:"rgb(173, 219, 103)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(99, 119, 119)",fontStyle:"italic"}},{types:["string","url"],style:{color:"rgb(173, 219, 103)"}},{types:["variable"],style:{color:"rgb(214, 222, 235)"}},{types:["number"],style:{color:"rgb(247, 140, 108)"}},{types:["builtin","char","constant","function"],style:{color:"rgb(130, 170, 255)"}},{types:["punctuation"],style:{color:"rgb(199, 146, 234)"}},{types:["selector","doctype"],style:{color:"rgb(199, 146, 234)",fontStyle:"italic"}},{types:["class-name"],style:{color:"rgb(255, 203, 139)"}},{types:["tag","operator","keyword"],style:{color:"rgb(127, 219, 202)"}},{types:["boolean"],style:{color:"rgb(255, 88, 116)"}},{types:["property"],style:{color:"rgb(128, 203, 196)"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)"}}]},lu=iu,cu={plain:{color:"#403f53",backgroundColor:"#FBFBFB"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)",fontStyle:"italic"}},{types:["inserted","attr-name"],style:{color:"rgb(72, 118, 214)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(152, 159, 177)",fontStyle:"italic"}},{types:["string","builtin","char","constant","url"],style:{color:"rgb(72, 118, 214)"}},{types:["variable"],style:{color:"rgb(201, 103, 101)"}},{types:["number"],style:{color:"rgb(170, 9, 130)"}},{types:["punctuation"],style:{color:"rgb(153, 76, 195)"}},{types:["function","selector","doctype"],style:{color:"rgb(153, 76, 195)",fontStyle:"italic"}},{types:["class-name"],style:{color:"rgb(17, 17, 17)"}},{types:["tag"],style:{color:"rgb(153, 76, 195)"}},{types:["operator","property","keyword","namespace"],style:{color:"rgb(12, 150, 155)"}},{types:["boolean"],style:{color:"rgb(188, 84, 84)"}}]},uu=cu,Ce={char:"#D8DEE9",comment:"#999999",keyword:"#c5a5c5",primitive:"#5a9bcf",string:"#8dc891",variable:"#d7deea",boolean:"#ff8b50",tag:"#fc929e",function:"#79b6f2",className:"#FAC863"},du={plain:{backgroundColor:"#282c34",color:"#ffffff"},styles:[{types:["attr-name"],style:{color:Ce.keyword}},{types:["attr-value"],style:{color:Ce.string}},{types:["comment","block-comment","prolog","doctype","cdata","shebang"],style:{color:Ce.comment}},{types:["property","number","function-name","constant","symbol","deleted"],style:{color:Ce.primitive}},{types:["boolean"],style:{color:Ce.boolean}},{types:["tag"],style:{color:Ce.tag}},{types:["string"],style:{color:Ce.string}},{types:["punctuation"],style:{color:Ce.string}},{types:["selector","char","builtin","inserted"],style:{color:Ce.char}},{types:["function"],style:{color:Ce.function}},{types:["operator","entity","url","variable"],style:{color:Ce.variable}},{types:["keyword"],style:{color:Ce.keyword}},{types:["atrule","class-name"],style:{color:Ce.className}},{types:["important"],style:{fontWeight:"400"}},{types:["bold"],style:{fontWeight:"bold"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["namespace"],style:{opacity:.7}}]},pu=du,fu={plain:{color:"#f8f8f2",backgroundColor:"#272822"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"#f92672",fontStyle:"italic"}},{types:["inserted"],style:{color:"rgb(173, 219, 103)",fontStyle:"italic"}},{types:["comment"],style:{color:"#8292a2",fontStyle:"italic"}},{types:["string","url"],style:{color:"#a6e22e"}},{types:["variable"],style:{color:"#f8f8f2"}},{types:["number"],style:{color:"#ae81ff"}},{types:["builtin","char","constant","function","class-name"],style:{color:"#e6db74"}},{types:["punctuation"],style:{color:"#f8f8f2"}},{types:["selector","doctype"],style:{color:"#a6e22e",fontStyle:"italic"}},{types:["tag","operator","keyword"],style:{color:"#66d9ef"}},{types:["boolean"],style:{color:"#ae81ff"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)",opacity:.7}},{types:["tag","property"],style:{color:"#f92672"}},{types:["attr-name"],style:{color:"#a6e22e !important"}},{types:["doctype"],style:{color:"#8292a2"}},{types:["rule"],style:{color:"#e6db74"}}]},mu=fu,gu={plain:{color:"#bfc7d5",backgroundColor:"#292d3e"},styles:[{types:["comment"],style:{color:"rgb(105, 112, 152)",fontStyle:"italic"}},{types:["string","inserted"],style:{color:"rgb(195, 232, 141)"}},{types:["number"],style:{color:"rgb(247, 140, 108)"}},{types:["builtin","char","constant","function"],style:{color:"rgb(130, 170, 255)"}},{types:["punctuation","selector"],style:{color:"rgb(199, 146, 234)"}},{types:["variable"],style:{color:"rgb(191, 199, 213)"}},{types:["class-name","attr-name"],style:{color:"rgb(255, 203, 107)"}},{types:["tag","deleted"],style:{color:"rgb(255, 85, 114)"}},{types:["operator"],style:{color:"rgb(137, 221, 255)"}},{types:["boolean"],style:{color:"rgb(255, 88, 116)"}},{types:["keyword"],style:{fontStyle:"italic"}},{types:["doctype"],style:{color:"rgb(199, 146, 234)",fontStyle:"italic"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)"}},{types:["url"],style:{color:"rgb(221, 221, 221)"}}]},hu=gu,yu={plain:{color:"#9EFEFF",backgroundColor:"#2D2A55"},styles:[{types:["changed"],style:{color:"rgb(255, 238, 128)"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)"}},{types:["inserted"],style:{color:"rgb(173, 219, 103)"}},{types:["comment"],style:{color:"rgb(179, 98, 255)",fontStyle:"italic"}},{types:["punctuation"],style:{color:"rgb(255, 255, 255)"}},{types:["constant"],style:{color:"rgb(255, 98, 140)"}},{types:["string","url"],style:{color:"rgb(165, 255, 144)"}},{types:["variable"],style:{color:"rgb(255, 238, 128)"}},{types:["number","boolean"],style:{color:"rgb(255, 98, 140)"}},{types:["attr-name"],style:{color:"rgb(255, 180, 84)"}},{types:["keyword","operator","property","namespace","tag","selector","doctype"],style:{color:"rgb(255, 157, 0)"}},{types:["builtin","char","constant","function","class-name"],style:{color:"rgb(250, 208, 0)"}}]},vu=yu,bu={plain:{backgroundColor:"linear-gradient(to bottom, #2a2139 75%, #34294f)",backgroundImage:"#34294f",color:"#f92aad",textShadow:"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"},styles:[{types:["comment","block-comment","prolog","doctype","cdata"],style:{color:"#495495",fontStyle:"italic"}},{types:["punctuation"],style:{color:"#ccc"}},{types:["tag","attr-name","namespace","number","unit","hexcode","deleted"],style:{color:"#e2777a"}},{types:["property","selector"],style:{color:"#72f1b8",textShadow:"0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475"}},{types:["function-name"],style:{color:"#6196cc"}},{types:["boolean","selector-id","function"],style:{color:"#fdfdfd",textShadow:"0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"}},{types:["class-name","maybe-class-name","builtin"],style:{color:"#fff5f6",textShadow:"0 0 2px #000, 0 0 10px #fc1f2c75, 0 0 5px #fc1f2c75, 0 0 25px #fc1f2c75"}},{types:["constant","symbol"],style:{color:"#f92aad",textShadow:"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"}},{types:["important","atrule","keyword","selector-class"],style:{color:"#f4eee4",textShadow:"0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"}},{types:["string","char","attr-value","regex","variable"],style:{color:"#f87c32"}},{types:["parameter"],style:{fontStyle:"italic"}},{types:["entity","url"],style:{color:"#67cdcc"}},{types:["operator"],style:{color:"ffffffee"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["entity"],style:{cursor:"help"}},{types:["inserted"],style:{color:"green"}}]},_u=bu,wu={plain:{color:"#282a2e",backgroundColor:"#ffffff"},styles:[{types:["comment"],style:{color:"rgb(197, 200, 198)"}},{types:["string","number","builtin","variable"],style:{color:"rgb(150, 152, 150)"}},{types:["class-name","function","tag","attr-name"],style:{color:"rgb(40, 42, 46)"}}]},xu=wu,ku={plain:{color:"#9CDCFE",backgroundColor:"#1E1E1E"},styles:[{types:["prolog"],style:{color:"rgb(0, 0, 128)"}},{types:["comment"],style:{color:"rgb(106, 153, 85)"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"rgb(86, 156, 214)"}},{types:["number","inserted"],style:{color:"rgb(181, 206, 168)"}},{types:["constant"],style:{color:"rgb(100, 102, 149)"}},{types:["attr-name","variable"],style:{color:"rgb(156, 220, 254)"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"rgb(206, 145, 120)"}},{types:["selector"],style:{color:"rgb(215, 186, 125)"}},{types:["tag"],style:{color:"rgb(78, 201, 176)"}},{types:["tag"],languages:["markup"],style:{color:"rgb(86, 156, 214)"}},{types:["punctuation","operator"],style:{color:"rgb(212, 212, 212)"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"rgb(220, 220, 170)"}},{types:["class-name"],style:{color:"rgb(78, 201, 176)"}},{types:["char"],style:{color:"rgb(209, 105, 105)"}}]},Zs=ku,Su={plain:{color:"#000000",backgroundColor:"#ffffff"},styles:[{types:["comment"],style:{color:"rgb(0, 128, 0)"}},{types:["builtin"],style:{color:"rgb(0, 112, 193)"}},{types:["number","variable","inserted"],style:{color:"rgb(9, 134, 88)"}},{types:["operator"],style:{color:"rgb(0, 0, 0)"}},{types:["constant","char"],style:{color:"rgb(129, 31, 63)"}},{types:["tag"],style:{color:"rgb(128, 0, 0)"}},{types:["attr-name"],style:{color:"rgb(255, 0, 0)"}},{types:["deleted","string"],style:{color:"rgb(163, 21, 21)"}},{types:["changed","punctuation"],style:{color:"rgb(4, 81, 165)"}},{types:["function","keyword"],style:{color:"rgb(0, 0, 255)"}},{types:["class-name"],style:{color:"rgb(38, 127, 153)"}}]},Eu=Su,Au={plain:{color:"#f8fafc",backgroundColor:"#011627"},styles:[{types:["prolog"],style:{color:"#000080"}},{types:["comment"],style:{color:"#6A9955"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"#569CD6"}},{types:["number","inserted"],style:{color:"#B5CEA8"}},{types:["constant"],style:{color:"#f8fafc"}},{types:["attr-name","variable"],style:{color:"#9CDCFE"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"#cbd5e1"}},{types:["selector"],style:{color:"#D7BA7D"}},{types:["tag"],style:{color:"#0ea5e9"}},{types:["tag"],languages:["markup"],style:{color:"#0ea5e9"}},{types:["punctuation","operator"],style:{color:"#D4D4D4"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"#7dd3fc"}},{types:["class-name"],style:{color:"#0ea5e9"}},{types:["char"],style:{color:"#D16969"}}]},Cu=Au,Tu={plain:{color:"#0f172a",backgroundColor:"#f1f5f9"},styles:[{types:["prolog"],style:{color:"#000080"}},{types:["comment"],style:{color:"#6A9955"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"#0c4a6e"}},{types:["number","inserted"],style:{color:"#B5CEA8"}},{types:["constant"],style:{color:"#0f172a"}},{types:["attr-name","variable"],style:{color:"#0c4a6e"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"#64748b"}},{types:["selector"],style:{color:"#D7BA7D"}},{types:["tag"],style:{color:"#0ea5e9"}},{types:["tag"],languages:["markup"],style:{color:"#0ea5e9"}},{types:["punctuation","operator"],style:{color:"#475569"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"#0e7490"}},{types:["class-name"],style:{color:"#0ea5e9"}},{types:["char"],style:{color:"#D16969"}}]},Iu=Tu,Ru={plain:{backgroundColor:"hsl(220, 13%, 18%)",color:"hsl(220, 14%, 71%)",textShadow:"0 1px rgba(0, 0, 0, 0.3)"},styles:[{types:["comment","prolog","cdata"],style:{color:"hsl(220, 10%, 40%)"}},{types:["doctype","punctuation","entity"],style:{color:"hsl(220, 14%, 71%)"}},{types:["attr-name","class-name","maybe-class-name","boolean","constant","number","atrule"],style:{color:"hsl(29, 54%, 61%)"}},{types:["keyword"],style:{color:"hsl(286, 60%, 67%)"}},{types:["property","tag","symbol","deleted","important"],style:{color:"hsl(355, 65%, 65%)"}},{types:["selector","string","char","builtin","inserted","regex","attr-value"],style:{color:"hsl(95, 38%, 62%)"}},{types:["variable","operator","function"],style:{color:"hsl(207, 82%, 66%)"}},{types:["url"],style:{color:"hsl(187, 47%, 55%)"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"hsl(220, 14%, 71%)"}}]},Nu=Ru,Pu={plain:{backgroundColor:"hsl(230, 1%, 98%)",color:"hsl(230, 8%, 24%)"},styles:[{types:["comment","prolog","cdata"],style:{color:"hsl(230, 4%, 64%)"}},{types:["doctype","punctuation","entity"],style:{color:"hsl(230, 8%, 24%)"}},{types:["attr-name","class-name","boolean","constant","number","atrule"],style:{color:"hsl(35, 99%, 36%)"}},{types:["keyword"],style:{color:"hsl(301, 63%, 40%)"}},{types:["property","tag","symbol","deleted","important"],style:{color:"hsl(5, 74%, 59%)"}},{types:["selector","string","char","builtin","inserted","regex","attr-value","punctuation"],style:{color:"hsl(119, 34%, 47%)"}},{types:["variable","operator","function"],style:{color:"hsl(221, 87%, 60%)"}},{types:["url"],style:{color:"hsl(198, 99%, 37%)"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"hsl(230, 8%, 24%)"}}]},Ou=Pu,ju={plain:{color:"#ebdbb2",backgroundColor:"#292828"},styles:[{types:["imports","class-name","maybe-class-name","constant","doctype","builtin","function"],style:{color:"#d8a657"}},{types:["property-access"],style:{color:"#7daea3"}},{types:["tag"],style:{color:"#e78a4e"}},{types:["attr-name","char","url","regex"],style:{color:"#a9b665"}},{types:["attr-value","string"],style:{color:"#89b482"}},{types:["comment","prolog","cdata","operator","inserted"],style:{color:"#a89984"}},{types:["delimiter","boolean","keyword","selector","important","atrule","property","variable","deleted"],style:{color:"#ea6962"}},{types:["entity","number","symbol"],style:{color:"#d3869b"}}]},Mu=ju,Du={plain:{color:"#654735",backgroundColor:"#f9f5d7"},styles:[{types:["delimiter","boolean","keyword","selector","important","atrule","property","variable","deleted"],style:{color:"#af2528"}},{types:["imports","class-name","maybe-class-name","constant","doctype","builtin"],style:{color:"#b4730e"}},{types:["string","attr-value"],style:{color:"#477a5b"}},{types:["property-access"],style:{color:"#266b79"}},{types:["function","attr-name","char","url"],style:{color:"#72761e"}},{types:["tag"],style:{color:"#b94c07"}},{types:["comment","prolog","cdata","operator","inserted"],style:{color:"#a89984"}},{types:["entity","number","symbol"],style:{color:"#924f79"}}]},Fu=Du,$u=e=>y.useCallback(t=>{var n=t,{className:r,style:a,line:s}=n,o=Us(n,["className","style","line"]);const i=Ln(Le({},o),{className:js("token-line",r)});return typeof e=="object"&&"plain"in e&&(i.style=e.plain),typeof a=="object"&&(i.style=Le(Le({},i.style||{}),a)),i},[e]),Lu=e=>{const t=y.useCallback(({types:n,empty:r})=>{if(e!=null){{if(n.length===1&&n[0]==="plain")return r!=null?{display:"inline-block"}:void 0;if(n.length===1&&r!=null)return e[n[0]]}return Object.assign(r!=null?{display:"inline-block"}:{},...n.map(a=>e[a]))}},[e]);return y.useCallback(n=>{var r=n,{token:a,className:s,style:o}=r,i=Us(r,["token","className","style"]);const l=Ln(Le({},i),{className:js("token",...a.types,s),children:a.content,style:t(a)});return o!=null&&(l.style=Le(Le({},l.style||{}),o)),l},[t])},Bu=/\r\n|\r|\n/,Ua=e=>{e.length===0?e.push({types:["plain"],content:`
`,empty:!0}):e.length===1&&e[0].content===""&&(e[0].content=`
`,e[0].empty=!0)},za=(e,t)=>{const n=e.length;return n>0&&e[n-1]===t?e:e.concat(t)},Uu=e=>{const t=[[]],n=[e],r=[0],a=[e.length];let s=0,o=0,i=[];const l=[i];for(;o>-1;){for(;(s=r[o]++)<a[o];){let c,u=t[o];const g=n[o][s];if(typeof g=="string"?(u=o>0?u:["plain"],c=g):(u=za(u,g.type),g.alias&&(u=za(u,g.alias)),c=g.content),typeof c!="string"){o++,t.push(u),n.push(c),r.push(0),a.push(c.length);continue}const v=c.split(Bu),f=v.length;i.push({types:u,content:v[0]});for(let m=1;m<f;m++)Ua(i),l.push(i=[]),i.push({types:u,content:v[m]})}o--,t.pop(),n.pop(),r.pop(),a.pop()}return Ua(i),l},Za=Uu,zu=({prism:e,code:t,grammar:n,language:r})=>y.useMemo(()=>{if(n==null)return Za([t]);const a={code:t,grammar:n,language:r,tokens:[]};return e.hooks.run("before-tokenize",a),a.tokens=e.tokenize(t,n),e.hooks.run("after-tokenize",a),Za(a.tokens)},[t,n,r,e]),Zu=(e,t)=>{const{plain:n}=e,r=e.styles.reduce((a,s)=>{const{languages:o,style:i}=s;return o&&!o.includes(t)||s.types.forEach(l=>{const c=Le(Le({},a[l]),i);a[l]=c}),a},{});return r.root=n,r.plain=Ln(Le({},n),{backgroundColor:void 0}),r},Gu=Zu,Vu=({children:e,language:t,code:n,theme:r,prism:a})=>{const s=t.toLowerCase(),o=Gu(r,s),i=$u(o),l=Lu(o),c=a.languages[s],u=zu({prism:a,language:s,code:n,grammar:c});return e({tokens:u,className:`prism-code language-${s}`,style:o!=null?o.root:{},getLineProps:i,getTokenProps:l})},qu=e=>y.createElement(Vu,Ln(Le({},e),{prism:e.prism||x,theme:e.theme||Zs,code:e.code,language:e.language}));/*! Bundled license information:

prismjs/prism.js:
  (**
   * Prism: Lightweight, robust, elegant syntax highlighting
   *
   * @license MIT <https://opensource.org/licenses/MIT>
   * <AUTHOR> Verou <https://lea.verou.me>
   * @namespace
   * @public
   *)
*/const Hu=`
You are LeetCode Whisper, a friendly and conversational AI helper for students solving LeetCode problems. Your goal is to guide students step-by-step toward a solution without giving the full answer immediately.

Input Context:

Problem Statement: {{problem_statement}}
User Code: {{user_code}}
Programming Language: {{programming_language}}

Your Tasks:

Analyze User Code:

- Spot mistakes or inefficiencies in {{user_code}}.
- Start with small feedback and ask friendly follow-up questions, like where the user needs help.
- Keep the conversation flowing naturally, like you're chatting with a friend. 😊

Provide Hints:

- Share concise, relevant hints based on {{problem_statement}}.
- Let the user lead the conversation—give hints only when necessary.
- Avoid overwhelming the user with too many hints at once.

Suggest Code Snippets:

- Share tiny, focused code snippets only when they’re needed to illustrate a point.

Output Requirements:

- Keep the feedback short, friendly, and easy to understand.
- snippet should always be code only and is optional.
- Do not say hey everytime
- Keep making feedback more personal and short overrime.
- Limit the words in feedback. Only give what is really required to the user as feedback.
- Hints must be crisp, short and clear

Tone & Style:

- Be kind, supportive, and approachable.
- Use emojis like 🌟, 🙌, or ✅ to make the conversation fun and engaging.
- Avoid long, formal responses—be natural and conversational.

`;function Wu(e){return Array.from(e).map(n=>n.textContent||"").join(`
`)}function Ku(e,t){return y.useReducer((n,r)=>t[n][r]??n,e)}var Ze=e=>{const{present:t,children:n}=e,r=Yu(t),a=typeof n=="function"?n({present:r.isPresent}):y.Children.only(n),s=xe(r.ref,Ju(a));return typeof n=="function"||r.isPresent?y.cloneElement(a,{ref:s}):null};Ze.displayName="Presence";function Yu(e){const[t,n]=y.useState(),r=y.useRef(null),a=y.useRef(e),s=y.useRef("none"),o=e?"mounted":"unmounted",[i,l]=Ku(o,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return y.useEffect(()=>{const c=ln(r.current);s.current=i==="mounted"?c:"none"},[i]),fn(()=>{const c=r.current,u=a.current;if(u!==e){const g=s.current,v=ln(c);e?l("MOUNT"):v==="none"||(c==null?void 0:c.display)==="none"?l("UNMOUNT"):l(u&&g!==v?"ANIMATION_OUT":"UNMOUNT"),a.current=e}},[e,l]),fn(()=>{if(t){let c;const u=t.ownerDocument.defaultView??window,d=v=>{const m=ln(r.current).includes(CSS.escape(v.animationName));if(v.target===t&&m&&(l("ANIMATION_END"),!a.current)){const h=t.style.animationFillMode;t.style.animationFillMode="forwards",c=u.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=h)})}},g=v=>{v.target===t&&(s.current=ln(r.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{u.clearTimeout(c),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:y.useCallback(c=>{r.current=c?getComputedStyle(c):null,n(c)},[])}}function ln(e){return(e==null?void 0:e.animationName)||"none"}function Ju(e){var r,a;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(a=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:a.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Bn="Collapsible",[Xu,Gs]=wt(Bn),[Qu,na]=Xu(Bn),Vs=y.forwardRef((e,t)=>{const{__scopeCollapsible:n,open:r,defaultOpen:a,disabled:s,onOpenChange:o,...i}=e,[l,c]=xt({prop:r,defaultProp:a??!1,onChange:o,caller:Bn});return p.jsx(Qu,{scope:n,disabled:s,contentId:ot(),open:l,onOpenToggle:y.useCallback(()=>c(u=>!u),[c]),children:p.jsx(ye.div,{"data-state":aa(l),"data-disabled":s?"":void 0,...i,ref:t})})});Vs.displayName=Bn;var qs="CollapsibleTrigger",Hs=y.forwardRef((e,t)=>{const{__scopeCollapsible:n,...r}=e,a=na(qs,n);return p.jsx(ye.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":aa(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...r,ref:t,onClick:z(e.onClick,a.onOpenToggle)})});Hs.displayName=qs;var ra="CollapsibleContent",Ws=y.forwardRef((e,t)=>{const{forceMount:n,...r}=e,a=na(ra,e.__scopeCollapsible);return p.jsx(Ze,{present:n||a.open,children:({present:s})=>p.jsx(ed,{...r,ref:t,present:s})})});Ws.displayName=ra;var ed=y.forwardRef((e,t)=>{const{__scopeCollapsible:n,present:r,children:a,...s}=e,o=na(ra,n),[i,l]=y.useState(r),c=y.useRef(null),u=xe(t,c),d=y.useRef(0),g=d.current,v=y.useRef(0),f=v.current,m=o.open||i,h=y.useRef(m),b=y.useRef(void 0);return y.useEffect(()=>{const _=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(_)},[]),fn(()=>{const _=c.current;if(_){b.current=b.current||{transitionDuration:_.style.transitionDuration,animationName:_.style.animationName},_.style.transitionDuration="0s",_.style.animationName="none";const w=_.getBoundingClientRect();d.current=w.height,v.current=w.width,h.current||(_.style.transitionDuration=b.current.transitionDuration,_.style.animationName=b.current.animationName),l(r)}},[o.open,r]),p.jsx(ye.div,{"data-state":aa(o.open),"data-disabled":o.disabled?"":void 0,id:o.contentId,hidden:!m,...s,ref:u,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":f?`${f}px`:void 0,...e.style},children:m&&a})});function aa(e){return e?"open":"closed"}var td=Vs,nd=Hs,rd=Ws,De="Accordion",ad=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[sa,sd,od]=ea(De),[Un,Gy]=wt(De,[od,Gs]),oa=Gs(),Ks=ie.forwardRef((e,t)=>{const{type:n,...r}=e,a=r,s=r;return p.jsx(sa.Provider,{scope:e.__scopeAccordion,children:n==="multiple"?p.jsx(ud,{...s,ref:t}):p.jsx(cd,{...a,ref:t})})});Ks.displayName=De;var[Ys,id]=Un(De),[Js,ld]=Un(De,{collapsible:!1}),cd=ie.forwardRef((e,t)=>{const{value:n,defaultValue:r,onValueChange:a=()=>{},collapsible:s=!1,...o}=e,[i,l]=xt({prop:n,defaultProp:r??"",onChange:a,caller:De});return p.jsx(Ys,{scope:e.__scopeAccordion,value:ie.useMemo(()=>i?[i]:[],[i]),onItemOpen:l,onItemClose:ie.useCallback(()=>s&&l(""),[s,l]),children:p.jsx(Js,{scope:e.__scopeAccordion,collapsible:s,children:p.jsx(Xs,{...o,ref:t})})})}),ud=ie.forwardRef((e,t)=>{const{value:n,defaultValue:r,onValueChange:a=()=>{},...s}=e,[o,i]=xt({prop:n,defaultProp:r??[],onChange:a,caller:De}),l=ie.useCallback(u=>i((d=[])=>[...d,u]),[i]),c=ie.useCallback(u=>i((d=[])=>d.filter(g=>g!==u)),[i]);return p.jsx(Ys,{scope:e.__scopeAccordion,value:o,onItemOpen:l,onItemClose:c,children:p.jsx(Js,{scope:e.__scopeAccordion,collapsible:!0,children:p.jsx(Xs,{...s,ref:t})})})}),[dd,zn]=Un(De),Xs=ie.forwardRef((e,t)=>{const{__scopeAccordion:n,disabled:r,dir:a,orientation:s="vertical",...o}=e,i=ie.useRef(null),l=xe(i,t),c=sd(n),d=Fn(a)==="ltr",g=z(e.onKeyDown,v=>{var D;if(!ad.includes(v.key))return;const f=v.target,m=c().filter(I=>{var $;return!(($=I.ref.current)!=null&&$.disabled)}),h=m.findIndex(I=>I.ref.current===f),b=m.length;if(h===-1)return;v.preventDefault();let _=h;const w=0,A=b-1,O=()=>{_=h+1,_>A&&(_=w)},N=()=>{_=h-1,_<w&&(_=A)};switch(v.key){case"Home":_=w;break;case"End":_=A;break;case"ArrowRight":s==="horizontal"&&(d?O():N());break;case"ArrowDown":s==="vertical"&&O();break;case"ArrowLeft":s==="horizontal"&&(d?N():O());break;case"ArrowUp":s==="vertical"&&N();break}const F=_%b;(D=m[F].ref.current)==null||D.focus()});return p.jsx(dd,{scope:n,disabled:r,direction:a,orientation:s,children:p.jsx(sa.Slot,{scope:n,children:p.jsx(ye.div,{...o,"data-orientation":s,ref:l,onKeyDown:r?void 0:g})})})}),gn="AccordionItem",[pd,ia]=Un(gn),Qs=ie.forwardRef((e,t)=>{const{__scopeAccordion:n,value:r,...a}=e,s=zn(gn,n),o=id(gn,n),i=oa(n),l=ot(),c=r&&o.value.includes(r)||!1,u=s.disabled||e.disabled;return p.jsx(pd,{scope:n,open:c,disabled:u,triggerId:l,children:p.jsx(td,{"data-orientation":s.orientation,"data-state":so(c),...i,...a,ref:t,disabled:u,open:c,onOpenChange:d=>{d?o.onItemOpen(r):o.onItemClose(r)}})})});Qs.displayName=gn;var eo="AccordionHeader",to=ie.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,a=zn(De,n),s=ia(eo,n);return p.jsx(ye.h3,{"data-orientation":a.orientation,"data-state":so(s.open),"data-disabled":s.disabled?"":void 0,...r,ref:t})});to.displayName=eo;var ur="AccordionTrigger",no=ie.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,a=zn(De,n),s=ia(ur,n),o=ld(ur,n),i=oa(n);return p.jsx(sa.ItemSlot,{scope:n,children:p.jsx(nd,{"aria-disabled":s.open&&!o.collapsible||void 0,"data-orientation":a.orientation,id:s.triggerId,...i,...r,ref:t})})});no.displayName=ur;var ro="AccordionContent",ao=ie.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,a=zn(De,n),s=ia(ro,n),o=oa(n);return p.jsx(rd,{role:"region","aria-labelledby":s.triggerId,"data-orientation":a.orientation,...o,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});ao.displayName=ro;function so(e){return e?"open":"closed"}var fd=Ks,md=Qs,gd=to,oo=no,io=ao;const hd=fd,dr=y.forwardRef(({className:e,...t},n)=>p.jsx(md,{ref:n,className:me("border-b",e),...t}));dr.displayName="AccordionItem";const pr=y.forwardRef(({className:e,children:t,...n},r)=>p.jsx(gd,{className:"flex",children:p.jsxs(oo,{ref:r,className:me("flex flex-1 items-center justify-between py-4 text-sm font-medium transition-all hover:underline text-left [&[data-state=open]>svg]:rotate-180",e),...n,children:[t,p.jsx(Mc,{className:"h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200"})]})}));pr.displayName=oo.displayName;const fr=y.forwardRef(({className:e,children:t,...n},r)=>p.jsx(io,{ref:r,className:"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...n,children:p.jsx("div",{className:me("pb-4 pt-0",e),children:t})}));fr.displayName=io.displayName;const la=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:me("rounded-xl border bg-card text-card-foreground shadow",e),...t}));la.displayName="Card";const yd=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:me("flex flex-col space-y-1.5 p-6",e),...t}));yd.displayName="CardHeader";const vd=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:me("font-semibold leading-none tracking-tight",e),...t}));vd.displayName="CardTitle";const bd=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:me("text-sm text-muted-foreground",e),...t}));bd.displayName="CardDescription";const ca=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:me("p-6 pt-0",e),...t}));ca.displayName="CardContent";const lo=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:me("flex items-center p-6 pt-0",e),...t}));lo.displayName="CardFooter";var co="vercel.ai.error",_d=Symbol.for(co),uo,wd=class po extends Error{constructor({name:t,message:n,cause:r}){super(n),this[uo]=!0,this.name=t,this.cause=r}static isInstance(t){return po.hasMarker(t,co)}static hasMarker(t,n){const r=Symbol.for(n);return t!=null&&typeof t=="object"&&r in t&&typeof t[r]=="boolean"&&t[r]===!0}toJSON(){return{name:this.name,message:this.message}}};uo=_d;var H=wd,mr="AI_APICallError",fo=`vercel.ai.error.${mr}`,xd=Symbol.for(fo),mo,$e=class extends H{constructor({message:e,url:t,requestBodyValues:n,statusCode:r,responseHeaders:a,responseBody:s,cause:o,isRetryable:i=r!=null&&(r===408||r===409||r===429||r>=500),data:l}){super({name:mr,message:e,cause:o}),this[mo]=!0,this.url=t,this.requestBodyValues=n,this.statusCode=r,this.responseHeaders=a,this.responseBody=s,this.isRetryable=i,this.data=l}static isInstance(e){return H.hasMarker(e,fo)}static isAPICallError(e){return e instanceof Error&&e.name===mr&&typeof e.url=="string"&&typeof e.requestBodyValues=="object"&&(e.statusCode==null||typeof e.statusCode=="number")&&(e.responseHeaders==null||typeof e.responseHeaders=="object")&&(e.responseBody==null||typeof e.responseBody=="string")&&(e.cause==null||typeof e.cause=="object")&&typeof e.isRetryable=="boolean"&&(e.data==null||typeof e.data=="object")}toJSON(){return{name:this.name,message:this.message,url:this.url,requestBodyValues:this.requestBodyValues,statusCode:this.statusCode,responseHeaders:this.responseHeaders,responseBody:this.responseBody,cause:this.cause,isRetryable:this.isRetryable,data:this.data}}};mo=xd;var gr="AI_EmptyResponseBodyError",go=`vercel.ai.error.${gr}`,kd=Symbol.for(go),ho,Sd=class extends H{constructor({message:e="Empty response body"}={}){super({name:gr,message:e}),this[ho]=!0}static isInstance(e){return H.hasMarker(e,go)}static isEmptyResponseBodyError(e){return e instanceof Error&&e.name===gr}};ho=kd;function yo(e){return e==null?"unknown error":typeof e=="string"?e:e instanceof Error?e.message:JSON.stringify(e)}var vo="AI_InvalidArgumentError",bo=`vercel.ai.error.${vo}`,Ed=Symbol.for(bo),_o,Ad=class extends H{constructor({message:t,cause:n,argument:r}){super({name:vo,message:t,cause:n}),this[_o]=!0,this.argument=r}static isInstance(t){return H.hasMarker(t,bo)}};_o=Ed;var hr="AI_InvalidPromptError",wo=`vercel.ai.error.${hr}`,Cd=Symbol.for(wo),xo,rt=class extends H{constructor({prompt:e,message:t,cause:n}){super({name:hr,message:`Invalid prompt: ${t}`,cause:n}),this[xo]=!0,this.prompt=e}static isInstance(e){return H.hasMarker(e,wo)}static isInvalidPromptError(e){return e instanceof Error&&e.name===hr&&prompt!=null}toJSON(){return{name:this.name,message:this.message,stack:this.stack,prompt:this.prompt}}};xo=Cd;var yr="AI_InvalidResponseDataError",ko=`vercel.ai.error.${yr}`,Td=Symbol.for(ko),So,Yn=class extends H{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:yr,message:t}),this[So]=!0,this.data=e}static isInstance(e){return H.hasMarker(e,ko)}static isInvalidResponseDataError(e){return e instanceof Error&&e.name===yr&&e.data!=null}toJSON(){return{name:this.name,message:this.message,stack:this.stack,data:this.data}}};So=Td;var vr="AI_JSONParseError",Eo=`vercel.ai.error.${vr}`,Id=Symbol.for(Eo),Ao,hn=class extends H{constructor({text:e,cause:t}){super({name:vr,message:`JSON parsing failed: Text: ${e}.
Error message: ${yo(t)}`,cause:t}),this[Ao]=!0,this.text=e}static isInstance(e){return H.hasMarker(e,Eo)}static isJSONParseError(e){return e instanceof Error&&e.name===vr&&"text"in e&&typeof e.text=="string"}toJSON(){return{name:this.name,message:this.message,cause:this.cause,stack:this.stack,valueText:this.text}}};Ao=Id;var br="AI_LoadAPIKeyError",Co=`vercel.ai.error.${br}`,Rd=Symbol.for(Co),To,cn=class extends H{constructor({message:e}){super({name:br,message:e}),this[To]=!0}static isInstance(e){return H.hasMarker(e,Co)}static isLoadAPIKeyError(e){return e instanceof Error&&e.name===br}};To=Rd;var _r="AI_TooManyEmbeddingValuesForCallError",Io=`vercel.ai.error.${_r}`,Nd=Symbol.for(Io),Ro,No=class extends H{constructor(e){super({name:_r,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[Ro]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return H.hasMarker(e,Io)}static isTooManyEmbeddingValuesForCallError(e){return e instanceof Error&&e.name===_r&&"provider"in e&&typeof e.provider=="string"&&"modelId"in e&&typeof e.modelId=="string"&&"maxEmbeddingsPerCall"in e&&typeof e.maxEmbeddingsPerCall=="number"&&"values"in e&&Array.isArray(e.values)}toJSON(){return{name:this.name,message:this.message,stack:this.stack,provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:this.values}}};Ro=Nd;var wr="AI_TypeValidationError",Po=`vercel.ai.error.${wr}`,Pd=Symbol.for(Po),Oo,Od=class xr extends H{constructor({value:t,cause:n}){super({name:wr,message:`Type validation failed: Value: ${JSON.stringify(t)}.
Error message: ${yo(n)}`,cause:n}),this[Oo]=!0,this.value=t}static isInstance(t){return H.hasMarker(t,Po)}static wrap({value:t,cause:n}){return xr.isInstance(n)&&n.value===t?n:new xr({value:t,cause:n})}static isTypeValidationError(t){return t instanceof Error&&t.name===wr}toJSON(){return{name:this.name,message:this.message,cause:this.cause,stack:this.stack,value:this.value}}};Oo=Pd;var Je=Od,kr="AI_UnsupportedFunctionalityError",jo=`vercel.ai.error.${kr}`,jd=Symbol.for(jo),Mo,fe=class extends H{constructor({functionality:e}){super({name:kr,message:`'${e}' functionality not supported.`}),this[Mo]=!0,this.functionality=e}static isInstance(e){return H.hasMarker(e,jo)}static isUnsupportedFunctionalityError(e){return e instanceof Error&&e.name===kr&&typeof e.functionality=="string"}toJSON(){return{name:this.name,message:this.message,stack:this.stack,functionality:this.functionality}}};Mo=jd;function yn(e){return e===null||typeof e=="string"||typeof e=="number"||typeof e=="boolean"?!0:Array.isArray(e)?e.every(yn):typeof e=="object"?Object.entries(e).every(([t,n])=>typeof t=="string"&&yn(n)):!1}function Ga(e){return Array.isArray(e)&&e.every(yn)}function Sr(e){return e!=null&&typeof e=="object"&&Object.entries(e).every(([t,n])=>typeof t=="string"&&yn(n))}let Md=(e,t=21)=>(n=t)=>{let r="",a=n|0;for(;a--;)r+=e[Math.random()*e.length|0];return r};var kt={exports:{}};const Dd=typeof Buffer<"u",Va=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,qa=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function Do(e,t,n){n==null&&t!==null&&typeof t=="object"&&(n=t,t=void 0),Dd&&Buffer.isBuffer(e)&&(e=e.toString()),e&&e.charCodeAt(0)===65279&&(e=e.slice(1));const r=JSON.parse(e,t);if(r===null||typeof r!="object")return r;const a=n&&n.protoAction||"error",s=n&&n.constructorAction||"error";if(a==="ignore"&&s==="ignore")return r;if(a!=="ignore"&&s!=="ignore"){if(Va.test(e)===!1&&qa.test(e)===!1)return r}else if(a!=="ignore"&&s==="ignore"){if(Va.test(e)===!1)return r}else if(qa.test(e)===!1)return r;return Fo(r,{protoAction:a,constructorAction:s,safe:n&&n.safe})}function Fo(e,{protoAction:t="error",constructorAction:n="error",safe:r}={}){let a=[e];for(;a.length;){const s=a;a=[];for(const o of s){if(t!=="ignore"&&Object.prototype.hasOwnProperty.call(o,"__proto__")){if(r===!0)return null;if(t==="error")throw new SyntaxError("Object contains forbidden prototype property");delete o.__proto__}if(n!=="ignore"&&Object.prototype.hasOwnProperty.call(o,"constructor")&&Object.prototype.hasOwnProperty.call(o.constructor,"prototype")){if(r===!0)return null;if(n==="error")throw new SyntaxError("Object contains forbidden prototype property");delete o.constructor}for(const i in o){const l=o[i];l&&typeof l=="object"&&a.push(l)}}}return e}function ua(e,t,n){const r=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return Do(e,t,n)}finally{Error.stackTraceLimit=r}}function Fd(e,t){const n=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return Do(e,t,{safe:!0})}catch{return null}finally{Error.stackTraceLimit=n}}kt.exports=ua;kt.exports.default=ua;kt.exports.parse=ua;kt.exports.safeParse=Fd;kt.exports.scan=Fo;var $d=kt.exports;const da=lc($d);function Ld(e){let t,n,r,a,s,o,i;return l(),{feed:c,reset:l};function l(){t=!0,n="",r=0,a=-1,s=void 0,o=void 0,i=""}function c(d){n=n?n+d:d,t&&Bd(n)&&(n=n.slice($o.length)),t=!1;const g=n.length;let v=0,f=!1;for(;v<g;){f&&(n[v]===`
`&&++v,f=!1);let m=-1,h=a,b;for(let _=r;m<0&&_<g;++_)b=n[_],b===":"&&h<0?h=_-v:b==="\r"?(f=!0,m=_-v):b===`
`&&(m=_-v);if(m<0){r=g-v,a=h;break}else r=0,a=-1;u(n,v,h,m),v+=m+1}v===g?n="":v>0&&(n=n.slice(v))}function u(d,g,v,f){if(f===0){i.length>0&&(e({type:"event",id:s,event:o||void 0,data:i.slice(0,-1)}),i="",s=void 0),o=void 0;return}const m=v<0,h=d.slice(g,g+(m?f:v));let b=0;m?b=f:d[g+v+1]===" "?b=v+2:b=v+1;const _=g+b,w=f-b,A=d.slice(_,_+w).toString();if(h==="data")i+=A?"".concat(A,`
`):`
`;else if(h==="event")o=A;else if(h==="id"&&!A.includes("\0"))s=A;else if(h==="retry"){const O=parseInt(A,10);Number.isNaN(O)||e({type:"reconnect-interval",value:O})}}}const $o=[239,187,191];function Bd(e){return $o.every((t,n)=>e.charCodeAt(n)===t)}class Ud extends TransformStream{constructor(){let t;super({start(n){t=Ld(r=>{r.type==="event"&&n.enqueue(r)})},transform(n){t.feed(n)}})}}var zd={};function Xe(...e){return e.reduce((t,n)=>({...t,...n??{}}),{})}function Zn(e){const t={};return e.headers.forEach((n,r)=>{t[r]=n}),t}var tn=({prefix:e,size:t=7,alphabet:n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:r="-"}={})=>{const a=Md(n,t);if(e==null)return a;if(n.includes(r))throw new Ad({argument:"separator",message:`The separator "${r}" must not be part of the alphabet "${n}".`});return s=>`${e}${r}${a(s)}`},dt=tn();function Zd(e){return e==null?"unknown error":typeof e=="string"?e:e instanceof Error?e.message:JSON.stringify(e)}function pn(e){return e instanceof Error&&(e.name==="AbortError"||e.name==="TimeoutError")}function Lo({apiKey:e,environmentVariableName:t,apiKeyParameterName:n="apiKey",description:r}){if(typeof e=="string")return e;if(e!=null)throw new cn({message:`${r} API key must be a string.`});if(typeof process>"u")throw new cn({message:`${r} API key is missing. Pass it using the '${n}' parameter. Environment variables is not supported in this environment.`});if(e=zd[t],e==null)throw new cn({message:`${r} API key is missing. Pass it using the '${n}' parameter or the ${t} environment variable.`});if(typeof e!="string")throw new cn({message:`${r} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}var vn=Symbol.for("vercel.ai.validator");function Gd(e){return{[vn]:!0,validate:e}}function Vd(e){return typeof e=="object"&&e!==null&&vn in e&&e[vn]===!0&&"validate"in e}function qd(e){return Vd(e)?e:Hd(e)}function Hd(e){return Gd(t=>{const n=e.safeParse(t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}})}function Wd({value:e,schema:t}){const n=mt({value:e,schema:t});if(!n.success)throw Je.wrap({value:e,cause:n.error});return n.value}function mt({value:e,schema:t}){const n=qd(t);try{if(n.validate==null)return{success:!0,value:e};const r=n.validate(e);return r.success?r:{success:!1,error:Je.wrap({value:e,cause:r.error})}}catch(r){return{success:!1,error:Je.wrap({value:e,cause:r})}}}function Kd({text:e,schema:t}){try{const n=da.parse(e);return t==null?n:Wd({value:n,schema:t})}catch(n){throw hn.isJSONParseError(n)||Je.isTypeValidationError(n)?n:new hn({text:e,cause:n})}}function pa({text:e,schema:t}){try{const n=da.parse(e);return t==null?{success:!0,value:n}:mt({value:n,schema:t})}catch(n){return{success:!1,error:hn.isJSONParseError(n)?n:new hn({text:e,cause:n})}}}function Ha(e){try{return da.parse(e),!0}catch{return!1}}function Yd(e){return Object.fromEntries(Object.entries(e).filter(([t,n])=>n!=null))}var Jd=()=>globalThis.fetch,Qe=async({url:e,headers:t,body:n,failedResponseHandler:r,successfulResponseHandler:a,abortSignal:s,fetch:o})=>Xd({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(n),values:n},failedResponseHandler:r,successfulResponseHandler:a,abortSignal:s,fetch:o}),Xd=async({url:e,headers:t={},body:n,successfulResponseHandler:r,failedResponseHandler:a,abortSignal:s,fetch:o=Jd()})=>{try{const i=await o(e,{method:"POST",headers:Yd(t),body:n.content,signal:s}),l=Zn(i);if(!i.ok){let c;try{c=await a({response:i,url:e,requestBodyValues:n.values})}catch(u){throw pn(u)||$e.isAPICallError(u)?u:new $e({message:"Failed to process error response",cause:u,statusCode:i.status,url:e,responseHeaders:l,requestBodyValues:n.values})}throw c.value}try{return await r({response:i,url:e,requestBodyValues:n.values})}catch(c){throw c instanceof Error&&(pn(c)||$e.isAPICallError(c))?c:new $e({message:"Failed to process successful response",cause:c,statusCode:i.status,url:e,responseHeaders:l,requestBodyValues:n.values})}}catch(i){if(pn(i))throw i;if(i instanceof TypeError&&i.message==="fetch failed"){const l=i.cause;if(l!=null)throw new $e({message:`Cannot connect to API: ${l.message}`,cause:l,url:e,requestBodyValues:n.values,isRetryable:!0})}throw i}},Bo=({errorSchema:e,errorToMessage:t,isRetryable:n})=>async({response:r,url:a,requestBodyValues:s})=>{const o=await r.text(),i=Zn(r);if(o.trim()==="")return{responseHeaders:i,value:new $e({message:r.statusText,url:a,requestBodyValues:s,statusCode:r.status,responseHeaders:i,responseBody:o,isRetryable:n==null?void 0:n(r)})};try{const l=Kd({text:o,schema:e});return{responseHeaders:i,value:new $e({message:t(l),url:a,requestBodyValues:s,statusCode:r.status,responseHeaders:i,responseBody:o,data:l,isRetryable:n==null?void 0:n(r,l)})}}catch{return{responseHeaders:i,value:new $e({message:r.statusText,url:a,requestBodyValues:s,statusCode:r.status,responseHeaders:i,responseBody:o,isRetryable:n==null?void 0:n(r)})}}},fa=e=>async({response:t})=>{const n=Zn(t);if(t.body==null)throw new Sd({});return{responseHeaders:n,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(new Ud).pipeThrough(new TransformStream({transform({data:r},a){r!=="[DONE]"&&a.enqueue(pa({text:r,schema:e}))}}))}},nn=e=>async({response:t,url:n,requestBodyValues:r})=>{const a=await t.text(),s=pa({text:a,schema:e}),o=Zn(t);if(!s.success)throw new $e({message:"Invalid JSON response",cause:s.error,statusCode:t.status,responseHeaders:o,responseBody:a,url:n,requestBodyValues:r});return{responseHeaders:o,value:s.value}},{btoa:Qd,atob:ep}=globalThis;function tp(e){const t=e.replace(/-/g,"+").replace(/_/g,"/"),n=ep(t);return Uint8Array.from(n,r=>r.codePointAt(0))}function bn(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCodePoint(e[n]);return Qd(t)}function Uo(e){return e==null?void 0:e.replace(/\/$/,"")}var Y;(function(e){e.assertEqual=a=>{};function t(a){}e.assertIs=t;function n(a){throw new Error}e.assertNever=n,e.arrayToEnum=a=>{const s={};for(const o of a)s[o]=o;return s},e.getValidEnumValues=a=>{const s=e.objectKeys(a).filter(i=>typeof a[a[i]]!="number"),o={};for(const i of s)o[i]=a[i];return e.objectValues(o)},e.objectValues=a=>e.objectKeys(a).map(function(s){return a[s]}),e.objectKeys=typeof Object.keys=="function"?a=>Object.keys(a):a=>{const s=[];for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&s.push(o);return s},e.find=(a,s)=>{for(const o of a)if(s(o))return o},e.isInteger=typeof Number.isInteger=="function"?a=>Number.isInteger(a):a=>typeof a=="number"&&Number.isFinite(a)&&Math.floor(a)===a;function r(a,s=" | "){return a.map(o=>typeof o=="string"?`'${o}'`:o).join(s)}e.joinValues=r,e.jsonStringifyReplacer=(a,s)=>typeof s=="bigint"?s.toString():s})(Y||(Y={}));var Wa;(function(e){e.mergeShapes=(t,n)=>({...t,...n})})(Wa||(Wa={}));const P=Y.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Ke=e=>{switch(typeof e){case"undefined":return P.undefined;case"string":return P.string;case"number":return Number.isNaN(e)?P.nan:P.number;case"boolean":return P.boolean;case"function":return P.function;case"bigint":return P.bigint;case"symbol":return P.symbol;case"object":return Array.isArray(e)?P.array:e===null?P.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?P.promise:typeof Map<"u"&&e instanceof Map?P.map:typeof Set<"u"&&e instanceof Set?P.set:typeof Date<"u"&&e instanceof Date?P.date:P.object;default:return P.unknown}},k=Y.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class qe extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(s){return s.message},r={_errors:[]},a=s=>{for(const o of s.issues)if(o.code==="invalid_union")o.unionErrors.map(a);else if(o.code==="invalid_return_type")a(o.returnTypeError);else if(o.code==="invalid_arguments")a(o.argumentsError);else if(o.path.length===0)r._errors.push(n(o));else{let i=r,l=0;for(;l<o.path.length;){const c=o.path[l];l===o.path.length-1?(i[c]=i[c]||{_errors:[]},i[c]._errors.push(n(o))):i[c]=i[c]||{_errors:[]},i=i[c],l++}}};return a(this),r}static assert(t){if(!(t instanceof qe))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Y.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const a of this.issues)if(a.path.length>0){const s=a.path[0];n[s]=n[s]||[],n[s].push(t(a))}else r.push(t(a));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}qe.create=e=>new qe(e);const Er=(e,t)=>{let n;switch(e.code){case k.invalid_type:e.received===P.undefined?n="Required":n=`Expected ${e.expected}, received ${e.received}`;break;case k.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,Y.jsonStringifyReplacer)}`;break;case k.unrecognized_keys:n=`Unrecognized key(s) in object: ${Y.joinValues(e.keys,", ")}`;break;case k.invalid_union:n="Invalid input";break;case k.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${Y.joinValues(e.options)}`;break;case k.invalid_enum_value:n=`Invalid enum value. Expected ${Y.joinValues(e.options)}, received '${e.received}'`;break;case k.invalid_arguments:n="Invalid function arguments";break;case k.invalid_return_type:n="Invalid function return type";break;case k.invalid_date:n="Invalid date";break;case k.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:Y.assertNever(e.validation):e.validation!=="regex"?n=`Invalid ${e.validation}`:n="Invalid";break;case k.too_small:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="bigint"?n=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:n="Invalid input";break;case k.too_big:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?n=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:n="Invalid input";break;case k.custom:n="Invalid input";break;case k.invalid_intersection_types:n="Intersection results could not be merged";break;case k.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case k.not_finite:n="Number must be finite";break;default:n=t.defaultError,Y.assertNever(e)}return{message:n}};let np=Er;function rp(){return np}const ap=e=>{const{data:t,path:n,errorMaps:r,issueData:a}=e,s=[...n,...a.path||[]],o={...a,path:s};if(a.message!==void 0)return{...a,path:s,message:a.message};let i="";const l=r.filter(c=>!!c).slice().reverse();for(const c of l)i=c(o,{data:t,defaultError:i}).message;return{...a,path:s,message:i}};function T(e,t){const n=rp(),r=ap({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===Er?void 0:Er].filter(a=>!!a)});e.common.issues.push(r)}class we{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const a of n){if(a.status==="aborted")return U;a.status==="dirty"&&t.dirty(),r.push(a.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const a of n){const s=await a.key,o=await a.value;r.push({key:s,value:o})}return we.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const a of n){const{key:s,value:o}=a;if(s.status==="aborted"||o.status==="aborted")return U;s.status==="dirty"&&t.dirty(),o.status==="dirty"&&t.dirty(),s.value!=="__proto__"&&(typeof o.value<"u"||a.alwaysSet)&&(r[s.value]=o.value)}return{status:t.value,value:r}}}const U=Object.freeze({status:"aborted"}),At=e=>({status:"dirty",value:e}),Ne=e=>({status:"valid",value:e}),Ka=e=>e.status==="aborted",Ya=e=>e.status==="dirty",gt=e=>e.status==="valid",_n=e=>typeof Promise<"u"&&e instanceof Promise;var j;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(j||(j={}));class Ue{constructor(t,n,r,a){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ja=(e,t)=>{if(gt(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new qe(e.common.issues);return this._error=n,this._error}}};function Z(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:a}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:a}:{errorMap:(o,i)=>{const{message:l}=e;return o.code==="invalid_enum_value"?{message:l??i.defaultError}:typeof i.data>"u"?{message:l??r??i.defaultError}:o.code!=="invalid_type"?{message:i.defaultError}:{message:l??n??i.defaultError}},description:a}}class q{get description(){return this._def.description}_getType(t){return Ke(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:Ke(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new we,ctx:{common:t.parent.common,data:t.data,parsedType:Ke(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if(_n(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){const r={common:{issues:[],async:(n==null?void 0:n.async)??!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Ke(t)},a=this._parseSync({data:t,path:r.path,parent:r});return Ja(r,a)}"~validate"(t){var r,a;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Ke(t)};if(!this["~standard"].async)try{const s=this._parseSync({data:t,path:[],parent:n});return gt(s)?{value:s.value}:{issues:n.common.issues}}catch(s){(a=(r=s==null?void 0:s.message)==null?void 0:r.toLowerCase())!=null&&a.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:n}).then(s=>gt(s)?{value:s.value}:{issues:n.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Ke(t)},a=this._parse({data:t,path:r.path,parent:r}),s=await(_n(a)?a:Promise.resolve(a));return Ja(r,s)}refine(t,n){const r=a=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(a):n;return this._refinement((a,s)=>{const o=t(a),i=()=>s.addIssue({code:k.custom,...r(a)});return typeof Promise<"u"&&o instanceof Promise?o.then(l=>l?!0:(i(),!1)):o?!0:(i(),!1)})}refinement(t,n){return this._refinement((r,a)=>t(r)?!0:(a.addIssue(typeof n=="function"?n(r,a):n),!1))}_refinement(t){return new vt({schema:this,typeName:S.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return Ye.create(this,this._def)}nullable(){return bt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Be.create(this)}promise(){return En.create(this,this._def)}or(t){return xn.create([this,t],this._def)}and(t){return kn.create(this,t,this._def)}transform(t){return new vt({...Z(this._def),schema:this,typeName:S.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new Pr({...Z(this._def),innerType:this,defaultValue:n,typeName:S.ZodDefault})}brand(){return new Cp({typeName:S.ZodBranded,type:this,...Z(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new Or({...Z(this._def),innerType:this,catchValue:n,typeName:S.ZodCatch})}describe(t){const n=this.constructor;return new n({...this._def,description:t})}pipe(t){return ma.create(this,t)}readonly(){return jr.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const sp=/^c[^\s-]{8,}$/i,op=/^[0-9a-z]+$/,ip=/^[0-9A-HJKMNP-TV-Z]{26}$/i,lp=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,cp=/^[a-z0-9_-]{21}$/i,up=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,dp=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,pp=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,fp="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Jn;const mp=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,gp=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,hp=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,yp=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,vp=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,bp=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,zo="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",_p=new RegExp(`^${zo}$`);function Zo(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`);const n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}function wp(e){return new RegExp(`^${Zo(e)}$`)}function xp(e){let t=`${zo}T${Zo(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function kp(e,t){return!!((t==="v4"||!t)&&mp.test(e)||(t==="v6"||!t)&&hp.test(e))}function Sp(e,t){if(!up.test(e))return!1;try{const[n]=e.split(".");if(!n)return!1;const r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),a=JSON.parse(atob(r));return!(typeof a!="object"||a===null||"typ"in a&&(a==null?void 0:a.typ)!=="JWT"||!a.alg||t&&a.alg!==t)}catch{return!1}}function Ep(e,t){return!!((t==="v4"||!t)&&gp.test(e)||(t==="v6"||!t)&&yp.test(e))}class Ve extends q{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==P.string){const s=this._getOrReturnCtx(t);return T(s,{code:k.invalid_type,expected:P.string,received:s.parsedType}),U}const r=new we;let a;for(const s of this._def.checks)if(s.kind==="min")t.data.length<s.value&&(a=this._getOrReturnCtx(t,a),T(a,{code:k.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),r.dirty());else if(s.kind==="max")t.data.length>s.value&&(a=this._getOrReturnCtx(t,a),T(a,{code:k.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),r.dirty());else if(s.kind==="length"){const o=t.data.length>s.value,i=t.data.length<s.value;(o||i)&&(a=this._getOrReturnCtx(t,a),o?T(a,{code:k.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):i&&T(a,{code:k.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),r.dirty())}else if(s.kind==="email")pp.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"email",code:k.invalid_string,message:s.message}),r.dirty());else if(s.kind==="emoji")Jn||(Jn=new RegExp(fp,"u")),Jn.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"emoji",code:k.invalid_string,message:s.message}),r.dirty());else if(s.kind==="uuid")lp.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"uuid",code:k.invalid_string,message:s.message}),r.dirty());else if(s.kind==="nanoid")cp.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"nanoid",code:k.invalid_string,message:s.message}),r.dirty());else if(s.kind==="cuid")sp.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"cuid",code:k.invalid_string,message:s.message}),r.dirty());else if(s.kind==="cuid2")op.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"cuid2",code:k.invalid_string,message:s.message}),r.dirty());else if(s.kind==="ulid")ip.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"ulid",code:k.invalid_string,message:s.message}),r.dirty());else if(s.kind==="url")try{new URL(t.data)}catch{a=this._getOrReturnCtx(t,a),T(a,{validation:"url",code:k.invalid_string,message:s.message}),r.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"regex",code:k.invalid_string,message:s.message}),r.dirty())):s.kind==="trim"?t.data=t.data.trim():s.kind==="includes"?t.data.includes(s.value,s.position)||(a=this._getOrReturnCtx(t,a),T(a,{code:k.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),r.dirty()):s.kind==="toLowerCase"?t.data=t.data.toLowerCase():s.kind==="toUpperCase"?t.data=t.data.toUpperCase():s.kind==="startsWith"?t.data.startsWith(s.value)||(a=this._getOrReturnCtx(t,a),T(a,{code:k.invalid_string,validation:{startsWith:s.value},message:s.message}),r.dirty()):s.kind==="endsWith"?t.data.endsWith(s.value)||(a=this._getOrReturnCtx(t,a),T(a,{code:k.invalid_string,validation:{endsWith:s.value},message:s.message}),r.dirty()):s.kind==="datetime"?xp(s).test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{code:k.invalid_string,validation:"datetime",message:s.message}),r.dirty()):s.kind==="date"?_p.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{code:k.invalid_string,validation:"date",message:s.message}),r.dirty()):s.kind==="time"?wp(s).test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{code:k.invalid_string,validation:"time",message:s.message}),r.dirty()):s.kind==="duration"?dp.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"duration",code:k.invalid_string,message:s.message}),r.dirty()):s.kind==="ip"?kp(t.data,s.version)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"ip",code:k.invalid_string,message:s.message}),r.dirty()):s.kind==="jwt"?Sp(t.data,s.alg)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"jwt",code:k.invalid_string,message:s.message}),r.dirty()):s.kind==="cidr"?Ep(t.data,s.version)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"cidr",code:k.invalid_string,message:s.message}),r.dirty()):s.kind==="base64"?vp.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"base64",code:k.invalid_string,message:s.message}),r.dirty()):s.kind==="base64url"?bp.test(t.data)||(a=this._getOrReturnCtx(t,a),T(a,{validation:"base64url",code:k.invalid_string,message:s.message}),r.dirty()):Y.assertNever(s);return{status:r.value,value:t.data}}_regex(t,n,r){return this.refinement(a=>t.test(a),{validation:n,code:k.invalid_string,...j.errToObj(r)})}_addCheck(t){return new Ve({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...j.errToObj(t)})}url(t){return this._addCheck({kind:"url",...j.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...j.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...j.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...j.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...j.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...j.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...j.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...j.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...j.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...j.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...j.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...j.errToObj(t)})}datetime(t){return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(t==null?void 0:t.offset)??!1,local:(t==null?void 0:t.local)??!1,...j.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...j.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...j.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...j.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...j.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...j.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...j.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...j.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...j.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...j.errToObj(n)})}nonempty(t){return this.min(1,j.errToObj(t))}trim(){return new Ve({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ve({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ve({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}Ve.create=e=>new Ve({checks:[],typeName:S.ZodString,coerce:(e==null?void 0:e.coerce)??!1,...Z(e)});function Ap(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,a=n>r?n:r,s=Number.parseInt(e.toFixed(a).replace(".","")),o=Number.parseInt(t.toFixed(a).replace(".",""));return s%o/10**a}class ht extends q{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==P.number){const s=this._getOrReturnCtx(t);return T(s,{code:k.invalid_type,expected:P.number,received:s.parsedType}),U}let r;const a=new we;for(const s of this._def.checks)s.kind==="int"?Y.isInteger(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{code:k.invalid_type,expected:"integer",received:"float",message:s.message}),a.dirty()):s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(r=this._getOrReturnCtx(t,r),T(r,{code:k.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(r=this._getOrReturnCtx(t,r),T(r,{code:k.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="multipleOf"?Ap(t.data,s.value)!==0&&(r=this._getOrReturnCtx(t,r),T(r,{code:k.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):s.kind==="finite"?Number.isFinite(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{code:k.not_finite,message:s.message}),a.dirty()):Y.assertNever(s);return{status:a.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,j.toString(n))}gt(t,n){return this.setLimit("min",t,!1,j.toString(n))}lte(t,n){return this.setLimit("max",t,!0,j.toString(n))}lt(t,n){return this.setLimit("max",t,!1,j.toString(n))}setLimit(t,n,r,a){return new ht({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:j.toString(a)}]})}_addCheck(t){return new ht({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:j.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:j.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:j.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:j.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:j.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:j.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:j.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:j.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:j.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&Y.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}ht.create=e=>new ht({checks:[],typeName:S.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...Z(e)});class Rt extends q{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==P.bigint)return this._getInvalidInput(t);let r;const a=new we;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(r=this._getOrReturnCtx(t,r),T(r,{code:k.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(r=this._getOrReturnCtx(t,r),T(r,{code:k.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="multipleOf"?t.data%s.value!==BigInt(0)&&(r=this._getOrReturnCtx(t,r),T(r,{code:k.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):Y.assertNever(s);return{status:a.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return T(n,{code:k.invalid_type,expected:P.bigint,received:n.parsedType}),U}gte(t,n){return this.setLimit("min",t,!0,j.toString(n))}gt(t,n){return this.setLimit("min",t,!1,j.toString(n))}lte(t,n){return this.setLimit("max",t,!0,j.toString(n))}lt(t,n){return this.setLimit("max",t,!1,j.toString(n))}setLimit(t,n,r,a){return new Rt({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:j.toString(a)}]})}_addCheck(t){return new Rt({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:j.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:j.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:j.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:j.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:j.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}Rt.create=e=>new Rt({checks:[],typeName:S.ZodBigInt,coerce:(e==null?void 0:e.coerce)??!1,...Z(e)});class Ar extends q{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==P.boolean){const r=this._getOrReturnCtx(t);return T(r,{code:k.invalid_type,expected:P.boolean,received:r.parsedType}),U}return Ne(t.data)}}Ar.create=e=>new Ar({typeName:S.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...Z(e)});class wn extends q{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==P.date){const s=this._getOrReturnCtx(t);return T(s,{code:k.invalid_type,expected:P.date,received:s.parsedType}),U}if(Number.isNaN(t.data.getTime())){const s=this._getOrReturnCtx(t);return T(s,{code:k.invalid_date}),U}const r=new we;let a;for(const s of this._def.checks)s.kind==="min"?t.data.getTime()<s.value&&(a=this._getOrReturnCtx(t,a),T(a,{code:k.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),r.dirty()):s.kind==="max"?t.data.getTime()>s.value&&(a=this._getOrReturnCtx(t,a),T(a,{code:k.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),r.dirty()):Y.assertNever(s);return{status:r.value,value:new Date(t.data.getTime())}}_addCheck(t){return new wn({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:j.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:j.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}wn.create=e=>new wn({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:S.ZodDate,...Z(e)});class Xa extends q{_parse(t){if(this._getType(t)!==P.symbol){const r=this._getOrReturnCtx(t);return T(r,{code:k.invalid_type,expected:P.symbol,received:r.parsedType}),U}return Ne(t.data)}}Xa.create=e=>new Xa({typeName:S.ZodSymbol,...Z(e)});class Qa extends q{_parse(t){if(this._getType(t)!==P.undefined){const r=this._getOrReturnCtx(t);return T(r,{code:k.invalid_type,expected:P.undefined,received:r.parsedType}),U}return Ne(t.data)}}Qa.create=e=>new Qa({typeName:S.ZodUndefined,...Z(e)});class Cr extends q{_parse(t){if(this._getType(t)!==P.null){const r=this._getOrReturnCtx(t);return T(r,{code:k.invalid_type,expected:P.null,received:r.parsedType}),U}return Ne(t.data)}}Cr.create=e=>new Cr({typeName:S.ZodNull,...Z(e)});class Nt extends q{constructor(){super(...arguments),this._any=!0}_parse(t){return Ne(t.data)}}Nt.create=e=>new Nt({typeName:S.ZodAny,...Z(e)});class Tr extends q{constructor(){super(...arguments),this._unknown=!0}_parse(t){return Ne(t.data)}}Tr.create=e=>new Tr({typeName:S.ZodUnknown,...Z(e)});class et extends q{_parse(t){const n=this._getOrReturnCtx(t);return T(n,{code:k.invalid_type,expected:P.never,received:n.parsedType}),U}}et.create=e=>new et({typeName:S.ZodNever,...Z(e)});class es extends q{_parse(t){if(this._getType(t)!==P.undefined){const r=this._getOrReturnCtx(t);return T(r,{code:k.invalid_type,expected:P.void,received:r.parsedType}),U}return Ne(t.data)}}es.create=e=>new es({typeName:S.ZodVoid,...Z(e)});class Be extends q{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),a=this._def;if(n.parsedType!==P.array)return T(n,{code:k.invalid_type,expected:P.array,received:n.parsedType}),U;if(a.exactLength!==null){const o=n.data.length>a.exactLength.value,i=n.data.length<a.exactLength.value;(o||i)&&(T(n,{code:o?k.too_big:k.too_small,minimum:i?a.exactLength.value:void 0,maximum:o?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(a.minLength!==null&&n.data.length<a.minLength.value&&(T(n,{code:k.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),a.maxLength!==null&&n.data.length>a.maxLength.value&&(T(n,{code:k.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((o,i)=>a.type._parseAsync(new Ue(n,o,n.path,i)))).then(o=>we.mergeArray(r,o));const s=[...n.data].map((o,i)=>a.type._parseSync(new Ue(n,o,n.path,i)));return we.mergeArray(r,s)}get element(){return this._def.type}min(t,n){return new Be({...this._def,minLength:{value:t,message:j.toString(n)}})}max(t,n){return new Be({...this._def,maxLength:{value:t,message:j.toString(n)}})}length(t,n){return new Be({...this._def,exactLength:{value:t,message:j.toString(n)}})}nonempty(t){return this.min(1,t)}}Be.create=(e,t)=>new Be({type:e,minLength:null,maxLength:null,exactLength:null,typeName:S.ZodArray,...Z(t)});function pt(e){if(e instanceof ge){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=Ye.create(pt(r))}return new ge({...e._def,shape:()=>t})}else return e instanceof Be?new Be({...e._def,type:pt(e.element)}):e instanceof Ye?Ye.create(pt(e.unwrap())):e instanceof bt?bt.create(pt(e.unwrap())):e instanceof it?it.create(e.items.map(t=>pt(t))):e}class ge extends q{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=Y.objectKeys(t);return this._cached={shape:t,keys:n},this._cached}_parse(t){if(this._getType(t)!==P.object){const c=this._getOrReturnCtx(t);return T(c,{code:k.invalid_type,expected:P.object,received:c.parsedType}),U}const{status:r,ctx:a}=this._processInputParams(t),{shape:s,keys:o}=this._getCached(),i=[];if(!(this._def.catchall instanceof et&&this._def.unknownKeys==="strip"))for(const c in a.data)o.includes(c)||i.push(c);const l=[];for(const c of o){const u=s[c],d=a.data[c];l.push({key:{status:"valid",value:c},value:u._parse(new Ue(a,d,a.path,c)),alwaysSet:c in a.data})}if(this._def.catchall instanceof et){const c=this._def.unknownKeys;if(c==="passthrough")for(const u of i)l.push({key:{status:"valid",value:u},value:{status:"valid",value:a.data[u]}});else if(c==="strict")i.length>0&&(T(a,{code:k.unrecognized_keys,keys:i}),r.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const u of i){const d=a.data[u];l.push({key:{status:"valid",value:u},value:c._parse(new Ue(a,d,a.path,u)),alwaysSet:u in a.data})}}return a.common.async?Promise.resolve().then(async()=>{const c=[];for(const u of l){const d=await u.key,g=await u.value;c.push({key:d,value:g,alwaysSet:u.alwaysSet})}return c}).then(c=>we.mergeObjectSync(r,c)):we.mergeObjectSync(r,l)}get shape(){return this._def.shape()}strict(t){return j.errToObj,new ge({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var s,o;const a=((o=(s=this._def).errorMap)==null?void 0:o.call(s,n,r).message)??r.defaultError;return n.code==="unrecognized_keys"?{message:j.errToObj(t).message??a}:{message:a}}}:{}})}strip(){return new ge({...this._def,unknownKeys:"strip"})}passthrough(){return new ge({...this._def,unknownKeys:"passthrough"})}extend(t){return new ge({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new ge({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:S.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new ge({...this._def,catchall:t})}pick(t){const n={};for(const r of Y.objectKeys(t))t[r]&&this.shape[r]&&(n[r]=this.shape[r]);return new ge({...this._def,shape:()=>n})}omit(t){const n={};for(const r of Y.objectKeys(this.shape))t[r]||(n[r]=this.shape[r]);return new ge({...this._def,shape:()=>n})}deepPartial(){return pt(this)}partial(t){const n={};for(const r of Y.objectKeys(this.shape)){const a=this.shape[r];t&&!t[r]?n[r]=a:n[r]=a.optional()}return new ge({...this._def,shape:()=>n})}required(t){const n={};for(const r of Y.objectKeys(this.shape))if(t&&!t[r])n[r]=this.shape[r];else{let s=this.shape[r];for(;s instanceof Ye;)s=s._def.innerType;n[r]=s}return new ge({...this._def,shape:()=>n})}keyof(){return Go(Y.objectKeys(this.shape))}}ge.create=(e,t)=>new ge({shape:()=>e,unknownKeys:"strip",catchall:et.create(),typeName:S.ZodObject,...Z(t)});ge.strictCreate=(e,t)=>new ge({shape:()=>e,unknownKeys:"strict",catchall:et.create(),typeName:S.ZodObject,...Z(t)});ge.lazycreate=(e,t)=>new ge({shape:e,unknownKeys:"strip",catchall:et.create(),typeName:S.ZodObject,...Z(t)});class xn extends q{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;function a(s){for(const i of s)if(i.result.status==="valid")return i.result;for(const i of s)if(i.result.status==="dirty")return n.common.issues.push(...i.ctx.common.issues),i.result;const o=s.map(i=>new qe(i.ctx.common.issues));return T(n,{code:k.invalid_union,unionErrors:o}),U}if(n.common.async)return Promise.all(r.map(async s=>{const o={...n,common:{...n.common,issues:[]},parent:null};return{result:await s._parseAsync({data:n.data,path:n.path,parent:o}),ctx:o}})).then(a);{let s;const o=[];for(const l of r){const c={...n,common:{...n.common,issues:[]},parent:null},u=l._parseSync({data:n.data,path:n.path,parent:c});if(u.status==="valid")return u;u.status==="dirty"&&!s&&(s={result:u,ctx:c}),c.common.issues.length&&o.push(c.common.issues)}if(s)return n.common.issues.push(...s.ctx.common.issues),s.result;const i=o.map(l=>new qe(l));return T(n,{code:k.invalid_union,unionErrors:i}),U}}get options(){return this._def.options}}xn.create=(e,t)=>new xn({options:e,typeName:S.ZodUnion,...Z(t)});function Ir(e,t){const n=Ke(e),r=Ke(t);if(e===t)return{valid:!0,data:e};if(n===P.object&&r===P.object){const a=Y.objectKeys(t),s=Y.objectKeys(e).filter(i=>a.indexOf(i)!==-1),o={...e,...t};for(const i of s){const l=Ir(e[i],t[i]);if(!l.valid)return{valid:!1};o[i]=l.data}return{valid:!0,data:o}}else if(n===P.array&&r===P.array){if(e.length!==t.length)return{valid:!1};const a=[];for(let s=0;s<e.length;s++){const o=e[s],i=t[s],l=Ir(o,i);if(!l.valid)return{valid:!1};a.push(l.data)}return{valid:!0,data:a}}else return n===P.date&&r===P.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class kn extends q{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),a=(s,o)=>{if(Ka(s)||Ka(o))return U;const i=Ir(s.value,o.value);return i.valid?((Ya(s)||Ya(o))&&n.dirty(),{status:n.value,value:i.data}):(T(r,{code:k.invalid_intersection_types}),U)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([s,o])=>a(s,o)):a(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}kn.create=(e,t,n)=>new kn({left:e,right:t,typeName:S.ZodIntersection,...Z(n)});class it extends q{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==P.array)return T(r,{code:k.invalid_type,expected:P.array,received:r.parsedType}),U;if(r.data.length<this._def.items.length)return T(r,{code:k.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),U;!this._def.rest&&r.data.length>this._def.items.length&&(T(r,{code:k.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const s=[...r.data].map((o,i)=>{const l=this._def.items[i]||this._def.rest;return l?l._parse(new Ue(r,o,r.path,i)):null}).filter(o=>!!o);return r.common.async?Promise.all(s).then(o=>we.mergeArray(n,o)):we.mergeArray(n,s)}get items(){return this._def.items}rest(t){return new it({...this._def,rest:t})}}it.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new it({items:e,typeName:S.ZodTuple,rest:null,...Z(t)})};class Sn extends q{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==P.object)return T(r,{code:k.invalid_type,expected:P.object,received:r.parsedType}),U;const a=[],s=this._def.keyType,o=this._def.valueType;for(const i in r.data)a.push({key:s._parse(new Ue(r,i,r.path,i)),value:o._parse(new Ue(r,r.data[i],r.path,i)),alwaysSet:i in r.data});return r.common.async?we.mergeObjectAsync(n,a):we.mergeObjectSync(n,a)}get element(){return this._def.valueType}static create(t,n,r){return n instanceof q?new Sn({keyType:t,valueType:n,typeName:S.ZodRecord,...Z(r)}):new Sn({keyType:Ve.create(),valueType:t,typeName:S.ZodRecord,...Z(n)})}}class ts extends q{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==P.map)return T(r,{code:k.invalid_type,expected:P.map,received:r.parsedType}),U;const a=this._def.keyType,s=this._def.valueType,o=[...r.data.entries()].map(([i,l],c)=>({key:a._parse(new Ue(r,i,r.path,[c,"key"])),value:s._parse(new Ue(r,l,r.path,[c,"value"]))}));if(r.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const l of o){const c=await l.key,u=await l.value;if(c.status==="aborted"||u.status==="aborted")return U;(c.status==="dirty"||u.status==="dirty")&&n.dirty(),i.set(c.value,u.value)}return{status:n.value,value:i}})}else{const i=new Map;for(const l of o){const c=l.key,u=l.value;if(c.status==="aborted"||u.status==="aborted")return U;(c.status==="dirty"||u.status==="dirty")&&n.dirty(),i.set(c.value,u.value)}return{status:n.value,value:i}}}}ts.create=(e,t,n)=>new ts({valueType:t,keyType:e,typeName:S.ZodMap,...Z(n)});class Pt extends q{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==P.set)return T(r,{code:k.invalid_type,expected:P.set,received:r.parsedType}),U;const a=this._def;a.minSize!==null&&r.data.size<a.minSize.value&&(T(r,{code:k.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),n.dirty()),a.maxSize!==null&&r.data.size>a.maxSize.value&&(T(r,{code:k.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),n.dirty());const s=this._def.valueType;function o(l){const c=new Set;for(const u of l){if(u.status==="aborted")return U;u.status==="dirty"&&n.dirty(),c.add(u.value)}return{status:n.value,value:c}}const i=[...r.data.values()].map((l,c)=>s._parse(new Ue(r,l,r.path,c)));return r.common.async?Promise.all(i).then(l=>o(l)):o(i)}min(t,n){return new Pt({...this._def,minSize:{value:t,message:j.toString(n)}})}max(t,n){return new Pt({...this._def,maxSize:{value:t,message:j.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}Pt.create=(e,t)=>new Pt({valueType:e,minSize:null,maxSize:null,typeName:S.ZodSet,...Z(t)});class Rr extends q{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}Rr.create=(e,t)=>new Rr({getter:e,typeName:S.ZodLazy,...Z(t)});class Nr extends q{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return T(n,{received:n.data,code:k.invalid_literal,expected:this._def.value}),U}return{status:"valid",value:t.data}}get value(){return this._def.value}}Nr.create=(e,t)=>new Nr({value:e,typeName:S.ZodLiteral,...Z(t)});function Go(e,t){return new yt({values:e,typeName:S.ZodEnum,...Z(t)})}class yt extends q{_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return T(n,{expected:Y.joinValues(r),received:n.parsedType,code:k.invalid_type}),U}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return T(n,{received:n.data,code:k.invalid_enum_value,options:r}),U}return Ne(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return yt.create(t,{...this._def,...n})}exclude(t,n=this._def){return yt.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}yt.create=Go;class ns extends q{_parse(t){const n=Y.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==P.string&&r.parsedType!==P.number){const a=Y.objectValues(n);return T(r,{expected:Y.joinValues(a),received:r.parsedType,code:k.invalid_type}),U}if(this._cache||(this._cache=new Set(Y.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const a=Y.objectValues(n);return T(r,{received:r.data,code:k.invalid_enum_value,options:a}),U}return Ne(t.data)}get enum(){return this._def.values}}ns.create=(e,t)=>new ns({values:e,typeName:S.ZodNativeEnum,...Z(t)});class En extends q{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==P.promise&&n.common.async===!1)return T(n,{code:k.invalid_type,expected:P.promise,received:n.parsedType}),U;const r=n.parsedType===P.promise?n.data:Promise.resolve(n.data);return Ne(r.then(a=>this._def.type.parseAsync(a,{path:n.path,errorMap:n.common.contextualErrorMap})))}}En.create=(e,t)=>new En({type:e,typeName:S.ZodPromise,...Z(t)});class vt extends q{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===S.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),a=this._def.effect||null,s={addIssue:o=>{T(r,o),o.fatal?n.abort():n.dirty()},get path(){return r.path}};if(s.addIssue=s.addIssue.bind(s),a.type==="preprocess"){const o=a.transform(r.data,s);if(r.common.async)return Promise.resolve(o).then(async i=>{if(n.value==="aborted")return U;const l=await this._def.schema._parseAsync({data:i,path:r.path,parent:r});return l.status==="aborted"?U:l.status==="dirty"||n.value==="dirty"?At(l.value):l});{if(n.value==="aborted")return U;const i=this._def.schema._parseSync({data:o,path:r.path,parent:r});return i.status==="aborted"?U:i.status==="dirty"||n.value==="dirty"?At(i.value):i}}if(a.type==="refinement"){const o=i=>{const l=a.refinement(i,s);if(r.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return i.status==="aborted"?U:(i.status==="dirty"&&n.dirty(),o(i.value),{status:n.value,value:i.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>i.status==="aborted"?U:(i.status==="dirty"&&n.dirty(),o(i.value).then(()=>({status:n.value,value:i.value}))))}if(a.type==="transform")if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!gt(o))return U;const i=a.transform(o.value,s);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:i}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>gt(o)?Promise.resolve(a.transform(o.value,s)).then(i=>({status:n.value,value:i})):U);Y.assertNever(a)}}vt.create=(e,t,n)=>new vt({schema:e,typeName:S.ZodEffects,effect:t,...Z(n)});vt.createWithPreprocess=(e,t,n)=>new vt({schema:t,effect:{type:"preprocess",transform:e},typeName:S.ZodEffects,...Z(n)});class Ye extends q{_parse(t){return this._getType(t)===P.undefined?Ne(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Ye.create=(e,t)=>new Ye({innerType:e,typeName:S.ZodOptional,...Z(t)});class bt extends q{_parse(t){return this._getType(t)===P.null?Ne(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}bt.create=(e,t)=>new bt({innerType:e,typeName:S.ZodNullable,...Z(t)});class Pr extends q{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===P.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}Pr.create=(e,t)=>new Pr({innerType:e,typeName:S.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...Z(t)});class Or extends q{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return _n(a)?a.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new qe(r.common.issues)},input:r.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new qe(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}Or.create=(e,t)=>new Or({innerType:e,typeName:S.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...Z(t)});class rs extends q{_parse(t){if(this._getType(t)!==P.nan){const r=this._getOrReturnCtx(t);return T(r,{code:k.invalid_type,expected:P.nan,received:r.parsedType}),U}return{status:"valid",value:t.data}}}rs.create=e=>new rs({typeName:S.ZodNaN,...Z(e)});class Cp extends q{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class ma extends q{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?U:s.status==="dirty"?(n.dirty(),At(s.value)):this._def.out._parseAsync({data:s.value,path:r.path,parent:r})})();{const a=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?U:a.status==="dirty"?(n.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:r.path,parent:r})}}static create(t,n){return new ma({in:t,out:n,typeName:S.ZodPipeline})}}class jr extends q{_parse(t){const n=this._def.innerType._parse(t),r=a=>(gt(a)&&(a.value=Object.freeze(a.value)),a);return _n(n)?n.then(a=>r(a)):r(n)}unwrap(){return this._def.innerType}}jr.create=(e,t)=>new jr({innerType:e,typeName:S.ZodReadonly,...Z(t)});function as(e,t){const n=typeof e=="function"?e(t):typeof e=="string"?{message:e}:e;return typeof n=="string"?{message:n}:n}function Vo(e,t={},n){return e?Nt.create().superRefine((r,a)=>{const s=e(r);if(s instanceof Promise)return s.then(o=>{if(!o){const i=as(t,r),l=i.fatal??n??!0;a.addIssue({code:"custom",...i,fatal:l})}});if(!s){const o=as(t,r),i=o.fatal??n??!0;a.addIssue({code:"custom",...o,fatal:i})}}):Nt.create()}var S;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(S||(S={}));const An=(e,t={message:`Input not instance of ${e.name}`})=>Vo(n=>n instanceof e,t),R=Ve.create,V=ht.create,qo=Ar.create,Tp=Cr.create,Ip=Nt.create,ga=Tr.create;et.create;const ne=Be.create,M=ge.create,Se=xn.create;kn.create;it.create;const Ot=Sn.create,Rp=Rr.create,Ee=Nr.create,Ho=yt.create;En.create;Ye.create;bt.create;function Np({prompt:e,useLegacyFunctionCalling:t=!1}){const n=[];for(const{role:r,content:a}of e)switch(r){case"system":{n.push({role:"system",content:a});break}case"user":{if(a.length===1&&a[0].type==="text"){n.push({role:"user",content:a[0].text});break}n.push({role:"user",content:a.map(s=>{var o,i,l;switch(s.type){case"text":return{type:"text",text:s.text};case"image":return{type:"image_url",image_url:{url:s.image instanceof URL?s.image.toString():`data:${(o=s.mimeType)!=null?o:"image/jpeg"};base64,${bn(s.image)}`,detail:(l=(i=s.providerMetadata)==null?void 0:i.openai)==null?void 0:l.imageDetail}};case"file":{if(s.data instanceof URL)throw new fe({functionality:"'File content parts with URL data' functionality not supported."});switch(s.mimeType){case"audio/wav":return{type:"input_audio",input_audio:{data:s.data,format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:s.data,format:"mp3"}};default:throw new fe({functionality:`File content part type ${s.mimeType} in user messages`})}}}})});break}case"assistant":{let s="";const o=[];for(const i of a)switch(i.type){case"text":{s+=i.text;break}case"tool-call":{o.push({id:i.toolCallId,type:"function",function:{name:i.toolName,arguments:JSON.stringify(i.args)}});break}default:{const l=i;throw new Error(`Unsupported part: ${l}`)}}if(t){if(o.length>1)throw new fe({functionality:"useLegacyFunctionCalling with multiple tool calls in one message"});n.push({role:"assistant",content:s,function_call:o.length>0?o[0].function:void 0})}else n.push({role:"assistant",content:s,tool_calls:o.length>0?o:void 0});break}case"tool":{for(const s of a)t?n.push({role:"function",name:s.toolName,content:JSON.stringify(s.result)}):n.push({role:"tool",tool_call_id:s.toolCallId,content:JSON.stringify(s.result)});break}default:{const s=r;throw new Error(`Unsupported role: ${s}`)}}return n}function ss(e){var t,n;return(n=(t=e==null?void 0:e.content)==null?void 0:t.map(({token:r,logprob:a,top_logprobs:s})=>({token:r,logprob:a,topLogprobs:s?s.map(({token:o,logprob:i})=>({token:o,logprob:i})):[]})))!=null?n:void 0}function Cn(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var ha=M({error:M({message:R(),type:R().nullish(),param:Ip().nullish(),code:Se([R(),V()]).nullish()})}),jt=Bo({errorSchema:ha,errorToMessage:e=>e.error.message});function Tn({id:e,model:t,created:n}){return{id:e??void 0,modelId:t??void 0,timestamp:n!=null?new Date(n*1e3):void 0}}function Pp({mode:e,useLegacyFunctionCalling:t=!1,structuredOutputs:n=!1}){var r;const a=(r=e.tools)!=null&&r.length?e.tools:void 0,s=[];if(a==null)return{tools:void 0,tool_choice:void 0,toolWarnings:s};const o=e.toolChoice;if(t){const c=[];for(const d of a)d.type==="provider-defined"?s.push({type:"unsupported-tool",tool:d}):c.push({name:d.name,description:d.description,parameters:d.parameters});if(o==null)return{functions:c,function_call:void 0,toolWarnings:s};switch(o.type){case"auto":case"none":case void 0:return{functions:c,function_call:void 0,toolWarnings:s};case"required":throw new fe({functionality:"useLegacyFunctionCalling and toolChoice: required"});default:return{functions:c,function_call:{name:o.toolName},toolWarnings:s}}}const i=[];for(const c of a)c.type==="provider-defined"?s.push({type:"unsupported-tool",tool:c}):i.push({type:"function",function:{name:c.name,description:c.description,parameters:c.parameters,strict:n===!0?!0:void 0}});if(o==null)return{tools:i,tool_choice:void 0,toolWarnings:s};const l=o.type;switch(l){case"auto":case"none":case"required":return{tools:i,tool_choice:l,toolWarnings:s};case"tool":return{tools:i,tool_choice:{type:"function",function:{name:o.toolName}},toolWarnings:s};default:{const c=l;throw new fe({functionality:`Unsupported tool choice type: ${c}`})}}}var Op=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get supportsStructuredOutputs(){return this.settings.structuredOutputs===!0}get defaultObjectGenerationMode(){return Dp(this.modelId)?"tool":this.supportsStructuredOutputs?"json":"tool"}get provider(){return this.config.provider}get supportsImageUrls(){return!this.settings.downloadImages}getArgs({mode:e,prompt:t,maxTokens:n,temperature:r,topP:a,topK:s,frequencyPenalty:o,presencePenalty:i,stopSequences:l,responseFormat:c,seed:u,providerMetadata:d}){var g,v,f,m,h,b,_,w,A;const O=e.type,N=[];s!=null&&N.push({type:"unsupported-setting",setting:"topK"}),c!=null&&c.type==="json"&&c.schema!=null&&N.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is not supported"});const F=this.settings.useLegacyFunctionCalling;if(F&&this.settings.parallelToolCalls===!0)throw new fe({functionality:"useLegacyFunctionCalling with parallelToolCalls"});if(F&&this.settings.structuredOutputs===!0)throw new fe({functionality:"structuredOutputs with useLegacyFunctionCalling"});const D={model:this.modelId,logit_bias:this.settings.logitBias,logprobs:this.settings.logprobs===!0||typeof this.settings.logprobs=="number"?!0:void 0,top_logprobs:typeof this.settings.logprobs=="number"?this.settings.logprobs:typeof this.settings.logprobs=="boolean"&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:n,temperature:r,top_p:a,frequency_penalty:o,presence_penalty:i,stop:l,seed:u,max_completion_tokens:(v=(g=d==null?void 0:d.openai)==null?void 0:g.maxCompletionTokens)!=null?v:void 0,store:(m=(f=d==null?void 0:d.openai)==null?void 0:f.store)!=null?m:void 0,metadata:(b=(h=d==null?void 0:d.openai)==null?void 0:h.metadata)!=null?b:void 0,prediction:(w=(_=d==null?void 0:d.openai)==null?void 0:_.prediction)!=null?w:void 0,response_format:(c==null?void 0:c.type)==="json"?{type:"json_object"}:void 0,messages:Np({prompt:t,useLegacyFunctionCalling:F})};switch(os(this.modelId)&&(D.temperature=void 0,D.top_p=void 0,D.frequency_penalty=void 0,D.presence_penalty=void 0),O){case"regular":{const{tools:I,tool_choice:$,functions:G,function_call:J,toolWarnings:he}=Pp({mode:e,useLegacyFunctionCalling:F,structuredOutputs:this.settings.structuredOutputs});return{args:{...D,tools:I,tool_choice:$,functions:G,function_call:J},warnings:[...N,...he]}}case"object-json":return{args:{...D,response_format:this.settings.structuredOutputs===!0&&e.schema!=null?{type:"json_schema",json_schema:{schema:e.schema,strict:!0,name:(A=e.name)!=null?A:"response",description:e.description}}:{type:"json_object"}},warnings:N};case"object-tool":return{args:F?{...D,function_call:{name:e.tool.name},functions:[{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}]}:{...D,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:this.settings.structuredOutputs===!0?!0:void 0}}]},warnings:N};default:{const I=O;throw new Error(`Unsupported type: ${I}`)}}}async doGenerate(e){var t,n,r,a,s,o,i,l,c,u,d,g,v,f,m,h,b,_;const{args:w,warnings:A}=this.getArgs(e),{responseHeaders:O,value:N}=await Qe({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:Xe(this.config.headers(),e.headers),body:w,failedResponseHandler:jt,successfulResponseHandler:nn(jp),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:F,...D}=w,I=N.choices[0];let $;return(((n=(t=N.usage)==null?void 0:t.completion_tokens_details)==null?void 0:n.reasoning_tokens)!=null||((a=(r=N.usage)==null?void 0:r.prompt_tokens_details)==null?void 0:a.cached_tokens)!=null)&&($={openai:{}},((o=(s=N.usage)==null?void 0:s.completion_tokens_details)==null?void 0:o.reasoning_tokens)!=null&&($.openai.reasoningTokens=(l=(i=N.usage)==null?void 0:i.completion_tokens_details)==null?void 0:l.reasoning_tokens),((u=(c=N.usage)==null?void 0:c.prompt_tokens_details)==null?void 0:u.cached_tokens)!=null&&($.openai.cachedPromptTokens=(g=(d=N.usage)==null?void 0:d.prompt_tokens_details)==null?void 0:g.cached_tokens)),{text:(v=I.message.content)!=null?v:void 0,toolCalls:this.settings.useLegacyFunctionCalling&&I.message.function_call?[{toolCallType:"function",toolCallId:dt(),toolName:I.message.function_call.name,args:I.message.function_call.arguments}]:(f=I.message.tool_calls)==null?void 0:f.map(G=>{var J;return{toolCallType:"function",toolCallId:(J=G.id)!=null?J:dt(),toolName:G.function.name,args:G.function.arguments}}),finishReason:Cn(I.finish_reason),usage:{promptTokens:(h=(m=N.usage)==null?void 0:m.prompt_tokens)!=null?h:NaN,completionTokens:(_=(b=N.usage)==null?void 0:b.completion_tokens)!=null?_:NaN},rawCall:{rawPrompt:F,rawSettings:D},rawResponse:{headers:O},request:{body:JSON.stringify(w)},response:Tn(N),warnings:A,logprobs:ss(I.logprobs),providerMetadata:$}}async doStream(e){if(os(this.modelId)){const m=await this.doGenerate(e);return{stream:new ReadableStream({start(b){if(b.enqueue({type:"response-metadata",...m.response}),m.text&&b.enqueue({type:"text-delta",textDelta:m.text}),m.toolCalls)for(const _ of m.toolCalls)b.enqueue({type:"tool-call",..._});b.enqueue({type:"finish",finishReason:m.finishReason,usage:m.usage,logprobs:m.logprobs,providerMetadata:m.providerMetadata}),b.close()}}),rawCall:m.rawCall,rawResponse:m.rawResponse,warnings:m.warnings}}const{args:t,warnings:n}=this.getArgs(e),r={...t,stream:!0,stream_options:this.config.compatibility==="strict"?{include_usage:!0}:void 0},{responseHeaders:a,value:s}=await Qe({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:Xe(this.config.headers(),e.headers),body:r,failedResponseHandler:jt,successfulResponseHandler:fa(Mp),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:o,...i}=t,l=[];let c="unknown",u={promptTokens:void 0,completionTokens:void 0},d,g=!0;const{useLegacyFunctionCalling:v}=this.settings;let f;return{stream:s.pipeThrough(new TransformStream({transform(m,h){var b,_,w,A,O,N,F,D,I,$,G,J,he,X,ue,le;if(!m.success){c="error",h.enqueue({type:"error",error:m.error});return}const de=m.value;if("error"in de){c="error",h.enqueue({type:"error",error:de.error});return}g&&(g=!1,h.enqueue({type:"response-metadata",...Tn(de)})),de.usage!=null&&(u={promptTokens:(b=de.usage.prompt_tokens)!=null?b:void 0,completionTokens:(_=de.usage.completion_tokens)!=null?_:void 0},((w=de.usage.prompt_tokens_details)==null?void 0:w.cached_tokens)!=null&&(f={openai:{cachedPromptTokens:(A=de.usage.prompt_tokens_details)==null?void 0:A.cached_tokens}}));const E=de.choices[0];if((E==null?void 0:E.finish_reason)!=null&&(c=Cn(E.finish_reason)),(E==null?void 0:E.delta)==null)return;const C=E.delta;C.content!=null&&h.enqueue({type:"text-delta",textDelta:C.content});const L=ss(E==null?void 0:E.logprobs);L!=null&&L.length&&(d===void 0&&(d=[]),d.push(...L));const W=v&&C.function_call!=null?[{type:"function",id:dt(),function:C.function_call,index:0}]:C.tool_calls;if(W!=null)for(const B of W){const te=B.index;if(l[te]==null){if(B.type!=="function")throw new Yn({data:B,message:"Expected 'function' type."});if(B.id==null)throw new Yn({data:B,message:"Expected 'id' to be a string."});if(((O=B.function)==null?void 0:O.name)==null)throw new Yn({data:B,message:"Expected 'function.name' to be a string."});l[te]={id:B.id,type:"function",function:{name:B.function.name,arguments:(N=B.function.arguments)!=null?N:""}};const ae=l[te];((F=ae.function)==null?void 0:F.name)!=null&&((D=ae.function)==null?void 0:D.arguments)!=null&&(ae.function.arguments.length>0&&h.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:ae.id,toolName:ae.function.name,argsTextDelta:ae.function.arguments}),Ha(ae.function.arguments)&&h.enqueue({type:"tool-call",toolCallType:"function",toolCallId:(I=ae.id)!=null?I:dt(),toolName:ae.function.name,args:ae.function.arguments}));continue}const oe=l[te];(($=B.function)==null?void 0:$.arguments)!=null&&(oe.function.arguments+=(J=(G=B.function)==null?void 0:G.arguments)!=null?J:""),h.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:oe.id,toolName:oe.function.name,argsTextDelta:(he=B.function.arguments)!=null?he:""}),((X=oe.function)==null?void 0:X.name)!=null&&((ue=oe.function)==null?void 0:ue.arguments)!=null&&Ha(oe.function.arguments)&&h.enqueue({type:"tool-call",toolCallType:"function",toolCallId:(le=oe.id)!=null?le:dt(),toolName:oe.function.name,args:oe.function.arguments})}},flush(m){var h,b;m.enqueue({type:"finish",finishReason:c,logprobs:d,usage:{promptTokens:(h=u.promptTokens)!=null?h:NaN,completionTokens:(b=u.completionTokens)!=null?b:NaN},...f!=null?{providerMetadata:f}:{}})}})),rawCall:{rawPrompt:o,rawSettings:i},rawResponse:{headers:a},request:{body:JSON.stringify(r)},warnings:n}}},Wo=M({prompt_tokens:V().nullish(),completion_tokens:V().nullish(),prompt_tokens_details:M({cached_tokens:V().nullish()}).nullish(),completion_tokens_details:M({reasoning_tokens:V().nullish()}).nullish()}).nullish(),jp=M({id:R().nullish(),created:V().nullish(),model:R().nullish(),choices:ne(M({message:M({role:Ee("assistant").nullish(),content:R().nullish(),function_call:M({arguments:R(),name:R()}).nullish(),tool_calls:ne(M({id:R().nullish(),type:Ee("function"),function:M({name:R(),arguments:R()})})).nullish()}),index:V(),logprobs:M({content:ne(M({token:R(),logprob:V(),top_logprobs:ne(M({token:R(),logprob:V()}))})).nullable()}).nullish(),finish_reason:R().nullish()})),usage:Wo}),Mp=Se([M({id:R().nullish(),created:V().nullish(),model:R().nullish(),choices:ne(M({delta:M({role:Ho(["assistant"]).nullish(),content:R().nullish(),function_call:M({name:R().optional(),arguments:R().optional()}).nullish(),tool_calls:ne(M({index:V(),id:R().nullish(),type:Ee("function").optional(),function:M({name:R().nullish(),arguments:R().nullish()})})).nullish()}).nullish(),logprobs:M({content:ne(M({token:R(),logprob:V(),top_logprobs:ne(M({token:R(),logprob:V()}))})).nullable()}).nullish(),finish_reason:R().nullable().optional(),index:V()})),usage:Wo}),ha]);function os(e){return e.startsWith("o1-")}function Dp(e){return e.startsWith("gpt-4o-audio-preview")}function Fp({prompt:e,inputFormat:t,user:n="user",assistant:r="assistant"}){if(t==="prompt"&&e.length===1&&e[0].role==="user"&&e[0].content.length===1&&e[0].content[0].type==="text")return{prompt:e[0].content[0].text};let a="";e[0].role==="system"&&(a+=`${e[0].content}

`,e=e.slice(1));for(const{role:s,content:o}of e)switch(s){case"system":throw new rt({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":{const i=o.map(l=>{switch(l.type){case"text":return l.text;case"image":throw new fe({functionality:"images"})}}).join("");a+=`${n}:
${i}

`;break}case"assistant":{const i=o.map(l=>{switch(l.type){case"text":return l.text;case"tool-call":throw new fe({functionality:"tool-call messages"})}}).join("");a+=`${r}:
${i}

`;break}case"tool":throw new fe({functionality:"tool messages"});default:{const i=s;throw new Error(`Unsupported role: ${i}`)}}return a+=`${r}:
`,{prompt:a,stopSequences:[`
${n}:`]}}function is(e){return e==null?void 0:e.tokens.map((t,n)=>({token:t,logprob:e.token_logprobs[n],topLogprobs:e.top_logprobs?Object.entries(e.top_logprobs[n]).map(([r,a])=>({token:r,logprob:a})):[]}))}var $p=class{constructor(e,t,n){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:n,maxTokens:r,temperature:a,topP:s,topK:o,frequencyPenalty:i,presencePenalty:l,stopSequences:c,responseFormat:u,seed:d}){var g;const v=e.type,f=[];o!=null&&f.push({type:"unsupported-setting",setting:"topK"}),u!=null&&u.type!=="text"&&f.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});const{prompt:m,stopSequences:h}=Fp({prompt:n,inputFormat:t}),b=[...h??[],...c??[]],_={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:typeof this.settings.logprobs=="number"?this.settings.logprobs:typeof this.settings.logprobs=="boolean"&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:r,temperature:a,top_p:s,frequency_penalty:i,presence_penalty:l,seed:d,prompt:m,stop:b.length>0?b:void 0};switch(v){case"regular":{if((g=e.tools)!=null&&g.length)throw new fe({functionality:"tools"});if(e.toolChoice)throw new fe({functionality:"toolChoice"});return{args:_,warnings:f}}case"object-json":throw new fe({functionality:"object-json mode"});case"object-tool":throw new fe({functionality:"object-tool mode"});default:{const w=v;throw new Error(`Unsupported type: ${w}`)}}}async doGenerate(e){const{args:t,warnings:n}=this.getArgs(e),{responseHeaders:r,value:a}=await Qe({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:Xe(this.config.headers(),e.headers),body:t,failedResponseHandler:jt,successfulResponseHandler:nn(Lp),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:s,...o}=t,i=a.choices[0];return{text:i.text,usage:{promptTokens:a.usage.prompt_tokens,completionTokens:a.usage.completion_tokens},finishReason:Cn(i.finish_reason),logprobs:is(i.logprobs),rawCall:{rawPrompt:s,rawSettings:o},rawResponse:{headers:r},response:Tn(a),warnings:n,request:{body:JSON.stringify(t)}}}async doStream(e){const{args:t,warnings:n}=this.getArgs(e),r={...t,stream:!0,stream_options:this.config.compatibility==="strict"?{include_usage:!0}:void 0},{responseHeaders:a,value:s}=await Qe({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:Xe(this.config.headers(),e.headers),body:r,failedResponseHandler:jt,successfulResponseHandler:fa(Bp),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:o,...i}=t;let l="unknown",c={promptTokens:Number.NaN,completionTokens:Number.NaN},u,d=!0;return{stream:s.pipeThrough(new TransformStream({transform(g,v){if(!g.success){l="error",v.enqueue({type:"error",error:g.error});return}const f=g.value;if("error"in f){l="error",v.enqueue({type:"error",error:f.error});return}d&&(d=!1,v.enqueue({type:"response-metadata",...Tn(f)})),f.usage!=null&&(c={promptTokens:f.usage.prompt_tokens,completionTokens:f.usage.completion_tokens});const m=f.choices[0];(m==null?void 0:m.finish_reason)!=null&&(l=Cn(m.finish_reason)),(m==null?void 0:m.text)!=null&&v.enqueue({type:"text-delta",textDelta:m.text});const h=is(m==null?void 0:m.logprobs);h!=null&&h.length&&(u===void 0&&(u=[]),u.push(...h))},flush(g){g.enqueue({type:"finish",finishReason:l,logprobs:u,usage:c})}})),rawCall:{rawPrompt:o,rawSettings:i},rawResponse:{headers:a},warnings:n,request:{body:JSON.stringify(r)}}}},Lp=M({id:R().nullish(),created:V().nullish(),model:R().nullish(),choices:ne(M({text:R(),finish_reason:R(),logprobs:M({tokens:ne(R()),token_logprobs:ne(V()),top_logprobs:ne(Ot(R(),V())).nullable()}).nullish()})),usage:M({prompt_tokens:V(),completion_tokens:V()})}),Bp=Se([M({id:R().nullish(),created:V().nullish(),model:R().nullish(),choices:ne(M({text:R(),finish_reason:R().nullish(),index:V(),logprobs:M({tokens:ne(R()),token_logprobs:ne(V()),top_logprobs:ne(Ot(R(),V())).nullable()}).nullish()})),usage:M({prompt_tokens:V(),completion_tokens:V()}).nullish()}),ha]),Up=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return(e=this.settings.maxEmbeddingsPerCall)!=null?e:2048}get supportsParallelCalls(){var e;return(e=this.settings.supportsParallelCalls)!=null?e:!0}async doEmbed({values:e,headers:t,abortSignal:n}){if(e.length>this.maxEmbeddingsPerCall)throw new No({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});const{responseHeaders:r,value:a}=await Qe({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:Xe(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:"float",dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:jt,successfulResponseHandler:nn(zp),abortSignal:n,fetch:this.config.fetch});return{embeddings:a.data.map(s=>s.embedding),usage:a.usage?{tokens:a.usage.prompt_tokens}:void 0,rawResponse:{headers:r}}}},zp=M({data:ne(M({embedding:ne(V())})),usage:M({prompt_tokens:V()}).nullish()});function ya(e={}){var t,n,r,a;const s=(n=Uo((t=e.baseURL)!=null?t:e.baseUrl))!=null?n:"https://api.openai.com/v1",o=(r=e.compatibility)!=null?r:"compatible",i=(a=e.name)!=null?a:"openai",l=()=>({Authorization:`Bearer ${Lo({apiKey:e.apiKey,environmentVariableName:"OPENAI_API_KEY",description:"OpenAI"})}`,"OpenAI-Organization":e.organization,"OpenAI-Project":e.project,...e.headers}),c=(f,m={})=>new Op(f,m,{provider:`${i}.chat`,url:({path:h})=>`${s}${h}`,headers:l,compatibility:o,fetch:e.fetch}),u=(f,m={})=>new $p(f,m,{provider:`${i}.completion`,url:({path:h})=>`${s}${h}`,headers:l,compatibility:o,fetch:e.fetch}),d=(f,m={})=>new Up(f,m,{provider:`${i}.embedding`,url:({path:h})=>`${s}${h}`,headers:l,fetch:e.fetch}),g=(f,m)=>{if(new.target)throw new Error("The OpenAI model function cannot be called with the new keyword.");return f==="gpt-3.5-turbo-instruct"?u(f,m):c(f,m)},v=function(f,m){return g(f,m)};return v.languageModel=g,v.chat=c,v.completion=u,v.embedding=d,v.textEmbedding=d,v.textEmbeddingModel=d,v}ya({compatibility:"strict"});const Zp=["c","cpp","csharp","cs","dart","elixir","erlang","go","java","javascript","jsonp","jsx","php","python","racket","rkt","ruby","rb","rust","scala","sql","Swift","typescript","tsx"],Gp=M({feedback:R(),hints:ne(R()).max(2,"You can only provide up to 2 hints.").optional().describe("max 2 hints"),snippet:R().optional().describe("code snippet should be in format."),programmingLanguage:Ho(Zp).optional().describe("Programming language code as supports by prismjs")}),Vp=Symbol("Let zodToJsonSchema decide on which parser to use"),qp={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"},Hp=e=>({...qp,...e}),Wp=e=>{const t=Hp(e),n=t.name!==void 0?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,flags:{hasReferencedOpenAiAnyType:!1},currentPath:n,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([r,a])=>[a._def,{def:a._def,path:[...t.basePath,t.definitionPath,r],jsonSchema:void 0}]))}};function Ko(e,t,n,r){r!=null&&r.errorMessages&&n&&(e.errorMessage={...e.errorMessage,[t]:n})}function re(e,t,n,r,a){e[t]=n,Ko(e,t,r,a)}const Yo=(e,t)=>{let n=0;for(;n<e.length&&n<t.length&&e[n]===t[n];n++);return[(e.length-n).toString(),...t.slice(n)].join("/")};function Ae(e){if(e.target!=="openAi")return{};const t=[...e.basePath,e.definitionPath,e.openAiAnyTypeName];return e.flags.hasReferencedOpenAiAnyType=!0,{$ref:e.$refStrategy==="relative"?Yo(t,e.currentPath):t.join("/")}}function Kp(e,t){var r,a,s;const n={type:"array"};return(r=e.type)!=null&&r._def&&((s=(a=e.type)==null?void 0:a._def)==null?void 0:s.typeName)!==S.ZodAny&&(n.items=ee(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&re(n,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&re(n,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(re(n,"minItems",e.exactLength.value,e.exactLength.message,t),re(n,"maxItems",e.exactLength.value,e.exactLength.message,t)),n}function Yp(e,t){const n={type:"integer",format:"int64"};if(!e.checks)return n;for(const r of e.checks)switch(r.kind){case"min":t.target==="jsonSchema7"?r.inclusive?re(n,"minimum",r.value,r.message,t):re(n,"exclusiveMinimum",r.value,r.message,t):(r.inclusive||(n.exclusiveMinimum=!0),re(n,"minimum",r.value,r.message,t));break;case"max":t.target==="jsonSchema7"?r.inclusive?re(n,"maximum",r.value,r.message,t):re(n,"exclusiveMaximum",r.value,r.message,t):(r.inclusive||(n.exclusiveMaximum=!0),re(n,"maximum",r.value,r.message,t));break;case"multipleOf":re(n,"multipleOf",r.value,r.message,t);break}return n}function Jp(){return{type:"boolean"}}function Jo(e,t){return ee(e.type._def,t)}const Xp=(e,t)=>ee(e.innerType._def,t);function Xo(e,t,n){const r=n??t.dateStrategy;if(Array.isArray(r))return{anyOf:r.map((a,s)=>Xo(e,t,a))};switch(r){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return Qp(e,t)}}const Qp=(e,t)=>{const n={type:"integer",format:"unix-time"};if(t.target==="openApi3")return n;for(const r of e.checks)switch(r.kind){case"min":re(n,"minimum",r.value,r.message,t);break;case"max":re(n,"maximum",r.value,r.message,t);break}return n};function ef(e,t){return{...ee(e.innerType._def,t),default:e.defaultValue()}}function tf(e,t){return t.effectStrategy==="input"?ee(e.schema._def,t):Ae(t)}function nf(e){return{type:"string",enum:Array.from(e.values)}}const rf=e=>"type"in e&&e.type==="string"?!1:"allOf"in e;function af(e,t){const n=[ee(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),ee(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(s=>!!s);let r=t.target==="jsonSchema2019-09"?{unevaluatedProperties:!1}:void 0;const a=[];return n.forEach(s=>{if(rf(s))a.push(...s.allOf),s.unevaluatedProperties===void 0&&(r=void 0);else{let o=s;if("additionalProperties"in s&&s.additionalProperties===!1){const{additionalProperties:i,...l}=s;o=l}else r=void 0;a.push(o)}}),a.length?{allOf:a,...r}:void 0}function sf(e,t){const n=typeof e.value;return n!=="bigint"&&n!=="number"&&n!=="boolean"&&n!=="string"?{type:Array.isArray(e.value)?"array":"object"}:t.target==="openApi3"?{type:n==="bigint"?"integer":n,enum:[e.value]}:{type:n==="bigint"?"integer":n,const:e.value}}let Xn;const Oe={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(Xn===void 0&&(Xn=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Xn),uuid:/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,ipv4:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6:/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function Qo(e,t){const n={type:"string"};if(e.checks)for(const r of e.checks)switch(r.kind){case"min":re(n,"minLength",typeof n.minLength=="number"?Math.max(n.minLength,r.value):r.value,r.message,t);break;case"max":re(n,"maxLength",typeof n.maxLength=="number"?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case"email":switch(t.emailStrategy){case"format:email":je(n,"email",r.message,t);break;case"format:idn-email":je(n,"idn-email",r.message,t);break;case"pattern:zod":_e(n,Oe.email,r.message,t);break}break;case"url":je(n,"uri",r.message,t);break;case"uuid":je(n,"uuid",r.message,t);break;case"regex":_e(n,r.regex,r.message,t);break;case"cuid":_e(n,Oe.cuid,r.message,t);break;case"cuid2":_e(n,Oe.cuid2,r.message,t);break;case"startsWith":_e(n,RegExp(`^${Qn(r.value,t)}`),r.message,t);break;case"endsWith":_e(n,RegExp(`${Qn(r.value,t)}$`),r.message,t);break;case"datetime":je(n,"date-time",r.message,t);break;case"date":je(n,"date",r.message,t);break;case"time":je(n,"time",r.message,t);break;case"duration":je(n,"duration",r.message,t);break;case"length":re(n,"minLength",typeof n.minLength=="number"?Math.max(n.minLength,r.value):r.value,r.message,t),re(n,"maxLength",typeof n.maxLength=="number"?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case"includes":{_e(n,RegExp(Qn(r.value,t)),r.message,t);break}case"ip":{r.version!=="v6"&&je(n,"ipv4",r.message,t),r.version!=="v4"&&je(n,"ipv6",r.message,t);break}case"base64url":_e(n,Oe.base64url,r.message,t);break;case"jwt":_e(n,Oe.jwt,r.message,t);break;case"cidr":{r.version!=="v6"&&_e(n,Oe.ipv4Cidr,r.message,t),r.version!=="v4"&&_e(n,Oe.ipv6Cidr,r.message,t);break}case"emoji":_e(n,Oe.emoji(),r.message,t);break;case"ulid":{_e(n,Oe.ulid,r.message,t);break}case"base64":{switch(t.base64Strategy){case"format:binary":{je(n,"binary",r.message,t);break}case"contentEncoding:base64":{re(n,"contentEncoding","base64",r.message,t);break}case"pattern:zod":{_e(n,Oe.base64,r.message,t);break}}break}case"nanoid":_e(n,Oe.nanoid,r.message,t)}return n}function Qn(e,t){return t.patternStrategy==="escape"?lf(e):e}const of=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function lf(e){let t="";for(let n=0;n<e.length;n++)of.has(e[n])||(t+="\\"),t+=e[n];return t}function je(e,t,n,r){var a;e.format||(a=e.anyOf)!=null&&a.some(s=>s.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&r.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.anyOf.push({format:t,...n&&r.errorMessages&&{errorMessage:{format:n}}})):re(e,"format",t,n,r)}function _e(e,t,n,r){var a;e.pattern||(a=e.allOf)!=null&&a.some(s=>s.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&r.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.allOf.push({pattern:ls(t,r),...n&&r.errorMessages&&{errorMessage:{pattern:n}}})):re(e,"pattern",ls(t,r),n,r)}function ls(e,t){var l;if(!t.applyRegexFlags||!e.flags)return e.source;const n={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},r=n.i?e.source.toLowerCase():e.source;let a="",s=!1,o=!1,i=!1;for(let c=0;c<r.length;c++){if(s){a+=r[c],s=!1;continue}if(n.i){if(o){if(r[c].match(/[a-z]/)){i?(a+=r[c],a+=`${r[c-2]}-${r[c]}`.toUpperCase(),i=!1):r[c+1]==="-"&&((l=r[c+2])!=null&&l.match(/[a-z]/))?(a+=r[c],i=!0):a+=`${r[c]}${r[c].toUpperCase()}`;continue}}else if(r[c].match(/[a-z]/)){a+=`[${r[c]}${r[c].toUpperCase()}]`;continue}}if(n.m){if(r[c]==="^"){a+=`(^|(?<=[\r
]))`;continue}else if(r[c]==="$"){a+=`($|(?=[\r
]))`;continue}}if(n.s&&r[c]==="."){a+=o?`${r[c]}\r
`:`[${r[c]}\r
]`;continue}a+=r[c],r[c]==="\\"?s=!0:o&&r[c]==="]"?o=!1:!o&&r[c]==="["&&(o=!0)}try{new RegExp(a)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return a}function ei(e,t){var r,a,s,o,i,l;if(t.target==="openAi"&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),t.target==="openApi3"&&((r=e.keyType)==null?void 0:r._def.typeName)===S.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((c,u)=>({...c,[u]:ee(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",u]})??Ae(t)}),{}),additionalProperties:t.rejectedAdditionalProperties};const n={type:"object",additionalProperties:ee(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if(t.target==="openApi3")return n;if(((a=e.keyType)==null?void 0:a._def.typeName)===S.ZodString&&((s=e.keyType._def.checks)!=null&&s.length)){const{type:c,...u}=Qo(e.keyType._def,t);return{...n,propertyNames:u}}else{if(((o=e.keyType)==null?void 0:o._def.typeName)===S.ZodEnum)return{...n,propertyNames:{enum:e.keyType._def.values}};if(((i=e.keyType)==null?void 0:i._def.typeName)===S.ZodBranded&&e.keyType._def.type._def.typeName===S.ZodString&&((l=e.keyType._def.type._def.checks)!=null&&l.length)){const{type:c,...u}=Jo(e.keyType._def,t);return{...n,propertyNames:u}}}return n}function cf(e,t){if(t.mapStrategy==="record")return ei(e,t);const n=ee(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||Ae(t),r=ee(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||Ae(t);return{type:"array",maxItems:125,items:{type:"array",items:[n,r],minItems:2,maxItems:2}}}function uf(e){const t=e.values,r=Object.keys(e.values).filter(s=>typeof t[t[s]]!="number").map(s=>t[s]),a=Array.from(new Set(r.map(s=>typeof s)));return{type:a.length===1?a[0]==="string"?"string":"number":["string","number"],enum:r}}function df(e){return e.target==="openAi"?void 0:{not:Ae({...e,currentPath:[...e.currentPath,"not"]})}}function pf(e){return e.target==="openApi3"?{enum:["null"],nullable:!0}:{type:"null"}}const In={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};function ff(e,t){if(t.target==="openApi3")return cs(e,t);const n=e.options instanceof Map?Array.from(e.options.values()):e.options;if(n.every(r=>r._def.typeName in In&&(!r._def.checks||!r._def.checks.length))){const r=n.reduce((a,s)=>{const o=In[s._def.typeName];return o&&!a.includes(o)?[...a,o]:a},[]);return{type:r.length>1?r:r[0]}}else if(n.every(r=>r._def.typeName==="ZodLiteral"&&!r.description)){const r=n.reduce((a,s)=>{const o=typeof s._def.value;switch(o){case"string":case"number":case"boolean":return[...a,o];case"bigint":return[...a,"integer"];case"object":if(s._def.value===null)return[...a,"null"];case"symbol":case"undefined":case"function":default:return a}},[]);if(r.length===n.length){const a=r.filter((s,o,i)=>i.indexOf(s)===o);return{type:a.length>1?a:a[0],enum:n.reduce((s,o)=>s.includes(o._def.value)?s:[...s,o._def.value],[])}}}else if(n.every(r=>r._def.typeName==="ZodEnum"))return{type:"string",enum:n.reduce((r,a)=>[...r,...a._def.values.filter(s=>!r.includes(s))],[])};return cs(e,t)}const cs=(e,t)=>{const n=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((r,a)=>ee(r._def,{...t,currentPath:[...t.currentPath,"anyOf",`${a}`]})).filter(r=>!!r&&(!t.strictUnions||typeof r=="object"&&Object.keys(r).length>0));return n.length?{anyOf:n}:void 0};function mf(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return t.target==="openApi3"?{type:In[e.innerType._def.typeName],nullable:!0}:{type:[In[e.innerType._def.typeName],"null"]};if(t.target==="openApi3"){const r=ee(e.innerType._def,{...t,currentPath:[...t.currentPath]});return r&&"$ref"in r?{allOf:[r],nullable:!0}:r&&{...r,nullable:!0}}const n=ee(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return n&&{anyOf:[n,{type:"null"}]}}function gf(e,t){const n={type:"number"};if(!e.checks)return n;for(const r of e.checks)switch(r.kind){case"int":n.type="integer",Ko(n,"type",r.message,t);break;case"min":t.target==="jsonSchema7"?r.inclusive?re(n,"minimum",r.value,r.message,t):re(n,"exclusiveMinimum",r.value,r.message,t):(r.inclusive||(n.exclusiveMinimum=!0),re(n,"minimum",r.value,r.message,t));break;case"max":t.target==="jsonSchema7"?r.inclusive?re(n,"maximum",r.value,r.message,t):re(n,"exclusiveMaximum",r.value,r.message,t):(r.inclusive||(n.exclusiveMaximum=!0),re(n,"maximum",r.value,r.message,t));break;case"multipleOf":re(n,"multipleOf",r.value,r.message,t);break}return n}function hf(e,t){const n=t.target==="openAi",r={type:"object",properties:{}},a=[],s=e.shape();for(const i in s){let l=s[i];if(l===void 0||l._def===void 0)continue;let c=vf(l);c&&n&&(l._def.typeName==="ZodOptional"&&(l=l._def.innerType),l.isNullable()||(l=l.nullable()),c=!1);const u=ee(l._def,{...t,currentPath:[...t.currentPath,"properties",i],propertyPath:[...t.currentPath,"properties",i]});u!==void 0&&(r.properties[i]=u,c||a.push(i))}a.length&&(r.required=a);const o=yf(e,t);return o!==void 0&&(r.additionalProperties=o),r}function yf(e,t){if(e.catchall._def.typeName!=="ZodNever")return ee(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return t.removeAdditionalStrategy==="strict"?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}function vf(e){try{return e.isOptional()}catch{return!0}}const bf=(e,t)=>{var r;if(t.currentPath.toString()===((r=t.propertyPath)==null?void 0:r.toString()))return ee(e.innerType._def,t);const n=ee(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return n?{anyOf:[{not:Ae(t)},n]}:Ae(t)},_f=(e,t)=>{if(t.pipeStrategy==="input")return ee(e.in._def,t);if(t.pipeStrategy==="output")return ee(e.out._def,t);const n=ee(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),r=ee(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",n?"1":"0"]});return{allOf:[n,r].filter(a=>a!==void 0)}};function wf(e,t){return ee(e.type._def,t)}function xf(e,t){const r={type:"array",uniqueItems:!0,items:ee(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&re(r,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&re(r,"maxItems",e.maxSize.value,e.maxSize.message,t),r}function kf(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((n,r)=>ee(n._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((n,r)=>r===void 0?n:[...n,r],[]),additionalItems:ee(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((n,r)=>ee(n._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((n,r)=>r===void 0?n:[...n,r],[])}}function Sf(e){return{not:Ae(e)}}function Ef(e){return Ae(e)}const Af=(e,t)=>ee(e.innerType._def,t),Cf=(e,t,n)=>{switch(t){case S.ZodString:return Qo(e,n);case S.ZodNumber:return gf(e,n);case S.ZodObject:return hf(e,n);case S.ZodBigInt:return Yp(e,n);case S.ZodBoolean:return Jp();case S.ZodDate:return Xo(e,n);case S.ZodUndefined:return Sf(n);case S.ZodNull:return pf(n);case S.ZodArray:return Kp(e,n);case S.ZodUnion:case S.ZodDiscriminatedUnion:return ff(e,n);case S.ZodIntersection:return af(e,n);case S.ZodTuple:return kf(e,n);case S.ZodRecord:return ei(e,n);case S.ZodLiteral:return sf(e,n);case S.ZodEnum:return nf(e);case S.ZodNativeEnum:return uf(e);case S.ZodNullable:return mf(e,n);case S.ZodOptional:return bf(e,n);case S.ZodMap:return cf(e,n);case S.ZodSet:return xf(e,n);case S.ZodLazy:return()=>e.getter()._def;case S.ZodPromise:return wf(e,n);case S.ZodNaN:case S.ZodNever:return df(n);case S.ZodEffects:return tf(e,n);case S.ZodAny:return Ae(n);case S.ZodUnknown:return Ef(n);case S.ZodDefault:return ef(e,n);case S.ZodBranded:return Jo(e,n);case S.ZodReadonly:return Af(e,n);case S.ZodCatch:return Xp(e,n);case S.ZodPipeline:return _f(e,n);case S.ZodFunction:case S.ZodVoid:case S.ZodSymbol:return;default:return(r=>{})()}};function ee(e,t,n=!1){var i;const r=t.seen.get(e);if(t.override){const l=(i=t.override)==null?void 0:i.call(t,e,t,r,n);if(l!==Vp)return l}if(r&&!n){const l=Tf(r,t);if(l!==void 0)return l}const a={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,a);const s=Cf(e,e.typeName,t),o=typeof s=="function"?ee(s(),t):s;if(o&&If(e,t,o),t.postProcess){const l=t.postProcess(o,e,t);return a.jsonSchema=o,l}return a.jsonSchema=o,o}const Tf=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:Yo(t.currentPath,e.path)};case"none":case"seen":return e.path.length<t.currentPath.length&&e.path.every((n,r)=>t.currentPath[r]===n)?(console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),Ae(t)):t.$refStrategy==="seen"?Ae(t):void 0}},If=(e,t,n)=>(e.description&&(n.description=e.description,t.markdownDescription&&(n.markdownDescription=e.description)),n),Rf=(e,t)=>{const n=Wp(t);let r=typeof t=="object"&&t.definitions?Object.entries(t.definitions).reduce((i,[l,c])=>({...i,[l]:ee(c._def,{...n,currentPath:[...n.basePath,n.definitionPath,l]},!0)??Ae(n)}),{}):void 0;const a=typeof t=="string"?t:t==null?void 0:t.name,s=ee(e._def,n,!1)??Ae(n);n.flags.hasReferencedOpenAiAnyType&&(r||(r={}),r[n.openAiAnyTypeName]||(r[n.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:n.$refStrategy==="relative"?"1":[...n.basePath,n.definitionPath,n.openAiAnyTypeName].join("/")}}));const o=a===void 0?r?{...s,[n.definitionPath]:r}:s:{$ref:[...n.$refStrategy==="relative"?[]:n.basePath,n.definitionPath,a].join("/"),[n.definitionPath]:{...r,[a]:s}};return n.target==="jsonSchema7"?o.$schema="http://json-schema.org/draft-07/schema#":(n.target==="jsonSchema2019-09"||n.target==="openAi")&&(o.$schema="https://json-schema.org/draft/2019-09/schema#"),n.target==="openAi"&&("anyOf"in o||"oneOf"in o||"allOf"in o||"type"in o&&Array.isArray(o.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),o};var Mt={code:"0",name:"text",parse:e=>{if(typeof e!="string")throw new Error('"text" parts expect a string value.');return{type:"text",value:e}}},Dt={code:"1",name:"function_call",parse:e=>{if(e==null||typeof e!="object"||!("function_call"in e)||typeof e.function_call!="object"||e.function_call==null||!("name"in e.function_call)||!("arguments"in e.function_call)||typeof e.function_call.name!="string"||typeof e.function_call.arguments!="string")throw new Error('"function_call" parts expect an object with a "function_call" property.');return{type:"function_call",value:e}}},Ft={code:"2",name:"data",parse:e=>{if(!Array.isArray(e))throw new Error('"data" parts expect an array value.');return{type:"data",value:e}}},$t={code:"3",name:"error",parse:e=>{if(typeof e!="string")throw new Error('"error" parts expect a string value.');return{type:"error",value:e}}},Lt={code:"4",name:"assistant_message",parse:e=>{if(e==null||typeof e!="object"||!("id"in e)||!("role"in e)||!("content"in e)||typeof e.id!="string"||typeof e.role!="string"||e.role!=="assistant"||!Array.isArray(e.content)||!e.content.every(t=>t!=null&&typeof t=="object"&&"type"in t&&t.type==="text"&&"text"in t&&t.text!=null&&typeof t.text=="object"&&"value"in t.text&&typeof t.text.value=="string"))throw new Error('"assistant_message" parts expect an object with an "id", "role", and "content" property.');return{type:"assistant_message",value:e}}},Bt={code:"5",name:"assistant_control_data",parse:e=>{if(e==null||typeof e!="object"||!("threadId"in e)||!("messageId"in e)||typeof e.threadId!="string"||typeof e.messageId!="string")throw new Error('"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.');return{type:"assistant_control_data",value:{threadId:e.threadId,messageId:e.messageId}}}},Ut={code:"6",name:"data_message",parse:e=>{if(e==null||typeof e!="object"||!("role"in e)||!("data"in e)||typeof e.role!="string"||e.role!=="data")throw new Error('"data_message" parts expect an object with a "role" and "data" property.');return{type:"data_message",value:e}}},zt={code:"7",name:"tool_calls",parse:e=>{if(e==null||typeof e!="object"||!("tool_calls"in e)||typeof e.tool_calls!="object"||e.tool_calls==null||!Array.isArray(e.tool_calls)||e.tool_calls.some(t=>t==null||typeof t!="object"||!("id"in t)||typeof t.id!="string"||!("type"in t)||typeof t.type!="string"||!("function"in t)||t.function==null||typeof t.function!="object"||!("arguments"in t.function)||typeof t.function.name!="string"||typeof t.function.arguments!="string"))throw new Error('"tool_calls" parts expect an object with a ToolCallPayload.');return{type:"tool_calls",value:e}}},Zt={code:"8",name:"message_annotations",parse:e=>{if(!Array.isArray(e))throw new Error('"message_annotations" parts expect an array value.');return{type:"message_annotations",value:e}}},Gt={code:"9",name:"tool_call",parse:e=>{if(e==null||typeof e!="object"||!("toolCallId"in e)||typeof e.toolCallId!="string"||!("toolName"in e)||typeof e.toolName!="string"||!("args"in e)||typeof e.args!="object")throw new Error('"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.');return{type:"tool_call",value:e}}},Vt={code:"a",name:"tool_result",parse:e=>{if(e==null||typeof e!="object"||!("toolCallId"in e)||typeof e.toolCallId!="string"||!("result"in e))throw new Error('"tool_result" parts expect an object with a "toolCallId" and a "result" property.');return{type:"tool_result",value:e}}},qt={code:"b",name:"tool_call_streaming_start",parse:e=>{if(e==null||typeof e!="object"||!("toolCallId"in e)||typeof e.toolCallId!="string"||!("toolName"in e)||typeof e.toolName!="string")throw new Error('"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.');return{type:"tool_call_streaming_start",value:e}}},Ht={code:"c",name:"tool_call_delta",parse:e=>{if(e==null||typeof e!="object"||!("toolCallId"in e)||typeof e.toolCallId!="string"||!("argsTextDelta"in e)||typeof e.argsTextDelta!="string")throw new Error('"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.');return{type:"tool_call_delta",value:e}}},Wt={code:"d",name:"finish_message",parse:e=>{if(e==null||typeof e!="object"||!("finishReason"in e)||typeof e.finishReason!="string")throw new Error('"finish_message" parts expect an object with a "finishReason" property.');const t={finishReason:e.finishReason};return"usage"in e&&e.usage!=null&&typeof e.usage=="object"&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:typeof e.usage.promptTokens=="number"?e.usage.promptTokens:Number.NaN,completionTokens:typeof e.usage.completionTokens=="number"?e.usage.completionTokens:Number.NaN}),{type:"finish_message",value:t}}},Kt={code:"e",name:"finish_step",parse:e=>{if(e==null||typeof e!="object"||!("finishReason"in e)||typeof e.finishReason!="string")throw new Error('"finish_step" parts expect an object with a "finishReason" property.');const t={finishReason:e.finishReason,isContinued:!1};return"usage"in e&&e.usage!=null&&typeof e.usage=="object"&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:typeof e.usage.promptTokens=="number"?e.usage.promptTokens:Number.NaN,completionTokens:typeof e.usage.completionTokens=="number"?e.usage.completionTokens:Number.NaN}),"isContinued"in e&&typeof e.isContinued=="boolean"&&(t.isContinued=e.isContinued),{type:"finish_step",value:t}}},ti=[Mt,Dt,Ft,$t,Lt,Bt,Ut,zt,Zt,Gt,Vt,qt,Ht,Wt,Kt];Mt.code+"",Dt.code+"",Ft.code+"",$t.code+"",Lt.code+"",Bt.code+"",Ut.code+"",zt.code+"",Zt.code+"",Gt.code+"",Vt.code+"",qt.code+"",Ht.code+"",Wt.code+"",Kt.code+"";Mt.name+"",Mt.code,Dt.name+"",Dt.code,Ft.name+"",Ft.code,$t.name+"",$t.code,Lt.name+"",Lt.code,Bt.name+"",Bt.code,Ut.name+"",Ut.code,zt.name+"",zt.code,Zt.name+"",Zt.code,Gt.name+"",Gt.code,Vt.name+"",Vt.code,qt.name+"",qt.code,Ht.name+"",Ht.code,Wt.name+"",Wt.code,Kt.name+"",Kt.code;ti.map(e=>e.code);function Nf(e,t){const n=ti.find(r=>r.name===e);if(!n)throw new Error(`Invalid stream part type: ${e}`);return`${n.code}:${JSON.stringify(t)}
`}var Mr=Symbol.for("vercel.ai.schema");function Pf(e,{validate:t}={}){return{[Mr]:!0,_type:void 0,[vn]:!0,jsonSchema:e,validate:t}}function Of(e){return typeof e=="object"&&e!==null&&Mr in e&&e[Mr]===!0&&"jsonSchema"in e&&"validate"in e}function us(e){return Of(e)?e:jf(e)}function jf(e){return Pf(Rf(e),{validate:t=>{const n=e.safeParse(t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}}})}var Mf=typeof globalThis=="object"?globalThis:typeof self=="object"?self:typeof window=="object"?window:typeof global=="object"?global:{},at="1.9.0",ds=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function Df(e){var t=new Set([e]),n=new Set,r=e.match(ds);if(!r)return function(){return!1};var a={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(a.prerelease!=null)return function(l){return l===e};function s(i){return n.add(i),!1}function o(i){return t.add(i),!0}return function(l){if(t.has(l))return!0;if(n.has(l))return!1;var c=l.match(ds);if(!c)return s(l);var u={major:+c[1],minor:+c[2],patch:+c[3],prerelease:c[4]};return u.prerelease!=null||a.major!==u.major?s(l):a.major===0?a.minor===u.minor&&a.patch<=u.patch?o(l):s(l):a.minor<=u.minor?o(l):s(l)}}var Ff=Df(at),$f=at.split(".")[0],Yt=Symbol.for("opentelemetry.js.api."+$f),Jt=Mf;function va(e,t,n,r){var a;r===void 0&&(r=!1);var s=Jt[Yt]=(a=Jt[Yt])!==null&&a!==void 0?a:{version:at};if(!r&&s[e]){var o=new Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return n.error(o.stack||o.message),!1}if(s.version!==at){var o=new Error("@opentelemetry/api: Registration of version v"+s.version+" for "+e+" does not match previously registered API v"+at);return n.error(o.stack||o.message),!1}return s[e]=t,n.debug("@opentelemetry/api: Registered a global for "+e+" v"+at+"."),!0}function Xt(e){var t,n,r=(t=Jt[Yt])===null||t===void 0?void 0:t.version;if(!(!r||!Ff(r)))return(n=Jt[Yt])===null||n===void 0?void 0:n[e]}function ba(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+at+".");var n=Jt[Yt];n&&delete n[e]}var Lf=function(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),a,s=[],o;try{for(;(t===void 0||t-- >0)&&!(a=r.next()).done;)s.push(a.value)}catch(i){o={error:i}}finally{try{a&&!a.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return s},Bf=function(e,t,n){if(n||arguments.length===2)for(var r=0,a=t.length,s;r<a;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))},Uf=function(){function e(t){this._namespace=t.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Et("debug",this._namespace,t)},e.prototype.error=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Et("error",this._namespace,t)},e.prototype.info=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Et("info",this._namespace,t)},e.prototype.warn=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Et("warn",this._namespace,t)},e.prototype.verbose=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Et("verbose",this._namespace,t)},e}();function Et(e,t,n){var r=Xt("diag");if(r)return n.unshift(t),r[e].apply(r,Bf([],Lf(n),!1))}var Te;(function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"})(Te||(Te={}));function zf(e,t){e<Te.NONE?e=Te.NONE:e>Te.ALL&&(e=Te.ALL),t=t||{};function n(r,a){var s=t[r];return typeof s=="function"&&e>=a?s.bind(t):function(){}}return{error:n("error",Te.ERROR),warn:n("warn",Te.WARN),info:n("info",Te.INFO),debug:n("debug",Te.DEBUG),verbose:n("verbose",Te.VERBOSE)}}var Zf=function(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),a,s=[],o;try{for(;(t===void 0||t-- >0)&&!(a=r.next()).done;)s.push(a.value)}catch(i){o={error:i}}finally{try{a&&!a.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return s},Gf=function(e,t,n){if(n||arguments.length===2)for(var r=0,a=t.length,s;r<a;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))},Vf="diag",Rn=function(){function e(){function t(a){return function(){for(var s=[],o=0;o<arguments.length;o++)s[o]=arguments[o];var i=Xt("diag");if(i)return i[a].apply(i,Gf([],Zf(s),!1))}}var n=this,r=function(a,s){var o,i,l;if(s===void 0&&(s={logLevel:Te.INFO}),a===n){var c=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return n.error((o=c.stack)!==null&&o!==void 0?o:c.message),!1}typeof s=="number"&&(s={logLevel:s});var u=Xt("diag"),d=zf((i=s.logLevel)!==null&&i!==void 0?i:Te.INFO,a);if(u&&!s.suppressOverrideMessage){var g=(l=new Error().stack)!==null&&l!==void 0?l:"<failed to generate stacktrace>";u.warn("Current logger will be overwritten from "+g),d.warn("Current logger will overwrite one already registered from "+g)}return va("diag",d,n,!0)};n.setLogger=r,n.disable=function(){ba(Vf,n)},n.createComponentLogger=function(a){return new Uf(a)},n.verbose=t("verbose"),n.debug=t("debug"),n.info=t("info"),n.warn=t("warn"),n.error=t("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}();function qf(e){return Symbol.for(e)}var Hf=function(){function e(t){var n=this;n._currentContext=t?new Map(t):new Map,n.getValue=function(r){return n._currentContext.get(r)},n.setValue=function(r,a){var s=new e(n._currentContext);return s._currentContext.set(r,a),s},n.deleteValue=function(r){var a=new e(n._currentContext);return a._currentContext.delete(r),a}}return e}(),Wf=new Hf,Kf=function(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),a,s=[],o;try{for(;(t===void 0||t-- >0)&&!(a=r.next()).done;)s.push(a.value)}catch(i){o={error:i}}finally{try{a&&!a.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return s},Yf=function(e,t,n){if(n||arguments.length===2)for(var r=0,a=t.length,s;r<a;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))},Jf=function(){function e(){}return e.prototype.active=function(){return Wf},e.prototype.with=function(t,n,r){for(var a=[],s=3;s<arguments.length;s++)a[s-3]=arguments[s];return n.call.apply(n,Yf([r],Kf(a),!1))},e.prototype.bind=function(t,n){return n},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),Xf=function(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),a,s=[],o;try{for(;(t===void 0||t-- >0)&&!(a=r.next()).done;)s.push(a.value)}catch(i){o={error:i}}finally{try{a&&!a.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return s},Qf=function(e,t,n){if(n||arguments.length===2)for(var r=0,a=t.length,s;r<a;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))},er="context",em=new Jf,ni=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(t){return va(er,t,Rn.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(t,n,r){for(var a,s=[],o=3;o<arguments.length;o++)s[o-3]=arguments[o];return(a=this._getContextManager()).with.apply(a,Qf([t,n,r],Xf(s),!1))},e.prototype.bind=function(t,n){return this._getContextManager().bind(t,n)},e.prototype._getContextManager=function(){return Xt(er)||em},e.prototype.disable=function(){this._getContextManager().disable(),ba(er,Rn.instance())},e}(),Dr;(function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"})(Dr||(Dr={}));var ri="0000000000000000",ai="00000000000000000000000000000000",tm={traceId:ai,spanId:ri,traceFlags:Dr.NONE},It=function(){function e(t){t===void 0&&(t=tm),this._spanContext=t}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(t,n){return this},e.prototype.setAttributes=function(t){return this},e.prototype.addEvent=function(t,n){return this},e.prototype.addLink=function(t){return this},e.prototype.addLinks=function(t){return this},e.prototype.setStatus=function(t){return this},e.prototype.updateName=function(t){return this},e.prototype.end=function(t){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(t,n){},e}(),_a=qf("OpenTelemetry Context Key SPAN");function wa(e){return e.getValue(_a)||void 0}function nm(){return wa(ni.getInstance().active())}function xa(e,t){return e.setValue(_a,t)}function rm(e){return e.deleteValue(_a)}function am(e,t){return xa(e,new It(t))}function si(e){var t;return(t=wa(e))===null||t===void 0?void 0:t.spanContext()}var sm=/^([0-9a-f]{32})$/i,om=/^[0-9a-f]{16}$/i;function im(e){return sm.test(e)&&e!==ai}function lm(e){return om.test(e)&&e!==ri}function oi(e){return im(e.traceId)&&lm(e.spanId)}function cm(e){return new It(e)}var tr=ni.getInstance(),ii=function(){function e(){}return e.prototype.startSpan=function(t,n,r){r===void 0&&(r=tr.active());var a=!!(n!=null&&n.root);if(a)return new It;var s=r&&si(r);return um(s)&&oi(s)?new It(s):new It},e.prototype.startActiveSpan=function(t,n,r,a){var s,o,i;if(!(arguments.length<2)){arguments.length===2?i=n:arguments.length===3?(s=n,i=r):(s=n,o=r,i=a);var l=o??tr.active(),c=this.startSpan(t,s,l),u=xa(l,c);return tr.with(u,i,void 0,c)}},e}();function um(e){return typeof e=="object"&&typeof e.spanId=="string"&&typeof e.traceId=="string"&&typeof e.traceFlags=="number"}var dm=new ii,pm=function(){function e(t,n,r,a){this._provider=t,this.name=n,this.version=r,this.options=a}return e.prototype.startSpan=function(t,n,r){return this._getTracer().startSpan(t,n,r)},e.prototype.startActiveSpan=function(t,n,r,a){var s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateTracer(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):dm},e}(),fm=function(){function e(){}return e.prototype.getTracer=function(t,n,r){return new ii},e}(),mm=new fm,ps=function(){function e(){}return e.prototype.getTracer=function(t,n,r){var a;return(a=this.getDelegateTracer(t,n,r))!==null&&a!==void 0?a:new pm(this,t,n,r)},e.prototype.getDelegate=function(){var t;return(t=this._delegate)!==null&&t!==void 0?t:mm},e.prototype.setDelegate=function(t){this._delegate=t},e.prototype.getDelegateTracer=function(t,n,r){var a;return(a=this._delegate)===null||a===void 0?void 0:a.getTracer(t,n,r)},e}(),Nn;(function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"})(Nn||(Nn={}));var nr="trace",gm=function(){function e(){this._proxyTracerProvider=new ps,this.wrapSpanContext=cm,this.isSpanContextValid=oi,this.deleteSpan=rm,this.getSpan=wa,this.getActiveSpan=nm,this.getSpanContext=si,this.setSpan=xa,this.setSpanContext=am}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(t){var n=va(nr,this._proxyTracerProvider,Rn.instance());return n&&this._proxyTracerProvider.setDelegate(t),n},e.prototype.getTracerProvider=function(){return Xt(nr)||this._proxyTracerProvider},e.prototype.getTracer=function(t,n){return this.getTracerProvider().getTracer(t,n)},e.prototype.disable=function(){ba(nr,Rn.instance()),this._proxyTracerProvider=new ps},e}(),hm=gm.getInstance(),ym=Object.defineProperty,li=(e,t)=>{for(var n in t)ym(e,n,{get:t[n],enumerable:!0})};async function vm(e){return e===void 0?Promise.resolve():new Promise(t=>setTimeout(t,e))}var Fr="AI_RetryError",ci=`vercel.ai.error.${Fr}`,bm=Symbol.for(ci),ui,fs=class extends H{constructor({message:e,reason:t,errors:n}){super({name:Fr,message:e}),this[ui]=!0,this.reason=t,this.errors=n,this.lastError=n[n.length-1]}static isInstance(e){return H.hasMarker(e,ci)}static isRetryError(e){return e instanceof Error&&e.name===Fr&&typeof e.reason=="string"&&Array.isArray(e.errors)}toJSON(){return{name:this.name,message:this.message,reason:this.reason,lastError:this.lastError,errors:this.errors}}};ui=bm;var _m=({maxRetries:e=2,initialDelayInMs:t=2e3,backoffFactor:n=2}={})=>async r=>di(r,{maxRetries:e,delayInMs:t,backoffFactor:n});async function di(e,{maxRetries:t,delayInMs:n,backoffFactor:r},a=[]){try{return await e()}catch(s){if(pn(s)||t===0)throw s;const o=Zd(s),i=[...a,s],l=i.length;if(l>t)throw new fs({message:`Failed after ${l} attempts. Last error: ${o}`,reason:"maxRetriesExceeded",errors:i});if(s instanceof Error&&$e.isAPICallError(s)&&s.isRetryable===!0&&l<=t)return await vm(n),di(e,{maxRetries:t,delayInMs:r*n,backoffFactor:r},i);throw l===1?s:new fs({message:`Failed after ${l} attempts with non-retryable error: '${o}'`,reason:"errorNotRetryable",errors:i})}}function rr({operationId:e,telemetry:t}){return{"operation.name":`${e}${(t==null?void 0:t.functionId)!=null?` ${t.functionId}`:""}`,"resource.name":t==null?void 0:t.functionId,"ai.operationId":e,"ai.telemetry.functionId":t==null?void 0:t.functionId}}function wm({model:e,settings:t,telemetry:n,headers:r}){var a;return{"ai.model.provider":e.provider,"ai.model.id":e.modelId,...Object.entries(t).reduce((s,[o,i])=>(s[`ai.settings.${o}`]=i,s),{}),...Object.entries((a=n==null?void 0:n.metadata)!=null?a:{}).reduce((s,[o,i])=>(s[`ai.telemetry.metadata.${o}`]=i,s),{}),...Object.entries(r??{}).reduce((s,[o,i])=>(i!==void 0&&(s[`ai.request.headers.${o}`]=i),s),{})}}var xm={startSpan(){return un},startActiveSpan(e,t,n,r){if(typeof t=="function")return t(un);if(typeof n=="function")return n(un);if(typeof r=="function")return r(un)}},un={spanContext(){return km},setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording(){return!1},recordException(){return this}},km={traceId:"",spanId:"",traceFlags:0};function Sm({isEnabled:e=!1,tracer:t}={}){return e?t||hm.getTracer("ai"):xm}function ar({name:e,tracer:t,attributes:n,fn:r,endWhenDone:a=!0}){return t.startActiveSpan(e,{attributes:n},async s=>{try{const o=await r(s);return a&&s.end(),o}catch(o){try{o instanceof Error?(s.recordException({name:o.name,message:o.message,stack:o.stack}),s.setStatus({code:Nn.ERROR,message:o.message})):s.setStatus({code:Nn.ERROR})}finally{s.end()}throw o}})}function ut({telemetry:e,attributes:t}){return(e==null?void 0:e.isEnabled)!==!0?{}:Object.entries(t).reduce((n,[r,a])=>{if(a===void 0)return n;if(typeof a=="object"&&"input"in a&&typeof a.input=="function"){if((e==null?void 0:e.recordInputs)===!1)return n;const s=a.input();return s===void 0?n:{...n,[r]:s}}if(typeof a=="object"&&"output"in a&&typeof a.output=="function"){if((e==null?void 0:e.recordOutputs)===!1)return n;const s=a.output();return s===void 0?n:{...n,[r]:s}}return{...n,[r]:a}},{})}var $r="AI_DownloadError",pi=`vercel.ai.error.${$r}`,Em=Symbol.for(pi),fi,sr=class extends H{constructor({url:e,statusCode:t,statusText:n,cause:r,message:a=r==null?`Failed to download ${e}: ${t} ${n}`:`Failed to download ${e}: ${r}`}){super({name:$r,message:a,cause:r}),this[fi]=!0,this.url=e,this.statusCode=t,this.statusText=n}static isInstance(e){return H.hasMarker(e,pi)}static isDownloadError(e){return e instanceof Error&&e.name===$r&&typeof e.url=="string"&&(e.statusCode==null||typeof e.statusCode=="number")&&(e.statusText==null||typeof e.statusText=="string")}toJSON(){return{name:this.name,message:this.message,url:this.url,statusCode:this.statusCode,statusText:this.statusText,cause:this.cause}}};fi=Em;async function Am({url:e,fetchImplementation:t=fetch}){var n;const r=e.toString();try{const a=await t(r);if(!a.ok)throw new sr({url:r,statusCode:a.status,statusText:a.statusText});return{data:new Uint8Array(await a.arrayBuffer()),mimeType:(n=a.headers.get("content-type"))!=null?n:void 0}}catch(a){throw sr.isInstance(a)?a:new sr({url:r,cause:a})}}var Cm=[{mimeType:"image/gif",bytes:[71,73,70]},{mimeType:"image/png",bytes:[137,80,78,71]},{mimeType:"image/jpeg",bytes:[255,216]},{mimeType:"image/webp",bytes:[82,73,70,70]}];function Tm(e){for(const{bytes:t,mimeType:n}of Cm)if(e.length>=t.length&&t.every((r,a)=>e[a]===r))return n}var Lr="AI_InvalidDataContentError",mi=`vercel.ai.error.${Lr}`,Im=Symbol.for(mi),gi,ms=class extends H{constructor({content:e,cause:t,message:n=`Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof e}.`}){super({name:Lr,message:n,cause:t}),this[gi]=!0,this.content=e}static isInstance(e){return H.hasMarker(e,mi)}static isInvalidDataContentError(e){return e instanceof Error&&e.name===Lr&&e.content!=null}toJSON(){return{name:this.name,message:this.message,stack:this.stack,cause:this.cause,content:this.content}}};gi=Im;var hi=Se([R(),An(Uint8Array),An(ArrayBuffer),Vo(e=>{var t,n;return(n=(t=globalThis.Buffer)==null?void 0:t.isBuffer(e))!=null?n:!1},{message:"Must be a Buffer"})]);function Rm(e){return typeof e=="string"?e:e instanceof ArrayBuffer?bn(new Uint8Array(e)):bn(e)}function Pn(e){if(e instanceof Uint8Array)return e;if(typeof e=="string")try{return tp(e)}catch(t){throw new ms({message:"Invalid data content. Content string is not a base64-encoded media.",content:e,cause:t})}if(e instanceof ArrayBuffer)return new Uint8Array(e);throw new ms({content:e})}function Nm(e){try{return new TextDecoder().decode(e)}catch{throw new Error("Error decoding Uint8Array to text")}}var Br="AI_InvalidMessageRoleError",yi=`vercel.ai.error.${Br}`,Pm=Symbol.for(yi),vi,Om=class extends H{constructor({role:e,message:t=`Invalid message role: '${e}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:Br,message:t}),this[vi]=!0,this.role=e}static isInstance(e){return H.hasMarker(e,yi)}static isInvalidMessageRoleError(e){return e instanceof Error&&e.name===Br&&typeof e.role=="string"}toJSON(){return{name:this.name,message:this.message,stack:this.stack,role:this.role}}};vi=Pm;function jm(e){try{const[t,n]=e.split(",");return{mimeType:t.split(";")[0].split(":")[1],base64Content:n}}catch{return{mimeType:void 0,base64Content:void 0}}}async function gs({prompt:e,modelSupportsImageUrls:t=!0,modelSupportsUrl:n=()=>!1,downloadImplementation:r=Am}){const a=await Dm(e.messages,r,t,n);return[...e.system!=null?[{role:"system",content:e.system}]:[],...e.messages.map(s=>Mm(s,a))]}function Mm(e,t){const n=e.role;switch(n){case"system":return{role:"system",content:e.content,providerMetadata:e.experimental_providerMetadata};case"user":return typeof e.content=="string"?{role:"user",content:[{type:"text",text:e.content}],providerMetadata:e.experimental_providerMetadata}:{role:"user",content:e.content.map(r=>Fm(r,t)).filter(r=>r.type!=="text"||r.text!==""),providerMetadata:e.experimental_providerMetadata};case"assistant":return typeof e.content=="string"?{role:"assistant",content:[{type:"text",text:e.content}],providerMetadata:e.experimental_providerMetadata}:{role:"assistant",content:e.content.filter(r=>r.type!=="text"||r.text!=="").map(r=>{const{experimental_providerMetadata:a,...s}=r;return{...s,providerMetadata:a}}),providerMetadata:e.experimental_providerMetadata};case"tool":return{role:"tool",content:e.content.map(r=>({type:"tool-result",toolCallId:r.toolCallId,toolName:r.toolName,result:r.result,content:r.experimental_content,isError:r.isError,providerMetadata:r.experimental_providerMetadata})),providerMetadata:e.experimental_providerMetadata};default:{const r=n;throw new Om({role:r})}}}async function Dm(e,t,n,r){const a=e.filter(o=>o.role==="user").map(o=>o.content).filter(o=>Array.isArray(o)).flat().filter(o=>o.type==="image"||o.type==="file").filter(o=>!(o.type==="image"&&n===!0)).map(o=>o.type==="image"?o.image:o.data).map(o=>typeof o=="string"&&(o.startsWith("http:")||o.startsWith("https:"))?new URL(o):o).filter(o=>o instanceof URL).filter(o=>!r(o)),s=await Promise.all(a.map(async o=>({url:o,data:await t({url:o})})));return Object.fromEntries(s.map(({url:o,data:i})=>[o.toString(),i]))}function Fm(e,t){if(e.type==="text")return{type:"text",text:e.text,providerMetadata:e.experimental_providerMetadata};let n=e.mimeType,r,a,s;const o=e.type;switch(o){case"image":r=e.image;break;case"file":r=e.data;break;default:throw new Error(`Unsupported part type: ${o}`)}try{a=typeof r=="string"?new URL(r):r}catch{a=r}if(a instanceof URL)if(a.protocol==="data:"){const{mimeType:i,base64Content:l}=jm(a.toString());if(i==null||l==null)throw new Error(`Invalid data URL format in part ${o}`);n=i,s=Pn(l)}else{const i=t[a.toString()];i?(s=i.data,n??(n=i.mimeType)):s=a}else s=Pn(a);switch(o){case"image":return n==null&&s instanceof Uint8Array&&(n=Tm(s)),{type:"image",image:s,mimeType:n,providerMetadata:e.experimental_providerMetadata};case"file":if(n==null)throw new Error("Mime type is missing for file part");return{type:"file",data:s instanceof Uint8Array?Rm(s):s,mimeType:n,providerMetadata:e.experimental_providerMetadata}}}var Ur="AI_InvalidArgumentError",bi=`vercel.ai.error.${Ur}`,$m=Symbol.for(bi),_i,ce=class extends H{constructor({parameter:e,value:t,message:n}){super({name:Ur,message:`Invalid argument for parameter ${e}: ${n}`}),this[_i]=!0,this.parameter=e,this.value=t}static isInstance(e){return H.hasMarker(e,bi)}static isInvalidArgumentError(e){return e instanceof Error&&e.name===Ur&&typeof e.parameter=="string"&&typeof e.value=="string"}toJSON(){return{name:this.name,message:this.message,stack:this.stack,parameter:this.parameter,value:this.value}}};_i=$m;function hs({maxTokens:e,temperature:t,topP:n,topK:r,presencePenalty:a,frequencyPenalty:s,stopSequences:o,seed:i,maxRetries:l}){if(e!=null){if(!Number.isInteger(e))throw new ce({parameter:"maxTokens",value:e,message:"maxTokens must be an integer"});if(e<1)throw new ce({parameter:"maxTokens",value:e,message:"maxTokens must be >= 1"})}if(t!=null&&typeof t!="number")throw new ce({parameter:"temperature",value:t,message:"temperature must be a number"});if(n!=null&&typeof n!="number")throw new ce({parameter:"topP",value:n,message:"topP must be a number"});if(r!=null&&typeof r!="number")throw new ce({parameter:"topK",value:r,message:"topK must be a number"});if(a!=null&&typeof a!="number")throw new ce({parameter:"presencePenalty",value:a,message:"presencePenalty must be a number"});if(s!=null&&typeof s!="number")throw new ce({parameter:"frequencyPenalty",value:s,message:"frequencyPenalty must be a number"});if(i!=null&&!Number.isInteger(i))throw new ce({parameter:"seed",value:i,message:"seed must be an integer"});if(l!=null){if(!Number.isInteger(l))throw new ce({parameter:"maxRetries",value:l,message:"maxRetries must be an integer"});if(l<0)throw new ce({parameter:"maxRetries",value:l,message:"maxRetries must be >= 0"})}return{maxTokens:e,temperature:t??0,topP:n,topK:r,presencePenalty:a,frequencyPenalty:s,stopSequences:o!=null&&o.length>0?o:void 0,seed:i,maxRetries:l??2}}var zr=Rp(()=>Se([Tp(),R(),V(),qo(),Ot(R(),zr),ne(zr)])),tt=Ot(R(),Ot(R(),zr)),Lm=ne(Se([M({type:Ee("text"),text:R()}),M({type:Ee("image"),data:R(),mimeType:R().optional()})])),wi=M({type:Ee("text"),text:R(),experimental_providerMetadata:tt.optional()}),Bm=M({type:Ee("image"),image:Se([hi,An(URL)]),mimeType:R().optional(),experimental_providerMetadata:tt.optional()}),Um=M({type:Ee("file"),data:Se([hi,An(URL)]),mimeType:R(),experimental_providerMetadata:tt.optional()}),zm=M({type:Ee("tool-call"),toolCallId:R(),toolName:R(),args:ga()}),Zm=M({type:Ee("tool-result"),toolCallId:R(),toolName:R(),result:ga(),content:Lm.optional(),isError:qo().optional(),experimental_providerMetadata:tt.optional()}),Gm=M({role:Ee("system"),content:R(),experimental_providerMetadata:tt.optional()}),Vm=M({role:Ee("user"),content:Se([R(),ne(Se([wi,Bm,Um]))]),experimental_providerMetadata:tt.optional()}),qm=M({role:Ee("assistant"),content:Se([R(),ne(Se([wi,zm]))]),experimental_providerMetadata:tt.optional()}),Hm=M({role:Ee("tool"),content:ne(Zm),experimental_providerMetadata:tt.optional()}),Wm=Se([Gm,Vm,qm,Hm]);function Km(e){if(!Array.isArray(e))return"other";if(e.length===0)return"messages";const t=e.map(Ym);return t.some(n=>n==="has-ui-specific-parts")?"ui-messages":t.every(n=>n==="has-core-specific-parts"||n==="message")?"messages":"other"}function Ym(e){return typeof e=="object"&&e!==null&&(e.role==="function"||e.role==="data"||"toolInvocations"in e||"experimental_attachments"in e)?"has-ui-specific-parts":typeof e=="object"&&e!==null&&"content"in e&&(Array.isArray(e.content)||"experimental_providerMetadata"in e)?"has-core-specific-parts":typeof e=="object"&&e!==null&&"role"in e&&"content"in e&&typeof e.content=="string"&&["system","user","assistant","tool"].includes(e.role)?"message":"other"}function Jm(e){var t,n,r;const a=[];for(const s of e){let o;try{o=new URL(s.url)}catch{throw new Error(`Invalid URL: ${s.url}`)}switch(o.protocol){case"http:":case"https:":{if((t=s.contentType)!=null&&t.startsWith("image/"))a.push({type:"image",image:o});else{if(!s.contentType)throw new Error("If the attachment is not an image, it must specify a content type");a.push({type:"file",data:o,mimeType:s.contentType})}break}case"data:":{let i,l,c;try{[i,l]=s.url.split(","),c=i.split(";")[0].split(":")[1]}catch{throw new Error(`Error processing data URL: ${s.url}`)}if(c==null||l==null)throw new Error(`Invalid data URL format: ${s.url}`);if((n=s.contentType)!=null&&n.startsWith("image/"))a.push({type:"image",image:Pn(l)});else if((r=s.contentType)!=null&&r.startsWith("text/"))a.push({type:"text",text:Nm(Pn(l))});else{if(!s.contentType)throw new Error("If the attachment is not an image or text, it must specify a content type");a.push({type:"file",data:l,mimeType:s.contentType})}break}default:throw new Error(`Unsupported URL protocol: ${o.protocol}`)}}return a}var xi="AI_MessageConversionError",ki=`vercel.ai.error.${xi}`,Xm=Symbol.for(ki),Si,ys=class extends H{constructor({originalMessage:e,message:t}){super({name:xi,message:t}),this[Si]=!0,this.originalMessage=e}static isInstance(e){return H.hasMarker(e,ki)}};Si=Xm;function Qm(e,t){var n;const r=(n=t==null?void 0:t.tools)!=null?n:{},a=[];for(const s of e){const{role:o,content:i,toolInvocations:l,experimental_attachments:c}=s;switch(o){case"system":{a.push({role:"system",content:i});break}case"user":{a.push({role:"user",content:c?[{type:"text",text:i},...Jm(c)]:i});break}case"assistant":{if(l==null){a.push({role:"assistant",content:i});break}a.push({role:"assistant",content:[{type:"text",text:i},...l.map(({toolCallId:u,toolName:d,args:g})=>({type:"tool-call",toolCallId:u,toolName:d,args:g}))]}),a.push({role:"tool",content:l.map(u=>{if(!("result"in u))throw new ys({originalMessage:s,message:"ToolInvocation must have a result: "+JSON.stringify(u)});const{toolCallId:d,toolName:g,result:v}=u,f=r[g];return(f==null?void 0:f.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:d,toolName:g,result:f.experimental_toToolResultContent(v),experimental_content:f.experimental_toToolResultContent(v)}:{type:"tool-result",toolCallId:d,toolName:g,result:v}})});break}case"function":case"data":case"tool":break;default:{const u=o;throw new ys({originalMessage:s,message:`Unsupported role: ${u}`})}}}return a}function vs({prompt:e,tools:t}){if(e.prompt==null&&e.messages==null)throw new rt({prompt:e,message:"prompt or messages must be defined"});if(e.prompt!=null&&e.messages!=null)throw new rt({prompt:e,message:"prompt and messages cannot be defined at the same time"});if(e.system!=null&&typeof e.system!="string")throw new rt({prompt:e,message:"system must be a string"});if(e.prompt!=null){if(typeof e.prompt!="string")throw new rt({prompt:e,message:"prompt must be a string"});return{type:"prompt",system:e.system,messages:[{role:"user",content:e.prompt}]}}if(e.messages!=null){const n=Km(e.messages);if(n==="other")throw new rt({prompt:e,message:"messages must be an array of CoreMessage or UIMessage"});const r=n==="ui-messages"?Qm(e.messages,{tools:t}):e.messages,a=mt({value:r,schema:ne(Wm)});if(!a.success)throw new rt({prompt:e,message:"messages must be an array of CoreMessage or UIMessage",cause:a.error});return{type:"messages",messages:r,system:e.system}}throw new Error("unreachable")}function eg(e){return{promptTokens:e.promptTokens,completionTokens:e.completionTokens,totalTokens:e.promptTokens+e.completionTokens}}function ka(e,{contentType:t,dataStreamVersion:n}){var r;const a=new Headers((r=e==null?void 0:e.headers)!=null?r:{});return a.has("Content-Type")||a.set("Content-Type",t),n!==void 0&&a.set("X-Vercel-AI-Data-Stream",n),a}var tg="JSON schema:",ng="You MUST answer with a JSON object that matches the JSON schema above.",rg="You MUST answer with JSON.";function bs({prompt:e,schema:t,schemaPrefix:n=t!=null?tg:void 0,schemaSuffix:r=t!=null?ng:rg}){return[e!=null&&e.length>0?e:void 0,e!=null&&e.length>0?"":void 0,n,t!=null?JSON.stringify(t):void 0,r].filter(a=>a!=null).join(`
`)}var Zr="AI_NoObjectGeneratedError",Ei=`vercel.ai.error.${Zr}`,ag=Symbol.for(Ei),Ai,Gr=class extends H{constructor({message:e="No object generated."}={}){super({name:Zr,message:e}),this[Ai]=!0}static isInstance(e){return H.hasMarker(e,Ei)}static isNoObjectGeneratedError(e){return e instanceof Error&&e.name===Zr}toJSON(){return{name:this.name,cause:this.cause,message:this.message,stack:this.stack}}};Ai=ag;function sg(e,t){const n=e.pipeThrough(new TransformStream(t));return n[Symbol.asyncIterator]=()=>{const r=n.getReader();return{async next(){const{done:a,value:s}=await r.read();return a?{done:!0,value:void 0}:{done:!1,value:s}}}},n}var og={type:"no-schema",jsonSchema:void 0,validatePartialResult({value:e,textDelta:t}){return{success:!0,value:{partial:e,textDelta:t}}},validateFinalResult(e){return e===void 0?{success:!1,error:new Gr}:{success:!0,value:e}},createElementStream(){throw new fe({functionality:"element streams in no-schema mode"})}},ig=e=>({type:"object",jsonSchema:e.jsonSchema,validatePartialResult({value:t,textDelta:n}){return{success:!0,value:{partial:t,textDelta:n}}},validateFinalResult(t){return mt({value:t,schema:e})},createElementStream(){throw new fe({functionality:"element streams in object mode"})}}),lg=e=>{const{$schema:t,...n}=e.jsonSchema;return{type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{elements:{type:"array",items:n}},required:["elements"],additionalProperties:!1},validatePartialResult({value:r,latestObject:a,isFirstDelta:s,isFinalDelta:o}){var i;if(!Sr(r)||!Ga(r.elements))return{success:!1,error:new Je({value:r,cause:"value must be an object that contains an array of elements"})};const l=r.elements,c=[];for(let g=0;g<l.length;g++){const v=l[g],f=mt({value:v,schema:e});if(!(g===l.length-1&&!o)){if(!f.success)return f;c.push(f.value)}}const u=(i=a==null?void 0:a.length)!=null?i:0;let d="";return s&&(d+="["),u>0&&(d+=","),d+=c.slice(u).map(g=>JSON.stringify(g)).join(","),o&&(d+="]"),{success:!0,value:{partial:c,textDelta:d}}},validateFinalResult(r){if(!Sr(r)||!Ga(r.elements))return{success:!1,error:new Je({value:r,cause:"value must be an object that contains an array of elements"})};const a=r.elements;for(const s of a){const o=mt({value:s,schema:e});if(!o.success)return o}return{success:!0,value:a}},createElementStream(r){let a=0;return sg(r,{transform(s,o){switch(s.type){case"object":{const i=s.object;for(;a<i.length;a++)o.enqueue(i[a]);break}case"text-delta":case"finish":break;case"error":o.error(s.error);break;default:{const i=s;throw new Error(`Unsupported chunk type: ${i}`)}}}})}}},cg=e=>({type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{result:{type:"string",enum:e}},required:["result"],additionalProperties:!1},validateFinalResult(t){if(!Sr(t)||typeof t.result!="string")return{success:!1,error:new Je({value:t,cause:'value must be an object that contains a string in the "result" property.'})};const n=t.result;return e.includes(n)?{success:!0,value:n}:{success:!1,error:new Je({value:t,cause:"value must be a string in the enum"})}},validatePartialResult(){throw new fe({functionality:"partial results in enum mode"})},createElementStream(){throw new fe({functionality:"element streams in enum mode"})}});function ug({output:e,schema:t,enumValues:n}){switch(e){case"object":return ig(us(t));case"array":return lg(us(t));case"enum":return cg(n);case"no-schema":return og;default:{const r=e;throw new Error(`Unsupported output: ${r}`)}}}function dg({output:e,mode:t,schema:n,schemaName:r,schemaDescription:a,enumValues:s}){if(e!=null&&e!=="object"&&e!=="array"&&e!=="enum"&&e!=="no-schema")throw new ce({parameter:"output",value:e,message:"Invalid output type."});if(e==="no-schema"){if(t==="auto"||t==="tool")throw new ce({parameter:"mode",value:t,message:'Mode must be "json" for no-schema output.'});if(n!=null)throw new ce({parameter:"schema",value:n,message:"Schema is not supported for no-schema output."});if(a!=null)throw new ce({parameter:"schemaDescription",value:a,message:"Schema description is not supported for no-schema output."});if(r!=null)throw new ce({parameter:"schemaName",value:r,message:"Schema name is not supported for no-schema output."});if(s!=null)throw new ce({parameter:"enumValues",value:s,message:"Enum values are not supported for no-schema output."})}if(e==="object"){if(n==null)throw new ce({parameter:"schema",value:n,message:"Schema is required for object output."});if(s!=null)throw new ce({parameter:"enumValues",value:s,message:"Enum values are not supported for object output."})}if(e==="array"){if(n==null)throw new ce({parameter:"schema",value:n,message:"Element schema is required for array output."});if(s!=null)throw new ce({parameter:"enumValues",value:s,message:"Enum values are not supported for array output."})}if(e==="enum"){if(n!=null)throw new ce({parameter:"schema",value:n,message:"Schema is not supported for enum output."});if(a!=null)throw new ce({parameter:"schemaDescription",value:a,message:"Schema description is not supported for enum output."});if(r!=null)throw new ce({parameter:"schemaName",value:r,message:"Schema name is not supported for enum output."});if(s==null)throw new ce({parameter:"enumValues",value:s,message:"Enum values are required for enum output."});for(const o of s)if(typeof o!="string")throw new ce({parameter:"enumValues",value:o,message:"Enum values must be strings."})}}var pg=tn({prefix:"aiobj",size:24});async function fg({model:e,enum:t,schema:n,schemaName:r,schemaDescription:a,mode:s,output:o="object",system:i,prompt:l,messages:c,maxRetries:u,abortSignal:d,headers:g,experimental_telemetry:v,experimental_providerMetadata:f,_internal:{generateId:m=pg,currentDate:h=()=>new Date}={},...b}){dg({output:o,mode:s,schema:n,schemaName:r,schemaDescription:a,enumValues:t});const _=ug({output:o,schema:n,enumValues:t});_.type==="no-schema"&&s===void 0&&(s="json");const w=wm({model:e,telemetry:v,headers:g,settings:{...b,maxRetries:u}}),A=Sm(v);return ar({name:"ai.generateObject",attributes:ut({telemetry:v,attributes:{...rr({operationId:"ai.generateObject",telemetry:v}),...w,"ai.prompt":{input:()=>JSON.stringify({system:i,prompt:l,messages:c})},"ai.schema":_.jsonSchema!=null?{input:()=>JSON.stringify(_.jsonSchema)}:void 0,"ai.schema.name":r,"ai.schema.description":a,"ai.settings.output":_.type,"ai.settings.mode":s}}),tracer:A,fn:async O=>{var N,F;const D=_m({maxRetries:u});(s==="auto"||s==null)&&(s=e.defaultObjectGenerationMode);let I,$,G,J,he,X,ue,le,de;switch(s){case"json":{const L=vs({prompt:{system:_.jsonSchema==null?bs({prompt:i}):e.supportsStructuredOutputs?i:bs({prompt:i,schema:_.jsonSchema}),prompt:l,messages:c},tools:void 0}),W=await gs({prompt:L,modelSupportsImageUrls:e.supportsImageUrls,modelSupportsUrl:e.supportsUrl}),B=await D(()=>ar({name:"ai.generateObject.doGenerate",attributes:ut({telemetry:v,attributes:{...rr({operationId:"ai.generateObject.doGenerate",telemetry:v}),...w,"ai.prompt.format":{input:()=>L.type},"ai.prompt.messages":{input:()=>JSON.stringify(W)},"ai.settings.mode":s,"gen_ai.system":e.provider,"gen_ai.request.model":e.modelId,"gen_ai.request.frequency_penalty":b.frequencyPenalty,"gen_ai.request.max_tokens":b.maxTokens,"gen_ai.request.presence_penalty":b.presencePenalty,"gen_ai.request.temperature":b.temperature,"gen_ai.request.top_k":b.topK,"gen_ai.request.top_p":b.topP}}),tracer:A,fn:async te=>{var oe,ae,pe,se,be,K;const Q=await e.doGenerate({mode:{type:"object-json",schema:_.jsonSchema,name:r,description:a},...hs(b),inputFormat:L.type,prompt:W,providerMetadata:f,abortSignal:d,headers:g});if(Q.text===void 0)throw new Gr;const Ie={id:(ae=(oe=Q.response)==null?void 0:oe.id)!=null?ae:m(),timestamp:(se=(pe=Q.response)==null?void 0:pe.timestamp)!=null?se:h(),modelId:(K=(be=Q.response)==null?void 0:be.modelId)!=null?K:e.modelId};return te.setAttributes(ut({telemetry:v,attributes:{"ai.response.finishReason":Q.finishReason,"ai.response.object":{output:()=>Q.text},"ai.response.id":Ie.id,"ai.response.model":Ie.modelId,"ai.response.timestamp":Ie.timestamp.toISOString(),"ai.usage.promptTokens":Q.usage.promptTokens,"ai.usage.completionTokens":Q.usage.completionTokens,"ai.finishReason":Q.finishReason,"ai.result.object":{output:()=>Q.text},"gen_ai.response.finish_reasons":[Q.finishReason],"gen_ai.response.id":Ie.id,"gen_ai.response.model":Ie.modelId,"gen_ai.usage.prompt_tokens":Q.usage.promptTokens,"gen_ai.usage.completion_tokens":Q.usage.completionTokens}})),{...Q,objectText:Q.text,responseData:Ie}}}));I=B.objectText,$=B.finishReason,G=B.usage,J=B.warnings,he=B.rawResponse,le=B.logprobs,de=B.providerMetadata,ue=(N=B.request)!=null?N:{},X=B.responseData;break}case"tool":{const L=vs({prompt:{system:i,prompt:l,messages:c},tools:void 0}),W=await gs({prompt:L,modelSupportsImageUrls:e.supportsImageUrls,modelSupportsUrl:e.supportsUrl}),B=L.type,te=await D(()=>ar({name:"ai.generateObject.doGenerate",attributes:ut({telemetry:v,attributes:{...rr({operationId:"ai.generateObject.doGenerate",telemetry:v}),...w,"ai.prompt.format":{input:()=>B},"ai.prompt.messages":{input:()=>JSON.stringify(W)},"ai.settings.mode":s,"gen_ai.system":e.provider,"gen_ai.request.model":e.modelId,"gen_ai.request.frequency_penalty":b.frequencyPenalty,"gen_ai.request.max_tokens":b.maxTokens,"gen_ai.request.presence_penalty":b.presencePenalty,"gen_ai.request.temperature":b.temperature,"gen_ai.request.top_k":b.topK,"gen_ai.request.top_p":b.topP}}),tracer:A,fn:async oe=>{var ae,pe,se,be,K,Q,Ie,St;const ke=await e.doGenerate({mode:{type:"object-tool",tool:{type:"function",name:r??"json",description:a??"Respond with a JSON object.",parameters:_.jsonSchema}},...hs(b),inputFormat:B,prompt:W,providerMetadata:f,abortSignal:d,headers:g}),ct=(pe=(ae=ke.toolCalls)==null?void 0:ae[0])==null?void 0:pe.args;if(ct===void 0)throw new Gr;const He={id:(be=(se=ke.response)==null?void 0:se.id)!=null?be:m(),timestamp:(Q=(K=ke.response)==null?void 0:K.timestamp)!=null?Q:h(),modelId:(St=(Ie=ke.response)==null?void 0:Ie.modelId)!=null?St:e.modelId};return oe.setAttributes(ut({telemetry:v,attributes:{"ai.response.finishReason":ke.finishReason,"ai.response.object":{output:()=>ct},"ai.response.id":He.id,"ai.response.model":He.modelId,"ai.response.timestamp":He.timestamp.toISOString(),"ai.usage.promptTokens":ke.usage.promptTokens,"ai.usage.completionTokens":ke.usage.completionTokens,"ai.finishReason":ke.finishReason,"ai.result.object":{output:()=>ct},"gen_ai.response.finish_reasons":[ke.finishReason],"gen_ai.response.id":He.id,"gen_ai.response.model":He.modelId,"gen_ai.usage.input_tokens":ke.usage.promptTokens,"gen_ai.usage.output_tokens":ke.usage.completionTokens}})),{...ke,objectText:ct,responseData:He}}}));I=te.objectText,$=te.finishReason,G=te.usage,J=te.warnings,he=te.rawResponse,le=te.logprobs,de=te.providerMetadata,ue=(F=te.request)!=null?F:{},X=te.responseData;break}case void 0:throw new Error("Model does not have a default object generation mode.");default:{const L=s;throw new Error(`Unsupported mode: ${L}`)}}const E=pa({text:I});if(!E.success)throw E.error;const C=_.validateFinalResult(E.value);if(!C.success)throw C.error;return O.setAttributes(ut({telemetry:v,attributes:{"ai.response.finishReason":$,"ai.response.object":{output:()=>JSON.stringify(C.value)},"ai.usage.promptTokens":G.promptTokens,"ai.usage.completionTokens":G.completionTokens,"ai.finishReason":$,"ai.result.object":{output:()=>JSON.stringify(C.value)}}})),new mg({object:C.value,finishReason:$,usage:eg(G),warnings:J,request:ue,response:{...X,headers:he==null?void 0:he.headers},logprobs:le,providerMetadata:de})}})}var mg=class{constructor(e){this.object=e.object,this.finishReason=e.finishReason,this.usage=e.usage,this.warnings=e.warnings,this.experimental_providerMetadata=e.providerMetadata,this.response=e.response,this.request=e.request,this.rawResponse={headers:e.response.headers},this.logprobs=e.logprobs}toJsonResponse(e){var t;return new Response(JSON.stringify(this.object),{status:(t=e==null?void 0:e.status)!=null?t:200,headers:ka(e,{contentType:"application/json; charset=utf-8"})})}};tn({prefix:"aiobj",size:24});tn({prefix:"aitxt",size:24});function Ci(e,t){const n=e.getReader(),r=t.getReader();let a,s,o=!1,i=!1;async function l(u){try{a==null&&(a=n.read());const d=await a;a=void 0,d.done?u.close():u.enqueue(d.value)}catch(d){u.error(d)}}async function c(u){try{s==null&&(s=r.read());const d=await s;s=void 0,d.done?u.close():u.enqueue(d.value)}catch(d){u.error(d)}}return new ReadableStream({async pull(u){try{if(o){await c(u);return}if(i){await l(u);return}a==null&&(a=n.read()),s==null&&(s=r.read());const{result:d,reader:g}=await Promise.race([a.then(v=>({result:v,reader:n})),s.then(v=>({result:v,reader:r}))]);d.done||u.enqueue(d.value),g===n?(a=void 0,d.done&&(await c(u),o=!0)):(s=void 0,d.done&&(i=!0,await l(u)))}catch(d){u.error(d)}},cancel(){n.cancel(),r.cancel()}})}tn({prefix:"aitxt",size:24});function Ti(e){const t=new TextEncoder;let n="";const r=e||{};return new TransformStream({async start(){r.onStart&&await r.onStart()},async transform(a,s){const o=typeof a=="string"?a:a.content;s.enqueue(t.encode(o)),n+=o,r.onToken&&await r.onToken(o),r.onText&&typeof a=="string"&&await r.onText(a)},async flush(){const a=gg(r);r.onCompletion&&await r.onCompletion(n),r.onFinal&&!a&&await r.onFinal(n)}})}function gg(e){return"experimental_onFunctionCall"in e}function hg(){let e=!0;return t=>(e&&(t=t.trimStart(),t&&(e=!1)),t)}function Ii(){const e=new TextEncoder,t=new TextDecoder;return new TransformStream({transform:async(n,r)=>{const a=t.decode(n);r.enqueue(e.encode(Nf("text",a)))}})}new TextDecoder("utf-8");var yg={};li(yg,{toAIStream:()=>vg,toDataStream:()=>Sa,toDataStreamResponse:()=>bg});function vg(e,t){return Sa(e,t)}function Sa(e,t){return e.pipeThrough(new TransformStream({transform:async(n,r)=>{var a;if(typeof n=="string"){r.enqueue(n);return}if("event"in n){n.event==="on_chat_model_stream"&&_s((a=n.data)==null?void 0:a.chunk,r);return}_s(n,r)}})).pipeThrough(Ti(t)).pipeThrough(Ii())}function bg(e,t){var n;const r=Sa(e,t==null?void 0:t.callbacks),a=t==null?void 0:t.data,s=t==null?void 0:t.init,o=a?Ci(a.stream,r):r;return new Response(o,{status:(n=s==null?void 0:s.status)!=null?n:200,statusText:s==null?void 0:s.statusText,headers:ka(s,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function _s(e,t){if(typeof e.content=="string")t.enqueue(e.content);else{const n=e.content;for(const r of n)r.type==="text"&&t.enqueue(r.text)}}var _g={};li(_g,{toDataStream:()=>Ri,toDataStreamResponse:()=>wg});function Ri(e,t){return xg(e).pipeThrough(Ti(t)).pipeThrough(Ii())}function wg(e,t={}){var n;const{init:r,data:a,callbacks:s}=t,o=Ri(e,s),i=a?Ci(a.stream,o):o;return new Response(i,{status:(n=r==null?void 0:r.status)!=null?n:200,statusText:r==null?void 0:r.statusText,headers:ka(r,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function xg(e){const t=e[Symbol.asyncIterator](),n=hg();return new ReadableStream({async pull(r){var a;const{value:s,done:o}=await t.next();if(o){r.close();return}const i=n((a=s.delta)!=null?a:"");i&&r.enqueue(i)}})}const Ea=async({messages:e,systemPrompt:t,prompt:n,extractedCode:r,model:a})=>await fg({model:a,schema:Gp,output:"object",messages:[{role:"system",content:t},{role:"system",content:`extractedCode (this code is writen by user): ${r}`},...e,{role:"user",content:n}]});class kg{constructor(){We(this,"name","openai_3.5_turbo");We(this,"apiKey","")}init(t){this.apiKey=t}async generateResponse(t){var n;try{const r=ya({compatibility:"strict",apiKey:this.apiKey});return{error:null,success:(await Ea({model:r((n=ft.find(s=>s.name===this.name))==null?void 0:n.model),messages:t.messages,systemPrompt:t.systemPrompt,prompt:t.prompt,extractedCode:t.extractedCode})).object}}catch(r){return{error:r,success:null}}}}function Fe(e){if(Sg(e))return;if(typeof e=="boolean")return{type:"boolean",properties:{}};const{type:t,description:n,required:r,properties:a,items:s,allOf:o,anyOf:i,oneOf:l,format:c,const:u,minLength:d}=e,g={};return n&&(g.description=n),r&&(g.required=r),c&&(g.format=c),u!==void 0&&(g.enum=[u]),t&&(Array.isArray(t)?t.includes("null")?(g.type=t.filter(v=>v!=="null")[0],g.nullable=!0):g.type=t:t==="null"?g.type="null":g.type=t),a!=null&&(g.properties=Object.entries(a).reduce((v,[f,m])=>(v[f]=Fe(m),v),{})),s&&(g.items=Array.isArray(s)?s.map(Fe):Fe(s)),o&&(g.allOf=o.map(Fe)),i&&(g.anyOf=i.map(Fe)),l&&(g.oneOf=l.map(Fe)),d!==void 0&&(g.minLength=d),g}function Sg(e){return e!=null&&typeof e=="object"&&e.type==="object"&&(e.properties==null||Object.keys(e.properties).length===0)}function Eg(e){var t,n;const r=[],a=[];let s=!0;for(const{role:o,content:i}of e)switch(o){case"system":{if(!s)throw new fe({functionality:"system messages are only supported at the beginning of the conversation"});r.push({text:i});break}case"user":{s=!1;const l=[];for(const c of i)switch(c.type){case"text":{l.push({text:c.text});break}case"image":{l.push(c.image instanceof URL?{fileData:{mimeType:(t=c.mimeType)!=null?t:"image/jpeg",fileUri:c.image.toString()}}:{inlineData:{mimeType:(n=c.mimeType)!=null?n:"image/jpeg",data:bn(c.image)}});break}case"file":{l.push(c.data instanceof URL?{fileData:{mimeType:c.mimeType,fileUri:c.data.toString()}}:{inlineData:{mimeType:c.mimeType,data:c.data}});break}default:{const u=c;throw new fe({functionality:`prompt part: ${u}`})}}a.push({role:"user",parts:l});break}case"assistant":{s=!1,a.push({role:"model",parts:i.map(l=>{switch(l.type){case"text":return l.text.length===0?void 0:{text:l.text};case"tool-call":return{functionCall:{name:l.toolName,args:l.args}}}}).filter(l=>l!==void 0)});break}case"tool":{s=!1,a.push({role:"user",parts:i.map(l=>({functionResponse:{name:l.toolName,response:l.result}}))});break}default:{const l=o;throw new Error(`Unsupported role: ${l}`)}}return{systemInstruction:r.length>0?{parts:r}:void 0,contents:a}}function ws(e){return e.includes("/")?e:`models/${e}`}var Ag=M({error:M({code:V().nullable(),message:R(),status:R()})}),Vr=Bo({errorSchema:Ag,errorToMessage:e=>e.error.message});function Cg(e){var t,n;const r=(t=e.tools)!=null&&t.length?e.tools:void 0,a=[];if(r==null)return{tools:void 0,toolConfig:void 0,toolWarnings:a};const s=[];for(const l of r)l.type==="provider-defined"?a.push({type:"unsupported-tool",tool:l}):s.push({name:l.name,description:(n=l.description)!=null?n:"",parameters:Fe(l.parameters)});const o=e.toolChoice;if(o==null)return{tools:{functionDeclarations:s},toolConfig:void 0,toolWarnings:a};const i=o.type;switch(i){case"auto":return{tools:{functionDeclarations:s},toolConfig:{functionCallingConfig:{mode:"AUTO"}},toolWarnings:a};case"none":return{tools:{functionDeclarations:s},toolConfig:{functionCallingConfig:{mode:"NONE"}},toolWarnings:a};case"required":return{tools:{functionDeclarations:s},toolConfig:{functionCallingConfig:{mode:"ANY"}},toolWarnings:a};case"tool":return{tools:{functionDeclarations:s},toolConfig:{functionCallingConfig:{mode:"ANY",allowedFunctionNames:[o.toolName]}},toolWarnings:a};default:{const l=i;throw new fe({functionality:`Unsupported tool choice type: ${l}`})}}}function xs({finishReason:e,hasToolCalls:t}){switch(e){case"STOP":return t?"tool-calls":"stop";case"MAX_TOKENS":return"length";case"RECITATION":case"SAFETY":return"content-filter";case"FINISH_REASON_UNSPECIFIED":case"OTHER":return"other";default:return"unknown"}}var Tg=class{constructor(e,t,n){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsImageUrls=!1,this.modelId=e,this.settings=t,this.config=n}get supportsObjectGeneration(){return this.settings.structuredOutputs!==!1}get provider(){return this.config.provider}async getArgs({mode:e,prompt:t,maxTokens:n,temperature:r,topP:a,topK:s,frequencyPenalty:o,presencePenalty:i,stopSequences:l,responseFormat:c,seed:u}){var d;const g=e.type,v=[];u!=null&&v.push({type:"unsupported-setting",setting:"seed"});const f={maxOutputTokens:n,temperature:r,topK:s??this.settings.topK,topP:a,frequencyPenalty:o,presencePenalty:i,stopSequences:l,responseMimeType:(c==null?void 0:c.type)==="json"?"application/json":void 0,responseSchema:(c==null?void 0:c.type)==="json"&&c.schema!=null&&this.supportsObjectGeneration?Fe(c.schema):void 0},{contents:m,systemInstruction:h}=Eg(t);switch(g){case"regular":{const{tools:b,toolConfig:_,toolWarnings:w}=Cg(e);return{args:{generationConfig:f,contents:m,systemInstruction:h,safetySettings:this.settings.safetySettings,tools:b,toolConfig:_,cachedContent:this.settings.cachedContent},warnings:[...v,...w]}}case"object-json":return{args:{generationConfig:{...f,responseMimeType:"application/json",responseSchema:e.schema!=null&&this.supportsObjectGeneration?Fe(e.schema):void 0},contents:m,systemInstruction:h,safetySettings:this.settings.safetySettings,cachedContent:this.settings.cachedContent},warnings:v};case"object-tool":return{args:{generationConfig:f,contents:m,tools:{functionDeclarations:[{name:e.tool.name,description:(d=e.tool.description)!=null?d:"",parameters:Fe(e.tool.parameters)}]},toolConfig:{functionCallingConfig:{mode:"ANY"}},safetySettings:this.settings.safetySettings,cachedContent:this.settings.cachedContent},warnings:v};default:{const b=g;throw new Error(`Unsupported type: ${b}`)}}}supportsUrl(e){return e.toString().startsWith("https://generativelanguage.googleapis.com/v1beta/files/")}async doGenerate(e){var t,n;const{args:r,warnings:a}=await this.getArgs(e),s=JSON.stringify(r),{responseHeaders:o,value:i}=await Qe({url:`${this.config.baseURL}/${ws(this.modelId)}:generateContent`,headers:Xe(this.config.headers(),e.headers),body:r,failedResponseHandler:Vr,successfulResponseHandler:nn(Ig),abortSignal:e.abortSignal,fetch:this.config.fetch}),{contents:l,...c}=r,u=i.candidates[0],d=ks({parts:u.content.parts,generateId:this.config.generateId}),g=i.usageMetadata;return{text:Ss(u.content.parts),toolCalls:d,finishReason:xs({finishReason:u.finishReason,hasToolCalls:d!=null&&d.length>0}),usage:{promptTokens:(t=g==null?void 0:g.promptTokenCount)!=null?t:NaN,completionTokens:(n=g==null?void 0:g.candidatesTokenCount)!=null?n:NaN},rawCall:{rawPrompt:l,rawSettings:c},rawResponse:{headers:o},warnings:a,request:{body:s}}}async doStream(e){const{args:t,warnings:n}=await this.getArgs(e),r=JSON.stringify(t),{responseHeaders:a,value:s}=await Qe({url:`${this.config.baseURL}/${ws(this.modelId)}:streamGenerateContent?alt=sse`,headers:Xe(this.config.headers(),e.headers),body:t,failedResponseHandler:Vr,successfulResponseHandler:fa(Rg),abortSignal:e.abortSignal,fetch:this.config.fetch}),{contents:o,...i}=t;let l="unknown",c={promptTokens:Number.NaN,completionTokens:Number.NaN};const u=this.config.generateId;let d=!1;return{stream:s.pipeThrough(new TransformStream({transform(g,v){var f,m;if(!g.success){v.enqueue({type:"error",error:g.error});return}const h=g.value,b=h.candidates[0];(b==null?void 0:b.finishReason)!=null&&(l=xs({finishReason:b.finishReason,hasToolCalls:d}));const _=h.usageMetadata;_!=null&&(c={promptTokens:(f=_.promptTokenCount)!=null?f:NaN,completionTokens:(m=_.candidatesTokenCount)!=null?m:NaN});const w=b.content;if(w==null)return;const A=Ss(w.parts);A!=null&&v.enqueue({type:"text-delta",textDelta:A});const O=ks({parts:w.parts,generateId:u});if(O!=null)for(const N of O)v.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:N.toolCallId,toolName:N.toolName,argsTextDelta:N.args}),v.enqueue({type:"tool-call",toolCallType:"function",toolCallId:N.toolCallId,toolName:N.toolName,args:N.args}),d=!0},flush(g){g.enqueue({type:"finish",finishReason:l,usage:c})}})),rawCall:{rawPrompt:o,rawSettings:i},rawResponse:{headers:a},warnings:n,request:{body:r}}}};function ks({parts:e,generateId:t}){const n=e.filter(r=>"functionCall"in r);return n.length===0?void 0:n.map(r=>({toolCallType:"function",toolCallId:t(),toolName:r.functionCall.name,args:JSON.stringify(r.functionCall.args)}))}function Ss(e){const t=e.filter(n=>"text"in n);return t.length===0?void 0:t.map(n=>n.text).join("")}var Ni=M({role:R(),parts:ne(Se([M({text:R()}),M({functionCall:M({name:R(),args:ga()})})]))}),Ig=M({candidates:ne(M({content:Ni,finishReason:R().optional()})),usageMetadata:M({promptTokenCount:V(),candidatesTokenCount:V().nullish(),totalTokenCount:V()}).optional()}),Rg=M({candidates:ne(M({content:Ni.optional(),finishReason:R().optional()})),usageMetadata:M({promptTokenCount:V(),candidatesTokenCount:V().nullish(),totalTokenCount:V()}).optional()}),Ng=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){return 2048}get supportsParallelCalls(){return!0}async doEmbed({values:e,headers:t,abortSignal:n}){if(e.length>this.maxEmbeddingsPerCall)throw new No({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});const{responseHeaders:r,value:a}=await Qe({url:`${this.config.baseURL}/models/${this.modelId}:batchEmbedContents`,headers:Xe(this.config.headers(),t),body:{requests:e.map(s=>({model:`models/${this.modelId}`,content:{role:"user",parts:[{text:s}]},outputDimensionality:this.settings.outputDimensionality}))},failedResponseHandler:Vr,successfulResponseHandler:nn(Pg),abortSignal:n,fetch:this.config.fetch});return{embeddings:a.embeddings.map(s=>s.values),usage:void 0,rawResponse:{headers:r}}}},Pg=M({embeddings:ne(M({values:ne(V())}))});function Pi(e={}){var t,n;const r=(n=Uo((t=e.baseURL)!=null?t:e.baseUrl))!=null?n:"https://generativelanguage.googleapis.com/v1beta",a=()=>({"x-goog-api-key":Lo({apiKey:e.apiKey,environmentVariableName:"GOOGLE_GENERATIVE_AI_API_KEY",description:"Google Generative AI"}),...e.headers}),s=(l,c={})=>{var u;return new Tg(l,c,{provider:"google.generative-ai",baseURL:r,headers:a,generateId:(u=e.generateId)!=null?u:dt,fetch:e.fetch})},o=(l,c={})=>new Ng(l,c,{provider:"google.generative-ai",baseURL:r,headers:a,fetch:e.fetch}),i=function(l,c){if(new.target)throw new Error("The Google Generative AI model function cannot be called with the new keyword.");return s(l,c)};return i.languageModel=s,i.chat=s,i.generativeAI=s,i.embedding=o,i.textEmbedding=o,i.textEmbeddingModel=o,i}Pi();class Og{constructor(){We(this,"name","gemini_1.5_pro");We(this,"apiKey","")}init(t){this.apiKey=t}async generateResponse(t){var n;try{const r=Pi({apiKey:this.apiKey});return{error:null,success:(await Ea({model:r((n=ft.find(s=>s.name===this.name))==null?void 0:n.model),messages:t.messages,systemPrompt:t.systemPrompt,prompt:t.prompt,extractedCode:t.extractedCode})).object}}catch(r){return{error:r,success:null}}}}class jg{constructor(){We(this,"name","openai_4o");We(this,"apiKey","")}init(t){this.apiKey=t}async generateResponse(t){var n;try{const r=ya({compatibility:"strict",apiKey:this.apiKey});return{error:null,success:(await Ea({model:r((n=ft.find(s=>s.name===this.name))==null?void 0:n.model),messages:t.messages,systemPrompt:t.systemPrompt,prompt:t.prompt,extractedCode:t.extractedCode})).object}}catch(r){return{error:r,success:null}}}}const Es={"openai_3.5_turbo":new kg,openai_4o:new jg,"gemini_1.5_pro":new Og};class Mg{constructor(){We(this,"activeModal",null)}selectModal(t,n){if(Es[t])this.activeModal=Es[t],this.activeModal.init(n);else throw new Error(`Modal "${t}" not found`)}async generate(t){if(!this.activeModal)throw new Error("No modal selected");return this.activeModal.generateResponse(t)}}const Dg=e=>e.map(t=>({role:t.role,content:typeof t.content=="string"?t.content:JSON.stringify(t.content)}));function Fg(e,t){return y.useReducer((n,r)=>t[n][r]??n,e)}var Aa="ScrollArea",[Oi,qy]=wt(Aa),[$g,Pe]=Oi(Aa),ji=y.forwardRef((e,t)=>{const{__scopeScrollArea:n,type:r="hover",dir:a,scrollHideDelay:s=600,...o}=e,[i,l]=y.useState(null),[c,u]=y.useState(null),[d,g]=y.useState(null),[v,f]=y.useState(null),[m,h]=y.useState(null),[b,_]=y.useState(0),[w,A]=y.useState(0),[O,N]=y.useState(!1),[F,D]=y.useState(!1),I=xe(t,G=>l(G)),$=Fn(a);return p.jsx($g,{scope:n,type:r,dir:$,scrollHideDelay:s,scrollArea:i,viewport:c,onViewportChange:u,content:d,onContentChange:g,scrollbarX:v,onScrollbarXChange:f,scrollbarXEnabled:O,onScrollbarXEnabledChange:N,scrollbarY:m,onScrollbarYChange:h,scrollbarYEnabled:F,onScrollbarYEnabledChange:D,onCornerWidthChange:_,onCornerHeightChange:A,children:p.jsx(ye.div,{dir:$,...o,ref:I,style:{position:"relative","--radix-scroll-area-corner-width":b+"px","--radix-scroll-area-corner-height":w+"px",...e.style}})})});ji.displayName=Aa;var Mi="ScrollAreaViewport",Di=y.forwardRef((e,t)=>{const{__scopeScrollArea:n,children:r,nonce:a,...s}=e,o=Pe(Mi,n),i=y.useRef(null),l=xe(t,i,o.onViewportChange);return p.jsxs(p.Fragment,{children:[p.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),p.jsx(ye.div,{"data-radix-scroll-area-viewport":"",...s,ref:l,style:{overflowX:o.scrollbarXEnabled?"scroll":"hidden",overflowY:o.scrollbarYEnabled?"scroll":"hidden",...e.style},children:p.jsx("div",{ref:o.onContentChange,style:{minWidth:"100%",display:"table"},children:r})})]})});Di.displayName=Mi;var Ge="ScrollAreaScrollbar",Ca=y.forwardRef((e,t)=>{const{forceMount:n,...r}=e,a=Pe(Ge,e.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:o}=a,i=e.orientation==="horizontal";return y.useEffect(()=>(i?s(!0):o(!0),()=>{i?s(!1):o(!1)}),[i,s,o]),a.type==="hover"?p.jsx(Lg,{...r,ref:t,forceMount:n}):a.type==="scroll"?p.jsx(Bg,{...r,ref:t,forceMount:n}):a.type==="auto"?p.jsx(Fi,{...r,ref:t,forceMount:n}):a.type==="always"?p.jsx(Ta,{...r,ref:t}):null});Ca.displayName=Ge;var Lg=y.forwardRef((e,t)=>{const{forceMount:n,...r}=e,a=Pe(Ge,e.__scopeScrollArea),[s,o]=y.useState(!1);return y.useEffect(()=>{const i=a.scrollArea;let l=0;if(i){const c=()=>{window.clearTimeout(l),o(!0)},u=()=>{l=window.setTimeout(()=>o(!1),a.scrollHideDelay)};return i.addEventListener("pointerenter",c),i.addEventListener("pointerleave",u),()=>{window.clearTimeout(l),i.removeEventListener("pointerenter",c),i.removeEventListener("pointerleave",u)}}},[a.scrollArea,a.scrollHideDelay]),p.jsx(Ze,{present:n||s,children:p.jsx(Fi,{"data-state":s?"visible":"hidden",...r,ref:t})})}),Bg=y.forwardRef((e,t)=>{const{forceMount:n,...r}=e,a=Pe(Ge,e.__scopeScrollArea),s=e.orientation==="horizontal",o=Vn(()=>l("SCROLL_END"),100),[i,l]=Fg("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return y.useEffect(()=>{if(i==="idle"){const c=window.setTimeout(()=>l("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(c)}},[i,a.scrollHideDelay,l]),y.useEffect(()=>{const c=a.viewport,u=s?"scrollLeft":"scrollTop";if(c){let d=c[u];const g=()=>{const v=c[u];d!==v&&(l("SCROLL"),o()),d=v};return c.addEventListener("scroll",g),()=>c.removeEventListener("scroll",g)}},[a.viewport,s,l,o]),p.jsx(Ze,{present:n||i!=="hidden",children:p.jsx(Ta,{"data-state":i==="hidden"?"hidden":"visible",...r,ref:t,onPointerEnter:z(e.onPointerEnter,()=>l("POINTER_ENTER")),onPointerLeave:z(e.onPointerLeave,()=>l("POINTER_LEAVE"))})})}),Fi=y.forwardRef((e,t)=>{const n=Pe(Ge,e.__scopeScrollArea),{forceMount:r,...a}=e,[s,o]=y.useState(!1),i=e.orientation==="horizontal",l=Vn(()=>{if(n.viewport){const c=n.viewport.offsetWidth<n.viewport.scrollWidth,u=n.viewport.offsetHeight<n.viewport.scrollHeight;o(i?c:u)}},10);return _t(n.viewport,l),_t(n.content,l),p.jsx(Ze,{present:r||s,children:p.jsx(Ta,{"data-state":s?"visible":"hidden",...a,ref:t})})}),Ta=y.forwardRef((e,t)=>{const{orientation:n="vertical",...r}=e,a=Pe(Ge,e.__scopeScrollArea),s=y.useRef(null),o=y.useRef(0),[i,l]=y.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=zi(i.viewport,i.content),u={...r,sizes:i,onSizesChange:l,hasThumb:c>0&&c<1,onThumbChange:g=>s.current=g,onThumbPointerUp:()=>o.current=0,onThumbPointerDown:g=>o.current=g};function d(g,v){return qg(g,o.current,i,v)}return n==="horizontal"?p.jsx(Ug,{...u,ref:t,onThumbPositionChange:()=>{if(a.viewport&&s.current){const g=a.viewport.scrollLeft,v=As(g,i,a.dir);s.current.style.transform=`translate3d(${v}px, 0, 0)`}},onWheelScroll:g=>{a.viewport&&(a.viewport.scrollLeft=g)},onDragScroll:g=>{a.viewport&&(a.viewport.scrollLeft=d(g,a.dir))}}):n==="vertical"?p.jsx(zg,{...u,ref:t,onThumbPositionChange:()=>{if(a.viewport&&s.current){const g=a.viewport.scrollTop,v=As(g,i);s.current.style.transform=`translate3d(0, ${v}px, 0)`}},onWheelScroll:g=>{a.viewport&&(a.viewport.scrollTop=g)},onDragScroll:g=>{a.viewport&&(a.viewport.scrollTop=d(g))}}):null}),Ug=y.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...a}=e,s=Pe(Ge,e.__scopeScrollArea),[o,i]=y.useState(),l=y.useRef(null),c=xe(t,l,s.onScrollbarXChange);return y.useEffect(()=>{l.current&&i(getComputedStyle(l.current))},[l]),p.jsx(Li,{"data-orientation":"horizontal",...a,ref:c,sizes:n,style:{bottom:0,left:s.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:s.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Gn(n)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.x),onDragScroll:u=>e.onDragScroll(u.x),onWheelScroll:(u,d)=>{if(s.viewport){const g=s.viewport.scrollLeft+u.deltaX;e.onWheelScroll(g),Gi(g,d)&&u.preventDefault()}},onResize:()=>{l.current&&s.viewport&&o&&r({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:jn(o.paddingLeft),paddingEnd:jn(o.paddingRight)}})}})}),zg=y.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...a}=e,s=Pe(Ge,e.__scopeScrollArea),[o,i]=y.useState(),l=y.useRef(null),c=xe(t,l,s.onScrollbarYChange);return y.useEffect(()=>{l.current&&i(getComputedStyle(l.current))},[l]),p.jsx(Li,{"data-orientation":"vertical",...a,ref:c,sizes:n,style:{top:0,right:s.dir==="ltr"?0:void 0,left:s.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Gn(n)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.y),onDragScroll:u=>e.onDragScroll(u.y),onWheelScroll:(u,d)=>{if(s.viewport){const g=s.viewport.scrollTop+u.deltaY;e.onWheelScroll(g),Gi(g,d)&&u.preventDefault()}},onResize:()=>{l.current&&s.viewport&&o&&r({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:jn(o.paddingTop),paddingEnd:jn(o.paddingBottom)}})}})}),[Zg,$i]=Oi(Ge),Li=y.forwardRef((e,t)=>{const{__scopeScrollArea:n,sizes:r,hasThumb:a,onThumbChange:s,onThumbPointerUp:o,onThumbPointerDown:i,onThumbPositionChange:l,onDragScroll:c,onWheelScroll:u,onResize:d,...g}=e,v=Pe(Ge,n),[f,m]=y.useState(null),h=xe(t,I=>m(I)),b=y.useRef(null),_=y.useRef(""),w=v.viewport,A=r.content-r.viewport,O=Me(u),N=Me(l),F=Vn(d,10);function D(I){if(b.current){const $=I.clientX-b.current.left,G=I.clientY-b.current.top;c({x:$,y:G})}}return y.useEffect(()=>{const I=$=>{const G=$.target;(f==null?void 0:f.contains(G))&&O($,A)};return document.addEventListener("wheel",I,{passive:!1}),()=>document.removeEventListener("wheel",I,{passive:!1})},[w,f,A,O]),y.useEffect(N,[r,N]),_t(f,F),_t(v.content,F),p.jsx(Zg,{scope:n,scrollbar:f,hasThumb:a,onThumbChange:Me(s),onThumbPointerUp:Me(o),onThumbPositionChange:N,onThumbPointerDown:Me(i),children:p.jsx(ye.div,{...g,ref:h,style:{position:"absolute",...g.style},onPointerDown:z(e.onPointerDown,I=>{I.button===0&&(I.target.setPointerCapture(I.pointerId),b.current=f.getBoundingClientRect(),_.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",v.viewport&&(v.viewport.style.scrollBehavior="auto"),D(I))}),onPointerMove:z(e.onPointerMove,D),onPointerUp:z(e.onPointerUp,I=>{const $=I.target;$.hasPointerCapture(I.pointerId)&&$.releasePointerCapture(I.pointerId),document.body.style.webkitUserSelect=_.current,v.viewport&&(v.viewport.style.scrollBehavior=""),b.current=null})})})}),On="ScrollAreaThumb",Bi=y.forwardRef((e,t)=>{const{forceMount:n,...r}=e,a=$i(On,e.__scopeScrollArea);return p.jsx(Ze,{present:n||a.hasThumb,children:p.jsx(Gg,{ref:t,...r})})}),Gg=y.forwardRef((e,t)=>{const{__scopeScrollArea:n,style:r,...a}=e,s=Pe(On,n),o=$i(On,n),{onThumbPositionChange:i}=o,l=xe(t,d=>o.onThumbChange(d)),c=y.useRef(void 0),u=Vn(()=>{c.current&&(c.current(),c.current=void 0)},100);return y.useEffect(()=>{const d=s.viewport;if(d){const g=()=>{if(u(),!c.current){const v=Hg(d,i);c.current=v,i()}};return i(),d.addEventListener("scroll",g),()=>d.removeEventListener("scroll",g)}},[s.viewport,u,i]),p.jsx(ye.div,{"data-state":o.hasThumb?"visible":"hidden",...a,ref:l,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...r},onPointerDownCapture:z(e.onPointerDownCapture,d=>{const v=d.target.getBoundingClientRect(),f=d.clientX-v.left,m=d.clientY-v.top;o.onThumbPointerDown({x:f,y:m})}),onPointerUp:z(e.onPointerUp,o.onThumbPointerUp)})});Bi.displayName=On;var Ia="ScrollAreaCorner",Ui=y.forwardRef((e,t)=>{const n=Pe(Ia,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&r?p.jsx(Vg,{...e,ref:t}):null});Ui.displayName=Ia;var Vg=y.forwardRef((e,t)=>{const{__scopeScrollArea:n,...r}=e,a=Pe(Ia,n),[s,o]=y.useState(0),[i,l]=y.useState(0),c=!!(s&&i);return _t(a.scrollbarX,()=>{var d;const u=((d=a.scrollbarX)==null?void 0:d.offsetHeight)||0;a.onCornerHeightChange(u),l(u)}),_t(a.scrollbarY,()=>{var d;const u=((d=a.scrollbarY)==null?void 0:d.offsetWidth)||0;a.onCornerWidthChange(u),o(u)}),c?p.jsx(ye.div,{...r,ref:t,style:{width:s,height:i,position:"absolute",right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function jn(e){return e?parseInt(e,10):0}function zi(e,t){const n=e/t;return isNaN(n)?0:n}function Gn(e){const t=zi(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=(e.scrollbar.size-n)*t;return Math.max(r,18)}function qg(e,t,n,r="ltr"){const a=Gn(n),s=a/2,o=t||s,i=a-o,l=n.scrollbar.paddingStart+o,c=n.scrollbar.size-n.scrollbar.paddingEnd-i,u=n.content-n.viewport,d=r==="ltr"?[0,u]:[u*-1,0];return Zi([l,c],d)(e)}function As(e,t,n="ltr"){const r=Gn(t),a=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,s=t.scrollbar.size-a,o=t.content-t.viewport,i=s-r,l=n==="ltr"?[0,o]:[o*-1,0],c=cc(e,l);return Zi([0,o],[0,i])(c)}function Zi(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function Gi(e,t){return e>0&&e<t}var Hg=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return function a(){const s={left:e.scrollLeft,top:e.scrollTop},o=n.left!==s.left,i=n.top!==s.top;(o||i)&&t(),n=s,r=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(r)};function Vn(e,t){const n=Me(e),r=y.useRef(0);return y.useEffect(()=>()=>window.clearTimeout(r.current),[]),y.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function _t(e,t){const n=Me(t);fn(()=>{let r=0;if(e){const a=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(n)});return a.observe(e),()=>{window.cancelAnimationFrame(r),a.unobserve(e)}}},[e,n])}var Vi=ji,Wg=Di,Kg=Ui;const qi=y.forwardRef(({className:e,children:t,...n},r)=>p.jsxs(Vi,{ref:r,className:me("relative overflow-hidden",e),...n,children:[p.jsx(Wg,{className:"h-full w-full rounded-[inherit]",children:t}),p.jsx(Hi,{}),p.jsx(Kg,{})]}));qi.displayName=Vi.displayName;const Hi=y.forwardRef(({className:e,orientation:t="vertical",...n},r)=>p.jsx(Ca,{ref:r,orientation:t,className:me("flex touch-none select-none transition-colors",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...n,children:p.jsx(Bi,{className:"relative flex-1 rounded-full bg-border"})}));Hi.displayName=Ca.displayName;const qr=(e,t)=>t.some(n=>e instanceof n);let Cs,Ts;function Yg(){return Cs||(Cs=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function Jg(){return Ts||(Ts=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}const Hr=new WeakMap,or=new WeakMap,qn=new WeakMap;function Xg(e){const t=new Promise((n,r)=>{const a=()=>{e.removeEventListener("success",s),e.removeEventListener("error",o)},s=()=>{n(st(e.result)),a()},o=()=>{r(e.error),a()};e.addEventListener("success",s),e.addEventListener("error",o)});return qn.set(t,e),t}function Qg(e){if(Hr.has(e))return;const t=new Promise((n,r)=>{const a=()=>{e.removeEventListener("complete",s),e.removeEventListener("error",o),e.removeEventListener("abort",o)},s=()=>{n(),a()},o=()=>{r(e.error||new DOMException("AbortError","AbortError")),a()};e.addEventListener("complete",s),e.addEventListener("error",o),e.addEventListener("abort",o)});Hr.set(e,t)}let Wr={get(e,t,n){if(e instanceof IDBTransaction){if(t==="done")return Hr.get(e);if(t==="store")return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return st(e[t])},set(e,t,n){return e[t]=n,!0},has(e,t){return e instanceof IDBTransaction&&(t==="done"||t==="store")?!0:t in e}};function Wi(e){Wr=e(Wr)}function eh(e){return Jg().includes(e)?function(...t){return e.apply(Kr(this),t),st(this.request)}:function(...t){return st(e.apply(Kr(this),t))}}function th(e){return typeof e=="function"?eh(e):(e instanceof IDBTransaction&&Qg(e),qr(e,Yg())?new Proxy(e,Wr):e)}function st(e){if(e instanceof IDBRequest)return Xg(e);if(or.has(e))return or.get(e);const t=th(e);return t!==e&&(or.set(e,t),qn.set(t,e)),t}const Kr=e=>qn.get(e);function nh(e,t,{blocked:n,upgrade:r,blocking:a,terminated:s}={}){const o=indexedDB.open(e,t),i=st(o);return r&&o.addEventListener("upgradeneeded",l=>{r(st(o.result),l.oldVersion,l.newVersion,st(o.transaction),l)}),n&&o.addEventListener("blocked",l=>n(l.oldVersion,l.newVersion,l)),i.then(l=>{s&&l.addEventListener("close",()=>s()),a&&l.addEventListener("versionchange",c=>a(c.oldVersion,c.newVersion,c))}).catch(()=>{}),i}const rh=["get","getKey","getAll","getAllKeys","count"],ah=["put","add","delete","clear"],ir=new Map;function Is(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&typeof t=="string"))return;if(ir.get(t))return ir.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,a=ah.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!(a||rh.includes(n)))return;const s=async function(o,...i){const l=this.transaction(o,a?"readwrite":"readonly");let c=l.store;return r&&(c=c.index(i.shift())),(await Promise.all([c[n](...i),a&&l.done]))[0]};return ir.set(t,s),s}Wi(e=>({...e,get:(t,n,r)=>Is(t,n)||e.get(t,n,r),has:(t,n)=>!!Is(t,n)||e.has(t,n)}));const sh=["continue","continuePrimaryKey","advance"],Rs={},Yr=new WeakMap,Ki=new WeakMap,oh={get(e,t){if(!sh.includes(t))return e[t];let n=Rs[t];return n||(n=Rs[t]=function(...r){Yr.set(this,Ki.get(this)[t](...r))}),n}};async function*ih(...e){let t=this;if(t instanceof IDBCursor||(t=await t.openCursor(...e)),!t)return;t=t;const n=new Proxy(t,oh);for(Ki.set(n,t),qn.set(n,Kr(t));t;)yield n,t=await(Yr.get(n)||t.continue()),Yr.delete(n)}function Ns(e,t){return t===Symbol.asyncIterator&&qr(e,[IDBIndex,IDBObjectStore,IDBCursor])||t==="iterate"&&qr(e,[IDBIndex,IDBObjectStore])}Wi(e=>({...e,get(t,n,r){return Ns(t,n)?ih:e.get(t,n,r)},has(t,n){return Ns(t,n)||e.has(t,n)}}));const Ra=nh("chat-db",1,{upgrade(e){e.createObjectStore("chats",{keyPath:"problemName"})}}),lh=async(e,t)=>{await(await Ra).put("chats",{problemName:e,chatHistory:t})},ch=async(e,t,n)=>{const a=await(await Ra).get("chats",e);if(!a)return{totalMessageCount:0,chatHistory:[]};const{chatHistory:s}=a,o=s.length,i=s.slice(Math.max(o-n-t,0),o-n);return{totalMessageCount:o,chatHistory:i,allChatHistory:s||[]}},uh=async e=>{await(await Ra).delete("chats",e)},dn=10,Ps=()=>({saveChatHistory:async(e,t)=>{await lh(e,t)},fetchChatHistory:async(e,t,n)=>await ch(e,t,n),clearChatHistory:async e=>{await uh(e)}});var lr="rovingFocusGroup.onEntryFocus",dh={bubbles:!1,cancelable:!0},rn="RovingFocusGroup",[Jr,Yi,ph]=ea(rn),[fh,Ji]=wt(rn,[ph]),[mh,gh]=fh(rn),Xi=y.forwardRef((e,t)=>p.jsx(Jr.Provider,{scope:e.__scopeRovingFocusGroup,children:p.jsx(Jr.Slot,{scope:e.__scopeRovingFocusGroup,children:p.jsx(hh,{...e,ref:t})})}));Xi.displayName=rn;var hh=y.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:a=!1,dir:s,currentTabStopId:o,defaultCurrentTabStopId:i,onCurrentTabStopIdChange:l,onEntryFocus:c,preventScrollOnEntryFocus:u=!1,...d}=e,g=y.useRef(null),v=xe(t,g),f=Fn(s),[m,h]=xt({prop:o,defaultProp:i??null,onChange:l,caller:rn}),[b,_]=y.useState(!1),w=Me(c),A=Yi(n),O=y.useRef(!1),[N,F]=y.useState(0);return y.useEffect(()=>{const D=g.current;if(D)return D.addEventListener(lr,w),()=>D.removeEventListener(lr,w)},[w]),p.jsx(mh,{scope:n,orientation:r,dir:f,loop:a,currentTabStopId:m,onItemFocus:y.useCallback(D=>h(D),[h]),onItemShiftTab:y.useCallback(()=>_(!0),[]),onFocusableItemAdd:y.useCallback(()=>F(D=>D+1),[]),onFocusableItemRemove:y.useCallback(()=>F(D=>D-1),[]),children:p.jsx(ye.div,{tabIndex:b||N===0?-1:0,"data-orientation":r,...d,ref:v,style:{outline:"none",...e.style},onMouseDown:z(e.onMouseDown,()=>{O.current=!0}),onFocus:z(e.onFocus,D=>{const I=!O.current;if(D.target===D.currentTarget&&I&&!b){const $=new CustomEvent(lr,dh);if(D.currentTarget.dispatchEvent($),!$.defaultPrevented){const G=A().filter(le=>le.focusable),J=G.find(le=>le.active),he=G.find(le=>le.id===m),ue=[J,he,...G].filter(Boolean).map(le=>le.ref.current);tl(ue,u)}}O.current=!1}),onBlur:z(e.onBlur,()=>_(!1))})})}),Qi="RovingFocusGroupItem",el=y.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:a=!1,tabStopId:s,children:o,...i}=e,l=ot(),c=s||l,u=gh(Qi,n),d=u.currentTabStopId===c,g=Yi(n),{onFocusableItemAdd:v,onFocusableItemRemove:f,currentTabStopId:m}=u;return y.useEffect(()=>{if(r)return v(),()=>f()},[r,v,f]),p.jsx(Jr.ItemSlot,{scope:n,id:c,focusable:r,active:a,children:p.jsx(ye.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...i,ref:t,onMouseDown:z(e.onMouseDown,h=>{r?u.onItemFocus(c):h.preventDefault()}),onFocus:z(e.onFocus,()=>u.onItemFocus(c)),onKeyDown:z(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){u.onItemShiftTab();return}if(h.target!==h.currentTarget)return;const b=bh(h,u.orientation,u.dir);if(b!==void 0){if(h.metaKey||h.ctrlKey||h.altKey||h.shiftKey)return;h.preventDefault();let w=g().filter(A=>A.focusable).map(A=>A.ref.current);if(b==="last")w.reverse();else if(b==="prev"||b==="next"){b==="prev"&&w.reverse();const A=w.indexOf(h.currentTarget);w=u.loop?_h(w,A+1):w.slice(A+1)}setTimeout(()=>tl(w))}}),children:typeof o=="function"?o({isCurrentTabStop:d,hasTabStop:m!=null}):o})})});el.displayName=Qi;var yh={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function vh(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function bh(e,t,n){const r=vh(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return yh[r]}function tl(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function _h(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var wh=Xi,xh=el,Xr=["Enter"," "],kh=["ArrowDown","PageUp","Home"],nl=["ArrowUp","PageDown","End"],Sh=[...kh,...nl],Eh={ltr:[...Xr,"ArrowRight"],rtl:[...Xr,"ArrowLeft"]},Ah={ltr:["ArrowLeft"],rtl:["ArrowRight"]},an="Menu",[Qt,Ch,Th]=ea(an),[lt,rl]=wt(an,[Th,Ms,Ji]),sn=Ms(),al=Ji(),[sl,nt]=lt(an),[Ih,on]=lt(an),ol=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:a,onOpenChange:s,modal:o=!0}=e,i=sn(t),[l,c]=y.useState(null),u=y.useRef(!1),d=Me(s),g=Fn(a);return y.useEffect(()=>{const v=()=>{u.current=!0,document.addEventListener("pointerdown",f,{capture:!0,once:!0}),document.addEventListener("pointermove",f,{capture:!0,once:!0})},f=()=>u.current=!1;return document.addEventListener("keydown",v,{capture:!0}),()=>{document.removeEventListener("keydown",v,{capture:!0}),document.removeEventListener("pointerdown",f,{capture:!0}),document.removeEventListener("pointermove",f,{capture:!0})}},[]),p.jsx(Fs,{...i,children:p.jsx(sl,{scope:t,open:n,onOpenChange:d,content:l,onContentChange:c,children:p.jsx(Ih,{scope:t,onClose:y.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:u,dir:g,modal:o,children:r})})})};ol.displayName=an;var Rh="MenuAnchor",Na=y.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,a=sn(n);return p.jsx(dc,{...a,...r,ref:t})});Na.displayName=Rh;var Pa="MenuPortal",[Nh,il]=lt(Pa,{forceMount:void 0}),ll=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:a}=e,s=nt(Pa,t);return p.jsx(Nh,{scope:t,forceMount:n,children:p.jsx(Ze,{present:n||s.open,children:p.jsx(uc,{asChild:!0,container:a,children:r})})})};ll.displayName=Pa;var Re="MenuContent",[Ph,Oa]=lt(Re),cl=y.forwardRef((e,t)=>{const n=il(Re,e.__scopeMenu),{forceMount:r=n.forceMount,...a}=e,s=nt(Re,e.__scopeMenu),o=on(Re,e.__scopeMenu);return p.jsx(Qt.Provider,{scope:e.__scopeMenu,children:p.jsx(Ze,{present:r||s.open,children:p.jsx(Qt.Slot,{scope:e.__scopeMenu,children:o.modal?p.jsx(Oh,{...a,ref:t}):p.jsx(jh,{...a,ref:t})})})})}),Oh=y.forwardRef((e,t)=>{const n=nt(Re,e.__scopeMenu),r=y.useRef(null),a=xe(t,r);return y.useEffect(()=>{const s=r.current;if(s)return vc(s)},[]),p.jsx(ja,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:z(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),jh=y.forwardRef((e,t)=>{const n=nt(Re,e.__scopeMenu);return p.jsx(ja,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Mh=mc("MenuContent.ScrollLock"),ja=y.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:a,onOpenAutoFocus:s,onCloseAutoFocus:o,disableOutsidePointerEvents:i,onEntryFocus:l,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:g,onDismiss:v,disableOutsideScroll:f,...m}=e,h=nt(Re,n),b=on(Re,n),_=sn(n),w=al(n),A=Ch(n),[O,N]=y.useState(null),F=y.useRef(null),D=xe(t,F,h.onContentChange),I=y.useRef(0),$=y.useRef(""),G=y.useRef(0),J=y.useRef(null),he=y.useRef("right"),X=y.useRef(0),ue=f?fc:y.Fragment,le=f?{as:Mh,allowPinchZoom:!0}:void 0,de=C=>{var se,be;const L=$.current+C,W=A().filter(K=>!K.disabled),B=document.activeElement,te=(se=W.find(K=>K.ref.current===B))==null?void 0:se.textValue,oe=W.map(K=>K.textValue),ae=Hh(oe,L,te),pe=(be=W.find(K=>K.textValue===ae))==null?void 0:be.ref.current;(function K(Q){$.current=Q,window.clearTimeout(I.current),Q!==""&&(I.current=window.setTimeout(()=>K(""),1e3))})(L),pe&&setTimeout(()=>pe.focus())};y.useEffect(()=>()=>window.clearTimeout(I.current),[]),pc();const E=y.useCallback(C=>{var W,B;return he.current===((W=J.current)==null?void 0:W.side)&&Kh(C,(B=J.current)==null?void 0:B.area)},[]);return p.jsx(Ph,{scope:n,searchRef:$,onItemEnter:y.useCallback(C=>{E(C)&&C.preventDefault()},[E]),onItemLeave:y.useCallback(C=>{var L;E(C)||((L=F.current)==null||L.focus(),N(null))},[E]),onTriggerLeave:y.useCallback(C=>{E(C)&&C.preventDefault()},[E]),pointerGraceTimerRef:G,onPointerGraceIntentChange:y.useCallback(C=>{J.current=C},[]),children:p.jsx(ue,{...le,children:p.jsx(gc,{asChild:!0,trapped:a,onMountAutoFocus:z(s,C=>{var L;C.preventDefault(),(L=F.current)==null||L.focus({preventScroll:!0})}),onUnmountAutoFocus:o,children:p.jsx(hc,{asChild:!0,disableOutsidePointerEvents:i,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:g,onDismiss:v,children:p.jsx(wh,{asChild:!0,...w,dir:b.dir,orientation:"vertical",loop:r,currentTabStopId:O,onCurrentTabStopIdChange:N,onEntryFocus:z(l,C=>{b.isUsingKeyboardRef.current||C.preventDefault()}),preventScrollOnEntryFocus:!0,children:p.jsx(yc,{role:"menu","aria-orientation":"vertical","data-state":Al(h.open),"data-radix-menu-content":"",dir:b.dir,..._,...m,ref:D,style:{outline:"none",...m.style},onKeyDown:z(m.onKeyDown,C=>{const W=C.target.closest("[data-radix-menu-content]")===C.currentTarget,B=C.ctrlKey||C.altKey||C.metaKey,te=C.key.length===1;W&&(C.key==="Tab"&&C.preventDefault(),!B&&te&&de(C.key));const oe=F.current;if(C.target!==oe||!Sh.includes(C.key))return;C.preventDefault();const pe=A().filter(se=>!se.disabled).map(se=>se.ref.current);nl.includes(C.key)&&pe.reverse(),Vh(pe)}),onBlur:z(e.onBlur,C=>{C.currentTarget.contains(C.target)||(window.clearTimeout(I.current),$.current="")}),onPointerMove:z(e.onPointerMove,en(C=>{const L=C.target,W=X.current!==C.clientX;if(C.currentTarget.contains(L)&&W){const B=C.clientX>X.current?"right":"left";he.current=B,X.current=C.clientX}}))})})})})})})});cl.displayName=Re;var Dh="MenuGroup",Ma=y.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return p.jsx(ye.div,{role:"group",...r,ref:t})});Ma.displayName=Dh;var Fh="MenuLabel",ul=y.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return p.jsx(ye.div,{...r,ref:t})});ul.displayName=Fh;var Mn="MenuItem",Os="menu.itemSelect",Hn=y.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...a}=e,s=y.useRef(null),o=on(Mn,e.__scopeMenu),i=Oa(Mn,e.__scopeMenu),l=xe(t,s),c=y.useRef(!1),u=()=>{const d=s.current;if(!n&&d){const g=new CustomEvent(Os,{bubbles:!0,cancelable:!0});d.addEventListener(Os,v=>r==null?void 0:r(v),{once:!0}),bc(d,g),g.defaultPrevented?c.current=!1:o.onClose()}};return p.jsx(dl,{...a,ref:l,disabled:n,onClick:z(e.onClick,u),onPointerDown:d=>{var g;(g=e.onPointerDown)==null||g.call(e,d),c.current=!0},onPointerUp:z(e.onPointerUp,d=>{var g;c.current||(g=d.currentTarget)==null||g.click()}),onKeyDown:z(e.onKeyDown,d=>{const g=i.searchRef.current!=="";n||g&&d.key===" "||Xr.includes(d.key)&&(d.currentTarget.click(),d.preventDefault())})})});Hn.displayName=Mn;var dl=y.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:a,...s}=e,o=Oa(Mn,n),i=al(n),l=y.useRef(null),c=xe(t,l),[u,d]=y.useState(!1),[g,v]=y.useState("");return y.useEffect(()=>{const f=l.current;f&&v((f.textContent??"").trim())},[s.children]),p.jsx(Qt.ItemSlot,{scope:n,disabled:r,textValue:a??g,children:p.jsx(xh,{asChild:!0,...i,focusable:!r,children:p.jsx(ye.div,{role:"menuitem","data-highlighted":u?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...s,ref:c,onPointerMove:z(e.onPointerMove,en(f=>{r?o.onItemLeave(f):(o.onItemEnter(f),f.defaultPrevented||f.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:z(e.onPointerLeave,en(f=>o.onItemLeave(f))),onFocus:z(e.onFocus,()=>d(!0)),onBlur:z(e.onBlur,()=>d(!1))})})})}),$h="MenuCheckboxItem",pl=y.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...a}=e;return p.jsx(yl,{scope:e.__scopeMenu,checked:n,children:p.jsx(Hn,{role:"menuitemcheckbox","aria-checked":Dn(n)?"mixed":n,...a,ref:t,"data-state":$a(n),onSelect:z(a.onSelect,()=>r==null?void 0:r(Dn(n)?!0:!n),{checkForDefaultPrevented:!1})})})});pl.displayName=$h;var fl="MenuRadioGroup",[Lh,Bh]=lt(fl,{value:void 0,onValueChange:()=>{}}),ml=y.forwardRef((e,t)=>{const{value:n,onValueChange:r,...a}=e,s=Me(r);return p.jsx(Lh,{scope:e.__scopeMenu,value:n,onValueChange:s,children:p.jsx(Ma,{...a,ref:t})})});ml.displayName=fl;var gl="MenuRadioItem",hl=y.forwardRef((e,t)=>{const{value:n,...r}=e,a=Bh(gl,e.__scopeMenu),s=n===a.value;return p.jsx(yl,{scope:e.__scopeMenu,checked:s,children:p.jsx(Hn,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":$a(s),onSelect:z(r.onSelect,()=>{var o;return(o=a.onValueChange)==null?void 0:o.call(a,n)},{checkForDefaultPrevented:!1})})})});hl.displayName=gl;var Da="MenuItemIndicator",[yl,Uh]=lt(Da,{checked:!1}),vl=y.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...a}=e,s=Uh(Da,n);return p.jsx(Ze,{present:r||Dn(s.checked)||s.checked===!0,children:p.jsx(ye.span,{...a,ref:t,"data-state":$a(s.checked)})})});vl.displayName=Da;var zh="MenuSeparator",bl=y.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return p.jsx(ye.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});bl.displayName=zh;var Zh="MenuArrow",_l=y.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,a=sn(n);return p.jsx(_c,{...a,...r,ref:t})});_l.displayName=Zh;var Fa="MenuSub",[Gh,wl]=lt(Fa),xl=e=>{const{__scopeMenu:t,children:n,open:r=!1,onOpenChange:a}=e,s=nt(Fa,t),o=sn(t),[i,l]=y.useState(null),[c,u]=y.useState(null),d=Me(a);return y.useEffect(()=>(s.open===!1&&d(!1),()=>d(!1)),[s.open,d]),p.jsx(Fs,{...o,children:p.jsx(sl,{scope:t,open:r,onOpenChange:d,content:c,onContentChange:u,children:p.jsx(Gh,{scope:t,contentId:ot(),triggerId:ot(),trigger:i,onTriggerChange:l,children:n})})})};xl.displayName=Fa;var Ct="MenuSubTrigger",kl=y.forwardRef((e,t)=>{const n=nt(Ct,e.__scopeMenu),r=on(Ct,e.__scopeMenu),a=wl(Ct,e.__scopeMenu),s=Oa(Ct,e.__scopeMenu),o=y.useRef(null),{pointerGraceTimerRef:i,onPointerGraceIntentChange:l}=s,c={__scopeMenu:e.__scopeMenu},u=y.useCallback(()=>{o.current&&window.clearTimeout(o.current),o.current=null},[]);return y.useEffect(()=>u,[u]),y.useEffect(()=>{const d=i.current;return()=>{window.clearTimeout(d),l(null)}},[i,l]),p.jsx(Na,{asChild:!0,...c,children:p.jsx(dl,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":Al(n.open),...e,ref:Ds(t,a.onTriggerChange),onClick:d=>{var g;(g=e.onClick)==null||g.call(e,d),!(e.disabled||d.defaultPrevented)&&(d.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:z(e.onPointerMove,en(d=>{s.onItemEnter(d),!d.defaultPrevented&&!e.disabled&&!n.open&&!o.current&&(s.onPointerGraceIntentChange(null),o.current=window.setTimeout(()=>{n.onOpenChange(!0),u()},100))})),onPointerLeave:z(e.onPointerLeave,en(d=>{var v,f;u();const g=(v=n.content)==null?void 0:v.getBoundingClientRect();if(g){const m=(f=n.content)==null?void 0:f.dataset.side,h=m==="right",b=h?-5:5,_=g[h?"left":"right"],w=g[h?"right":"left"];s.onPointerGraceIntentChange({area:[{x:d.clientX+b,y:d.clientY},{x:_,y:g.top},{x:w,y:g.top},{x:w,y:g.bottom},{x:_,y:g.bottom}],side:m}),window.clearTimeout(i.current),i.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(d),d.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:z(e.onKeyDown,d=>{var v;const g=s.searchRef.current!=="";e.disabled||g&&d.key===" "||Eh[r.dir].includes(d.key)&&(n.onOpenChange(!0),(v=n.content)==null||v.focus(),d.preventDefault())})})})});kl.displayName=Ct;var Sl="MenuSubContent",El=y.forwardRef((e,t)=>{const n=il(Re,e.__scopeMenu),{forceMount:r=n.forceMount,...a}=e,s=nt(Re,e.__scopeMenu),o=on(Re,e.__scopeMenu),i=wl(Sl,e.__scopeMenu),l=y.useRef(null),c=xe(t,l);return p.jsx(Qt.Provider,{scope:e.__scopeMenu,children:p.jsx(Ze,{present:r||s.open,children:p.jsx(Qt.Slot,{scope:e.__scopeMenu,children:p.jsx(ja,{id:i.contentId,"aria-labelledby":i.triggerId,...a,ref:c,align:"start",side:o.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:u=>{var d;o.isUsingKeyboardRef.current&&((d=l.current)==null||d.focus()),u.preventDefault()},onCloseAutoFocus:u=>u.preventDefault(),onFocusOutside:z(e.onFocusOutside,u=>{u.target!==i.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:z(e.onEscapeKeyDown,u=>{o.onClose(),u.preventDefault()}),onKeyDown:z(e.onKeyDown,u=>{var v;const d=u.currentTarget.contains(u.target),g=Ah[o.dir].includes(u.key);d&&g&&(s.onOpenChange(!1),(v=i.trigger)==null||v.focus(),u.preventDefault())})})})})})});El.displayName=Sl;function Al(e){return e?"open":"closed"}function Dn(e){return e==="indeterminate"}function $a(e){return Dn(e)?"indeterminate":e?"checked":"unchecked"}function Vh(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function qh(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Hh(e,t,n){const a=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let o=qh(e,Math.max(s,0));a.length===1&&(o=o.filter(c=>c!==n));const l=o.find(c=>c.toLowerCase().startsWith(a.toLowerCase()));return l!==n?l:void 0}function Wh(e,t){const{x:n,y:r}=e;let a=!1;for(let s=0,o=t.length-1;s<t.length;o=s++){const i=t[s],l=t[o],c=i.x,u=i.y,d=l.x,g=l.y;u>r!=g>r&&n<(d-c)*(r-u)/(g-u)+c&&(a=!a)}return a}function Kh(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Wh(n,t)}function en(e){return t=>t.pointerType==="mouse"?e(t):void 0}var Yh=ol,Jh=Na,Xh=ll,Qh=cl,ey=Ma,ty=ul,ny=Hn,ry=pl,ay=ml,sy=hl,oy=vl,iy=bl,ly=_l,cy=xl,uy=kl,dy=El,Wn="DropdownMenu",[py,Hy]=wt(Wn,[rl]),ve=rl(),[fy,Cl]=py(Wn),Tl=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:a,defaultOpen:s,onOpenChange:o,modal:i=!0}=e,l=ve(t),c=y.useRef(null),[u,d]=xt({prop:a,defaultProp:s??!1,onChange:o,caller:Wn});return p.jsx(fy,{scope:t,triggerId:ot(),triggerRef:c,contentId:ot(),open:u,onOpenChange:d,onOpenToggle:y.useCallback(()=>d(g=>!g),[d]),modal:i,children:p.jsx(Yh,{...l,open:u,onOpenChange:d,dir:r,modal:i,children:n})})};Tl.displayName=Wn;var Il="DropdownMenuTrigger",Rl=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,s=Cl(Il,n),o=ve(n);return p.jsx(Jh,{asChild:!0,...o,children:p.jsx(ye.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:Ds(t,s.triggerRef),onPointerDown:z(e.onPointerDown,i=>{!r&&i.button===0&&i.ctrlKey===!1&&(s.onOpenToggle(),s.open||i.preventDefault())}),onKeyDown:z(e.onKeyDown,i=>{r||(["Enter"," "].includes(i.key)&&s.onOpenToggle(),i.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(i.key)&&i.preventDefault())})})})});Rl.displayName=Il;var my="DropdownMenuPortal",Nl=e=>{const{__scopeDropdownMenu:t,...n}=e,r=ve(t);return p.jsx(Xh,{...r,...n})};Nl.displayName=my;var Pl="DropdownMenuContent",Ol=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=Cl(Pl,n),s=ve(n),o=y.useRef(!1);return p.jsx(Qh,{id:a.contentId,"aria-labelledby":a.triggerId,...s,...r,ref:t,onCloseAutoFocus:z(e.onCloseAutoFocus,i=>{var l;o.current||(l=a.triggerRef.current)==null||l.focus(),o.current=!1,i.preventDefault()}),onInteractOutside:z(e.onInteractOutside,i=>{const l=i.detail.originalEvent,c=l.button===0&&l.ctrlKey===!0,u=l.button===2||c;(!a.modal||u)&&(o.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ol.displayName=Pl;var gy="DropdownMenuGroup",jl=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(ey,{...a,...r,ref:t})});jl.displayName=gy;var hy="DropdownMenuLabel",Ml=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(ty,{...a,...r,ref:t})});Ml.displayName=hy;var yy="DropdownMenuItem",Dl=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(ny,{...a,...r,ref:t})});Dl.displayName=yy;var vy="DropdownMenuCheckboxItem",Fl=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(ry,{...a,...r,ref:t})});Fl.displayName=vy;var by="DropdownMenuRadioGroup",$l=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(ay,{...a,...r,ref:t})});$l.displayName=by;var _y="DropdownMenuRadioItem",Ll=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(sy,{...a,...r,ref:t})});Ll.displayName=_y;var wy="DropdownMenuItemIndicator",Bl=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(oy,{...a,...r,ref:t})});Bl.displayName=wy;var xy="DropdownMenuSeparator",Ul=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(iy,{...a,...r,ref:t})});Ul.displayName=xy;var ky="DropdownMenuArrow",Sy=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(ly,{...a,...r,ref:t})});Sy.displayName=ky;var Ey=e=>{const{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:a,defaultOpen:s}=e,o=ve(t),[i,l]=xt({prop:r,defaultProp:s??!1,onChange:a,caller:"DropdownMenuSub"});return p.jsx(cy,{...o,open:i,onOpenChange:l,children:n})},Ay="DropdownMenuSubTrigger",zl=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(uy,{...a,...r,ref:t})});zl.displayName=Ay;var Cy="DropdownMenuSubContent",Zl=y.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,a=ve(n);return p.jsx(dy,{...a,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Zl.displayName=Cy;var Ty=Tl,Iy=Rl,Gl=Nl,Vl=Ol,Ry=jl,ql=Ml,Hl=Dl,Wl=Fl,Ny=$l,Kl=Ll,Yl=Bl,Jl=Ul,Py=Ey,Xl=zl,Ql=Zl;const Oy=Ty,jy=Iy,My=Ry,Dy=Gl,Fy=Py,$y=Ny,ec=y.forwardRef(({className:e,inset:t,children:n,...r},a)=>p.jsxs(Xl,{ref:a,className:me("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...r,children:[n,p.jsx(Dc,{className:"ml-auto"})]}));ec.displayName=Xl.displayName;const tc=y.forwardRef(({className:e,...t},n)=>p.jsx(Ql,{ref:n,className:me("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));tc.displayName=Ql.displayName;const nc=y.forwardRef(({className:e,sideOffset:t=4,...n},r)=>p.jsx(Gl,{children:p.jsx(Vl,{ref:r,sideOffset:t,className:me("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));nc.displayName=Vl.displayName;const rc=y.forwardRef(({className:e,inset:t,...n},r)=>p.jsx(Hl,{ref:r,className:me("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",t&&"pl-8",e),...n}));rc.displayName=Hl.displayName;const Ly=y.forwardRef(({className:e,children:t,checked:n,...r},a)=>p.jsxs(Wl,{ref:a,className:me("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[p.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:p.jsx(Yl,{children:p.jsx(jc,{className:"h-4 w-4"})})}),t]}));Ly.displayName=Wl.displayName;const ac=y.forwardRef(({className:e,children:t,...n},r)=>p.jsxs(Kl,{ref:r,className:me("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[p.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:p.jsx(Yl,{children:p.jsx(Fc,{className:"h-2 w-2 fill-current"})})}),t]}));ac.displayName=Kl.displayName;const sc=y.forwardRef(({className:e,inset:t,...n},r)=>p.jsx(ql,{ref:r,className:me("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));sc.displayName=ql.displayName;const Qr=y.forwardRef(({className:e,...t},n)=>p.jsx(Jl,{ref:n,className:me("-mx-1 my-1 h-px bg-muted",e),...t}));Qr.displayName=Jl.displayName;const By=({context:e,visible:t,model:n,apikey:r,heandelModel:a,selectedModel:s})=>{var de;const[o,i]=ie.useState(""),[l,c]=ie.useState([]),[u,d]=ie.useState([]),[g,v]=ie.useState(!1),f=y.useRef(null),m=y.useRef(null),[h,b]=ie.useState(0),[_,w]=ie.useState(0),[A,O]=ie.useState(!1),{fetchChatHistory:N,saveChatHistory:F}=Ps(),I=(()=>{const E=window.location.href,C=/\/problems\/([^/]+)/.exec(E);return C?C[1]:"Unknown Problem"})(),$=y.useRef(null);y.useEffect(()=>{m.current&&!A&&m.current.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{var E;(E=$.current)==null||E.focus()},0)},[l,g,t]);const G=async()=>{const{clearChatHistory:E}=Ps();await E(I),c([]),d([])},J=async()=>{var se,be;const E=new Mg;E.selectModal(n,r);let C="UNKNOWN";const L=document.querySelector("button.rounded.items-center.whitespace-nowrap.inline-flex.bg-transparent.dark\\:bg-dark-transparent.text-text-secondary.group");L&&L.textContent&&(C=L.textContent);const W=document.querySelectorAll(".view-line"),B=Wu(W),te=Hu.replace(/{{problem_statement}}/gi,e.problemStatement).replace(/{{programming_language}}/g,C).replace(/{{user_code}}/g,B),oe=Dg(l),{error:ae,success:pe}=await E.generate({prompt:`${o}`,systemPrompt:te,messages:oe,extractedCode:B});if(ae){const K={role:"assistant",content:ae.message};await F(I,[...u,{role:"user",content:o},K]),d(Q=>[...Q,K]),c(Q=>[...Q,K]),(se=m.current)==null||se.scrollIntoView({behavior:"smooth"})}if(pe){const K={role:"assistant",content:pe};await F(I,[...u,{role:"user",content:o},K]),d(Q=>[...Q,K]),c(Q=>[...Q,K]),i(""),(be=m.current)==null||be.scrollIntoView({behavior:"smooth"})}v(!1),setTimeout(()=>{var K;(K=$.current)==null||K.focus()},0)},he=async()=>{const{totalMessageCount:E,chatHistory:C,allChatHistory:L}=await N(I,dn,0);d(L||[]),w(E),c(C),b(dn)};y.useEffect(()=>{he()},[I]);const X=async()=>{if(_<h)return;O(!0);const{chatHistory:E}=await N(I,dn,h);E.length>0&&(c(C=>[...E,...C]),b(C=>C+dn)),setTimeout(()=>{O(!1)},500)},ue=E=>{E.currentTarget.scrollTop===0&&(console.log("Reached the top, loading more messages..."),X())},le=async E=>{var L;v(!0);const C={role:"user",content:E};d(W=>[...W,C]),c([...l,C]),(L=m.current)==null||L.scrollIntoView({behavior:"smooth"}),J()};return t?p.jsxs(la,{className:"mb-2 ",children:[p.jsxs("div",{className:"flex gap-2 items-center justify-between h-20 rounded-t-lg p-4",children:[p.jsxs("div",{className:"flex gap-2 items-center justify-start",children:[p.jsx("div",{className:"bg-white rounded-full p-2",children:p.jsx(cr,{color:"#000",className:"h-6 w-6"})}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-bold text-lg",children:"Need Help?"}),p.jsx("h6",{className:"font-normal text-xs",children:"Always online"})]})]}),p.jsxs(Oy,{children:[p.jsx(jy,{asChild:!0,children:p.jsx(Tt,{variant:"tertiary",size:"icon",children:p.jsx(Lc,{size:18})})}),p.jsxs(nc,{className:"w-56",children:[p.jsxs(sc,{className:"flex items-center",children:[p.jsx(zc,{size:16,strokeWidth:1.5,className:"mr-2"})," ",(de=ft.find(E=>E.name===s))==null?void 0:de.display]}),p.jsx(Qr,{}),p.jsx(My,{children:p.jsxs(Fy,{children:[p.jsxs(ec,{children:[p.jsx(cr,{size:16,strokeWidth:1.5})," Change Model"]}),p.jsx(Dy,{children:p.jsx(tc,{children:p.jsx($y,{value:s,onValueChange:E=>a(E),children:ft.map(E=>p.jsx(ac,{value:E.name,children:E.display},E.name))})})})]})}),p.jsx(Qr,{}),p.jsxs(rc,{onClick:G,onMouseEnter:E=>E.currentTarget.style.backgroundColor="rgb(185 28 28 / 0.35)",onMouseLeave:E=>E.currentTarget.style.backgroundColor="",children:[p.jsx(Bc,{size:14,strokeWidth:1.5})," Clear Chat"]})]})]})]}),p.jsx(ca,{className:"p-2",children:l.length>0?p.jsxs(qi,{className:"space-y-4 h-[500px] w-[400px] p-2",ref:f,onScroll:ue,children:[_>h&&p.jsx("div",{className:"flex w-full items-center justify-center",children:p.jsx(Tt,{className:"text-sm p-1 m-x-auto bg-transpernent text-white hover:bg-transpernent",onClick:X,children:"Load Previous Messages"})}),l.map((E,C)=>{var L,W,B,te,oe,ae,pe;return p.jsx("div",{className:me("flex w-max max-w-[75%] flex-col gap-2 px-3 py-2 text-sm my-4",E.role==="user"?"ml-auto bg-primary text-primary-foreground rounded-bl-lg rounded-tl-lg rounded-tr-lg ":"bg-muted rounded-br-lg rounded-tl-lg rounded-tr-lg"),children:p.jsxs(p.Fragment,{children:[p.jsx("p",{className:"max-w-80",children:typeof E.content=="string"?E.content:E.content.feedback}),typeof E.content!="string"&&p.jsxs(hd,{type:"multiple",children:[((L=E.content)==null?void 0:L.hints)&&E.content.hints.length>0&&p.jsxs(dr,{value:"item-1",className:"max-w-80",children:[p.jsx(pr,{children:"Hints 👀"}),p.jsx(fr,{children:p.jsx("ul",{className:"space-y-4",children:(B=(W=E.content)==null?void 0:W.hints)==null?void 0:B.map(se=>p.jsx("li",{children:se},se))})})]}),((te=E.content)==null?void 0:te.snippet)&&p.jsxs(dr,{value:"item-2",className:"max-w-80",children:[p.jsx(pr,{children:"Code 🧑🏻‍💻"}),p.jsx(fr,{children:p.jsx("div",{className:"mt-4 rounded-md",children:p.jsxs("div",{className:"relative",children:[p.jsx($c,{onClick:()=>{var se;typeof E.content!="string"&&navigator.clipboard.writeText(`${(se=E.content)==null?void 0:se.snippet}`)},className:"absolute right-2 top-2 h-4 w-4"}),p.jsx(qu,{theme:zs.dracula,code:((oe=E.content)==null?void 0:oe.snippet)||"",language:((pe=(ae=E.content)==null?void 0:ae.programmingLanguage)==null?void 0:pe.toLowerCase())||"javascript",children:({className:se,style:be,tokens:K,getLineProps:Q,getTokenProps:Ie})=>p.jsx("pre",{style:be,className:me(se,"p-3 rounded-md"),children:K.map((St,ke)=>p.jsx("div",{...Q({line:St}),children:St.map((ct,He)=>p.jsx("span",{...Ie({token:ct})},He))},ke))})})]})})})]})]})]})},C)}),g&&p.jsx("div",{className:"flex w-max max-w-[75%] flex-col my-2",children:p.jsx("div",{className:"w-5 h-5 rounded-full animate-pulse bg-primary"})}),p.jsx("div",{ref:m})]}):p.jsx("div",{children:p.jsx("p",{className:"flex items-center justify-center h-[510px] w-[400px] text-center space-y-4",children:"No messages yet."})})}),p.jsx(lo,{children:p.jsxs("form",{onSubmit:E=>{E.preventDefault(),o.trim().length!==0&&(le(o),i(""))},className:"flex w-full items-center space-x-2",children:[p.jsx(Ic,{id:"message",placeholder:"Type your message...",className:"flex-1",autoComplete:"off",value:o,onChange:E=>i(E.target.value),disabled:g,required:!0,ref:$}),p.jsxs(Tt,{type:"submit",className:"bg-[#fafafa] rounded-lg text-black",size:"icon",disabled:o.length===0,children:[p.jsx(Uc,{className:"h-4 w-4"}),p.jsx("span",{className:"sr-only",children:"Send"})]})]})})]}):p.jsx(p.Fragment,{})},Uy=()=>{const[e,t]=ie.useState(!1),n=document.querySelector("meta[name=description]"),r=n==null?void 0:n.getAttribute("content"),[a,s]=ie.useState(null),[o,i]=ie.useState(null),[l,c]=ie.useState(),u=y.useRef(null),d=v=>{u.current&&v.target instanceof Node&&u.current.contains(v.target)};ie.useEffect(()=>(document.addEventListener("click",d),()=>{document.removeEventListener("click",d)}),[]),(async()=>{const{getKeyModel:v,selectModel:f}=Kn(),{model:m,apiKey:h}=await v(await f());s(m),i(h)})();const g=v=>{if(v){const{setSelectModel:f}=Kn();f(v),c(v)}};return ie.useEffect(()=>{(async()=>{if(!chrome)return;const{selectModel:f}=Kn();c(await f())})()},[]),p.jsxs("div",{ref:u,className:"dark z-50",style:{position:"fixed",bottom:"30px",right:"30px"},children:[!a||!o?e?p.jsx(p.Fragment,{children:p.jsx(la,{className:"mb-5",children:p.jsx(ca,{className:"h-[500px] grid place-items-center",children:p.jsxs("div",{className:"grid place-items-center gap-4",children:[!l&&p.jsxs(p.Fragment,{children:[p.jsx("p",{className:"text-center",children:"Please configure the extension before using this feature."}),p.jsx(Tt,{onClick:()=>{chrome.runtime.sendMessage({action:"openPopup"})},children:"configure"})]}),l&&p.jsxs(p.Fragment,{children:[p.jsxs("p",{children:["We couldn't find any API key for selected model"," ",p.jsx("b",{children:p.jsx("u",{children:l})})]}),p.jsx("p",{children:"you can select another models"}),p.jsxs(wc,{onValueChange:v=>g(v),value:l,children:[p.jsx(xc,{className:"w-56",children:p.jsx(kc,{placeholder:"Select a model"})}),p.jsx(Sc,{children:p.jsxs(Ec,{children:[p.jsx(Ac,{children:"Model"}),p.jsx(Cc,{}),ft.map(v=>p.jsx(Tc,{value:v.name,children:v.display},v.name))]})})]})]})]})})})}):null:p.jsx(By,{visible:e,context:{problemStatement:r},model:a,apikey:o,heandelModel:g,selectedModel:l}),p.jsx("div",{className:"flex justify-end",children:p.jsx(Tt,{size:"icon",onClick:()=>t(!e),children:p.jsx(cr,{})})})]})},La=document.createElement("div");La.id="__leetcode_ai_whisper_container";document.body.append(La);Rc(La).render(p.jsx(y.StrictMode,{children:p.jsx(Uy,{})}));
