@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\openai@4.104.0_zod@3.25.76\node_modules\openai\bin\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\openai@4.104.0_zod@3.25.76\node_modules\openai\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\openai@4.104.0_zod@3.25.76\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\openai@4.104.0_zod@3.25.76\node_modules\openai\bin\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\openai@4.104.0_zod@3.25.76\node_modules\openai\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\openai@4.104.0_zod@3.25.76\node_modules;C:\Users\<USER>\Desktop\DSA extension\leetcode-whisper-chrome-extension\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\openai\bin\cli" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\openai\bin\cli" %*
)
