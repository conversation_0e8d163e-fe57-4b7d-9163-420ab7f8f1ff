name: Test and Build

on:
  pull_request:
    branches:
      - main

jobs:
  test-and-build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22.11.0'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9.12.3

      - name: Cache dependencies
        uses: actions/cache@v3
        id: cache
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('package.json') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: pnpm install

      - name: Build
        run: pnpm run build
