import{j as e,O as P,R as C,r as o,S as V,E as R,G as _,H as B,I as D,V as q,J as z,K as F,L as H,Q as J,B as G,z as L,N as Q}from"./select-DdeiFjEC.js";(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))f(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const n of a.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&f(n)}).observe(document,{childList:!0,subtree:!0});function l(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function f(s){if(s.ep)return;s.ep=!0;const a=l(s);fetch(s.href,a)}})();const W="/assets/leetcode-D0VPESVK.png",U=({show:t,children:c})=>t?c:e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsx(P,{})}),Y=()=>{const[t,c]=C.useState(null),[l,f]=C.useState(null),[s,a]=C.useState(!1),[n,p]=o.useState(!1),[u,y]=o.useState(null),[v,m]=o.useState(),x=async r=>{r.preventDefault();try{p(!0);const{setKeyModel:i}=L();t&&l&&await i(t,l),y({state:"success",message:"API Key saved successfully"})}catch(i){y({state:"error",message:i.message})}finally{p(!1)}};C.useEffect(()=>{(async()=>{if(!chrome)return;const{selectModel:i,getKeyModel:S}=L();f(await i()),m(await i()),c((await S(await i())).apiKey),a(!0)})()},[]);const j=async r=>{if(r){const{setSelectModel:i,getKeyModel:S,selectModel:g}=L();i(r),f(r),m(r),c((await S(await g())).apiKey)}};return e.jsx("div",{className:"relative p-4 w-[350px] bg-background",children:e.jsx(U,{show:s,children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"w-full  h-20 overflow-hidden ",children:e.jsx("img",{className:"mx-auto h-20 w-auto",src:W,width:150,height:150})}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:" font-bold text-3xl text-white",children:["LeetCode ",e.jsx("span",{className:"text-whisperOrange",children:"Whisper"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Your Companion to Beat LeetCode!"})]}),e.jsxs("form",{onSubmit:r=>x(r),className:"mt-10 flex flex-col gap-2 w-full",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"text",className:"text-xs text-muted-foreground",children:"select a model"}),e.jsxs(V,{onValueChange:r=>j(r),value:v,children:[e.jsx(R,{className:"w-full",children:e.jsx(_,{placeholder:"Select a model"})}),e.jsx(B,{children:e.jsxs(D,{children:[e.jsx(z,{children:"Model"}),e.jsx(F,{}),q.map(r=>e.jsx(H,{value:r.name,children:r.display},r.name))]})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{htmlFor:"text",className:"text-xs text-muted-foreground",children:["API Key ",l?`for ${l}`:""]}),e.jsx(J,{value:t||"",onChange:r=>c(r.target.value),placeholder:"Enter OpenAI API Key",disabled:!l,required:!0})]}),e.jsx(G,{disabled:n,type:"submit",className:"w-full mt-2",children:"save API Key"})]}),u?e.jsx("div",{className:"mt-2 text-center text-sm text-muted-foreground flex items-center justify-center p-2 rounded-sm",style:{color:u.state==="error"?"red":"green",border:u.state==="error"?"1px solid red":"1px solid green",backgroundColor:u.state==="error"?"rgba(255, 0, 0, 0.1)":"rgba(0, 255, 0, 0.1)"},children:u.state==="error"?e.jsx("p",{className:"text-red-500",children:u.message}):e.jsx("p",{className:"text-green-500",children:u.message})}):"",e.jsx("div",{className:"mt-7 flex items-center justify-center",children:e.jsxs("p",{className:"text-sm",children:["Want more features? ",e.jsx("a",{href:"https://github.com/piyushgarg-dev/leetcode-whisper-chrome-extension/issues/new",className:"text-blue-500 hover:underline",target:"_blank",children:"Request a feature!"})]})})]})})})};var E=["light","dark"],M="(prefers-color-scheme: dark)",X=typeof window>"u",O=o.createContext(void 0),Z=t=>o.useContext(O)?t.children:o.createElement(te,{...t}),ee=["light","dark"],te=({forcedTheme:t,disableTransitionOnChange:c=!1,enableSystem:l=!0,enableColorScheme:f=!0,storageKey:s="theme",themes:a=ee,defaultTheme:n=l?"system":"light",attribute:p="data-theme",value:u,children:y,nonce:v})=>{let[m,x]=o.useState(()=>T(s,n)),[j,r]=o.useState(()=>T(s)),i=u?Object.values(u):a,S=o.useCallback(d=>{let h=d;if(!h)return;d==="system"&&l&&(h=K());let $=u?u[h]:h,I=c?re():null,N=document.documentElement;if(p==="class"?(N.classList.remove(...i),$&&N.classList.add($)):$?N.setAttribute(p,$):N.removeAttribute(p),f){let k=E.includes(n)?n:null,A=E.includes(h)?h:k;N.style.colorScheme=A}I==null||I()},[]),g=o.useCallback(d=>{let h=typeof d=="function"?d(d):d;x(h);try{localStorage.setItem(s,h)}catch{}},[t]),b=o.useCallback(d=>{let h=K(d);r(h),m==="system"&&l&&!t&&S("system")},[m,t]);o.useEffect(()=>{let d=window.matchMedia(M);return d.addListener(b),b(d),()=>d.removeListener(b)},[b]),o.useEffect(()=>{let d=h=>{if(h.key!==s)return;let $=h.newValue||n;g($)};return window.addEventListener("storage",d),()=>window.removeEventListener("storage",d)},[g]),o.useEffect(()=>{S(t??m)},[t,m]);let w=o.useMemo(()=>({theme:m,setTheme:g,forcedTheme:t,resolvedTheme:m==="system"?j:m,themes:l?[...a,"system"]:a,systemTheme:l?j:void 0}),[m,g,t,j,l,a]);return o.createElement(O.Provider,{value:w},o.createElement(se,{forcedTheme:t,disableTransitionOnChange:c,enableSystem:l,enableColorScheme:f,storageKey:s,themes:a,defaultTheme:n,attribute:p,value:u,children:y,attrs:i,nonce:v}),y)},se=o.memo(({forcedTheme:t,storageKey:c,attribute:l,enableSystem:f,enableColorScheme:s,defaultTheme:a,value:n,attrs:p,nonce:u})=>{let y=a==="system",v=l==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${p.map(r=>`'${r}'`).join(",")})`};`:`var d=document.documentElement,n='${l}',s='setAttribute';`,m=s?E.includes(a)&&a?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${a}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",x=(r,i=!1,S=!0)=>{let g=n?n[r]:r,b=i?r+"|| ''":`'${g}'`,w="";return s&&S&&!i&&E.includes(r)&&(w+=`d.style.colorScheme = '${r}';`),l==="class"?i||g?w+=`c.add(${b})`:w+="null":g&&(w+=`d[s](n,${b})`),w},j=t?`!function(){${v}${x(t)}}()`:f?`!function(){try{${v}var e=localStorage.getItem('${c}');if('system'===e||(!e&&${y})){var t='${M}',m=window.matchMedia(t);if(m.media!==t||m.matches){${x("dark")}}else{${x("light")}}}else if(e){${n?`var x=${JSON.stringify(n)};`:""}${x(n?"x[e]":"e",!0)}}${y?"":"else{"+x(a,!1,!1)+"}"}${m}}catch(e){}}()`:`!function(){try{${v}var e=localStorage.getItem('${c}');if(e){${n?`var x=${JSON.stringify(n)};`:""}${x(n?"x[e]":"e",!0)}}else{${x(a,!1,!1)};}${m}}catch(t){}}();`;return o.createElement("script",{nonce:u,dangerouslySetInnerHTML:{__html:j}})}),T=(t,c)=>{if(X)return;let l;try{l=localStorage.getItem(t)||void 0}catch{}return l||c},re=()=>{let t=document.createElement("style");return t.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},K=t=>(t||(t=window.matchMedia(M)),t.matches?"dark":"light");const ae=({children:t,attribute:c="class",defaultTheme:l="dark",enableSystem:f=!0,disableTransitionOnChange:s=!0})=>e.jsx(Z,{attribute:c,defaultTheme:l,enableSystem:f,disableTransitionOnChange:s,children:e.jsx("div",{children:t})});Q(document.getElementById("root")).render(e.jsx(o.StrictMode,{children:e.jsx(ae,{children:e.jsx(Y,{})})}));
