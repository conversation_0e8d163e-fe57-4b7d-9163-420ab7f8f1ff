{"name": "chrome-extension", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prettier": "prettier . --write"}, "dependencies": {"@ai-sdk/google": "^0.0.55", "@ai-sdk/openai": "^0.0.72", "@crxjs/vite-plugin": "^2.0.0-beta.28", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "ai": "^3.4.33", "autoprefixer": "^10.4.20", "chrome-ai": "^1.11.1", "chrome-types": "^0.1.319", "class-variance-authority": "^0.7.0", "framer-motion": "^11.11.17", "idb": "^8.0.0", "lucide-react": "^0.456.0", "next-themes": "^0.3.0", "openai": "^4.72.0", "prism-react-renderer": "^2.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "prettier": "^3.3.3", "vite": "^5.4.10"}}